import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';

import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/home/<USER>/widgets/quick_wallet/quick_wallet_recipients_bottom_sheet.dart';
import 'package:cbrs/features/transactions/presentation/views/quick%20wallet%20transfer/quick_pay_recipent_card.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/authorization_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/member_lookup_response.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/transfer_to_wallet/domain/entities/recent_wallet_transfer_hive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class WalletTransferAddAmount extends StatefulWidget {
  const WalletTransferAddAmount({
    required this.dashenExchangeAmount,
    required this.currencyType,
    this.memberInfo,
    this.recipent,
    this.isFromQuick = true,
    this.ignoreAmountCheck = false,
    this.isFromChat = false,
    super.key,
  });
  final MemberLookupResponse? memberInfo;
  final RecentRecipient? recipent;

  final CurrencyType currencyType;
  final double dashenExchangeAmount;
  final bool isFromQuick;
  final bool ignoreAmountCheck;
  final bool isFromChat;
  @override
  State<WalletTransferAddAmount> createState() =>
      _WalletTransferAddAmountState();
}

class _WalletTransferAddAmountState extends State<WalletTransferAddAmount> {
  late CurrencyInputController _currencyController;
  bool _isLoading = false;
  double walletBalance = 0;

  final TextEditingController _pinController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;

  @override
  void initState() {
    super.initState();

    // context.read<WalletTransferBloc>().add(
    //       GetWalletDetailsEvent(
    //         currency: widget.currencyType.name.toUpperCase(),
    //       ),
    //     );

    context.read<WalletBalanceBloc>().add(
          FetchWalletEvent(
            isUsdWallet: GlobalVariable.currentlySelectedWallet == 'USD',
            forceTheme: false,
          ),
        );

    final state = context.read<WalletBalanceBloc>().state;

    if (state is WalletLoadedState) {
      setState(() {
        walletBalance = state.isUsdWallet ? state.usdBalance : state.etbBalance;
      });
    }

    _currencyController = CurrencyInputController(
      currencyType: (GlobalVariable.currentlySelectedWallet ?? '') == 'USD'
          ? CurrencyType.usd
          : CurrencyType.etb,
      maxBalance: walletBalance,
      ignoreWalletAmountCheck: widget.ignoreAmountCheck,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      isFromChat: widget.isFromChat,
      transactionType: tx_type.TransactionType.walletTransfer,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        context.read<TransactionBloc>().add(
              ConfirmTransferEvent(
                pin: pin,
                billRefNo: (context.read<WalletTransferBloc>().state
                        as WalletTransferPinRequired)
                    .billRefNo,
                transactionType: tx_type.TransactionType.walletTransfer,
              ),
            );
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        if (!widget.isFromChat) {
          final recentTransaction = RecentWalletTransferHive(
            recipientId: response.transaction.beneficiaryId ?? '',
            recipientEmail: response.transaction.beneficiaryEmail ?? '',
            recipientPhone: response.transaction.beneficiaryPhone ?? '',
            recipientName: response.transaction.beneficiaryName ?? '',
            createdAt: DateTime.now(),
            avatar: '',
          );

          context.read<RecentWalletTransferBloc>().add(
                SaveRecentWalletTransferEvent(
                  transaction: recentTransaction,
                ),
              );
        }
        Navigator.pop(context);
    
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    try {
      final amount = _currencyController.numericAmount;

      // Validate amount is greater than 0
      if (amount <= 0) {
        CustomToastification(
          context,
          message: 'Amount must be greater than 0',
        );
        return;
      }
      setState(() {
        _isLoading = true;
      });

      context.read<WalletTransferBloc>().add(
            CheckWalletTransferRulesRequested(
              amount: amount,
              currency: GlobalVariable.currentlySelectedWallet ?? '',
            ),
          );
    } catch (e) {
      CustomToastification(
        context,
        message: 'Invalid Amount',
      );
    }
  }

  void _showConfirmScreenBottomSheet(WalletTransferResponse response) {
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Wallet to Wallet Transfer',
        'Recipient Name': widget.isFromQuick
            ? widget.recipent?.name
            : widget.memberInfo?.fullName,
        'Recipient Email': widget.isFromQuick
            ? widget.recipent?.email
            : widget.memberInfo?.email,
        'Recipient Phone': widget.isFromQuick
            ? widget.recipent?.phone
            : widget.memberInfo?.phoneNumber,
        'Amount':
            '${response.data.billAmount} ${response.data.originalCurrency}',
        'Service Charge':
            '${response.data.serviceCharge} ${response.data.originalCurrency}',

        'VAT': '${response.data.vat} ${response.data.originalCurrency}',

        'Date': AppMapper.safeFormattedDate(response.data.createdAt),
        // 'BillRef No': response.data.billRefNo,
      },
      status: response.data.status,
      originalCurrency: response.data.originalCurrency,
      totalAmount: response.data.totalAmount,
      billAmount: response.data.billAmount,
      requiresOtp:
          WalletTransferAuthResponse.fromJson(response.toJson()).requiresOtp,
      billRefNo: response.data.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
        totalAmount: transaction.totalAmount,
        billAmount: transaction.billAmount,
        originalCurrency: transaction.originalCurrency,
      transactionId: transaction.id,
      billRefNo: transaction.billRefNo,

        status: 'Paid',
        title: 'Your wallet to wallet transfer was completed successfully',
        {
          'Transaction Type': 'Wallet to Wallet Transfer',

          'Recipient Name': transaction.beneficiaryName ??
              (
                widget.isFromQuick
                    ? widget.recipent?.name
                    : widget.memberInfo?.fullName,
              ),

          'Recipient Email': transaction.beneficiaryEmail,
          'Recipeint PhoneNumber': transaction.beneficiaryPhone,
          if (transaction.originalCurrency == 'USD')
            'Amount In USD': '${transaction.billAmount} USD',
          if (transaction.originalCurrency == 'USD' &&
              transaction.amountInEtb != null)
            'Amount In ETB': '${transaction.paidAmount} ETB'
          else
            'Amount In ETB': '${transaction.billAmount} ETB',

          //widget.memberInfo.phoneNumber,

          'Service Charge':
              '${transaction.serviceCharge} ${transaction.originalCurrency}',
          'VAT': '${transaction.vat} ${transaction.originalCurrency}',

          'Date': AppMapper.safeFormattedDate(transaction.createdAt),
          'BillRefNo': transaction.billRefNo,
        });
  }

  @override
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transfer To Wallet'),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<TransactionBloc, TransactionState>(
            listenWhen: (previous, current) =>
                current is ConfirmTransferSuccess ||
                current is ConfirmTransferError,
            listener: (context, state) {
              if (state is ConfirmTransferError) {
                CustomToastification(context, message: state.message);
              }
            },
          ),
          BlocListener<WalletTransferBloc, WalletTransferState>(
            listenWhen: (previous, current) =>
                current is CheckingWalletTransferRules ||
                current is WalletTransferRulesChecked ||
                current is WalletTransferLoading ||
                current is WalletTransferPinRequired ||
                current is WalletTransferFailure ||
                current is WalletDetailsLoaded ||
                current is WalletTransferError,
            listener: (context, state) {
              if (state is CheckingWalletTransferRules ||
                  state is WalletTransferLoading) {
                setState(() => _isLoading = true);
              } else if (state is WalletTransferRulesChecked) {
                setState(() => _isLoading = false);
                context.read<WalletTransferBloc>().add(
                      TransferToWalletEvent(
                        beneficiaryEmail: widget.isFromQuick
                            ? widget.recipent?.email
                            : widget.memberInfo?.email,
                        beneficiaryPhone: widget.isFromQuick
                            ? widget.recipent?.phone
                            : widget.memberInfo?.phoneNumber,
                        beneficiaryId: widget.isFromQuick
                            ? widget.recipent?.id
                            : widget.memberInfo?.id,
                        amount: _currencyController.numericAmount,
                        currency:
                            GlobalVariable.currentlySelectedWallet ?? '',
                      ),
                    );
              } else if (state is WalletTransferPinRequired) {
                setState(() => _isLoading = false);
                _showConfirmScreenBottomSheet(state.response);
              } else if (state is WalletTransferFailure ||
                  state is WalletTransferError) {
                setState(() => _isLoading = false);
                CustomToastification(
                  context,
                  message: state is WalletTransferFailure
                      ? state.message
                      : (state as WalletTransferError).message,
                );
              }
             
            },
          ),
        ],
        child: BlocListener<WalletBalanceBloc, HomeState>(
          listener: (context, state) {
            // CustomToastification(context, message: 'tttkkekkeke $state');
      
            if (state is WalletLoadedState) {
              setState(() {
                walletBalance =
                    state.isUsdWallet ? state.usdBalance : state.etbBalance;
                _currencyController = CurrencyInputController(
                  currencyType: CurrencyType.usd,
                  maxBalance: walletBalance,
                );
              });
            }
          },
          child: BlocBuilder<WalletTransferBloc, WalletTransferState>(
            buildWhen: (previous, current) =>
                current is WalletTransferInitial ||
                current is WalletTransferLoading ||
                current is WalletDetailsLoaded,
            builder: (context, state) {
              if (state is WalletDetailsLoadingState) {
                return const CustomConnectLoader();
              }
      
              return CurrencyInputWidget(
                controller: _currencyController,
                title: 'Add Amount',
                header: !widget.isFromQuick
                    ? null
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 2),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: Row(
                              mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Add Amount',
                                  style: GoogleFonts.outfit(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // SizedBox(height: 0.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: Row(
                              mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Enter the amount you wish to send to the recipient and submit.',
                                  style: GoogleFonts.outfit(
                                    fontSize: 12.sp,
                                    color: const Color(0xFFAAAAAA),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // const SizedBox(height: 2),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 16),
                            child: QuickPayRecipentCard(
                              recipientName:
                                  widget.recipent?.name ?? 'rober insarmu',
                              recipientPhone: widget.recipent?.phone ?? '',
                              recipientAvatar:
                                  widget.recipent?.avatarUrl ?? '',
                              recipientEmail: widget.recipent?.email ?? '',
                            ),
                          ),
                        ],
                      ),
                transactionType: 'wallet_transfer',
                subtitle:
                    'Enter the amount you wish to send to recipient and submit.',
                balanceLabel: CurrencyFormatter.formatWalletBalance(
                  walletBalance,
                  GlobalVariable.currentlySelectedWallet,
                ),
                hasCustomWalletDisplay: true,
                showBalanceSwitching: true,
                showExchangeAmount: true,
                exchangeAmount: widget.dashenExchangeAmount.toString(),
                resetBalance: () {
                  _currencyController = CurrencyInputController(
                    currencyType:
                        (GlobalVariable.currentlySelectedWallet) == 'USD'
                            ? CurrencyType.usd
                            : CurrencyType.etb,
                    maxBalance: walletBalance,
                  );
                  setState(() {});
                },
                onContinue: _onContinuePressed,
                isLoading: _isLoading,
              );
            },
          ),
        ),
      ),
    );
  }
}
