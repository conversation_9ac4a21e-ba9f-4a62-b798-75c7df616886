import 'dart:convert';

import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/constants/mini_apps_urls.dart';
import 'package:cbrs/core/utils/app_mapper.dart' show AppMapper;
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/utility/application/bloc/utility_bloc.dart';
import 'package:cbrs/features/utility/domain/entities/create_order_utility_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

class UtilityWebview extends StatefulWidget {
  const UtilityWebview({
    required this.url,
    required this.appName,
    super.key,
  });
  final String url;
  final String appName;

  @override
  State<UtilityWebview> createState() => _UtilityWebviewState();
}

class _UtilityWebviewState extends State<UtilityWebview> {
  late final WebViewController _controller;
  bool _isLoading = true;
  bool _hasError = false;
  int _retryCount = 0;
  static const int _maxRetries = 3;
  final TextEditingController _pinController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;

  String billRefNo = '';
  @override
  void initState() {
    super.initState();
    _initWebView();

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.utility,
      pinController: _pinController,
      onPinSubmitted: _handleSubmitPin,
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  void _handleSubmitPin(String pin) {
    debugPrint('Pin📌 😒 $pin biil $billRefNo');
    context.read<TransactionBloc>().add(
          ConfirmTransferEvent(
            pin: pin,
            billRefNo: billRefNo,
            transactionType: tx_type.TransactionType.utility,
          ),
        );
  }

  void _initWebView() {
    if (widget.url.isEmpty) {
      setState(() => _hasError = true);
      return;
    }

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..enableZoom(true)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
            debugPrint('page startttted.');
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
            }
          },
          onPageFinished: (url) {
            debugPrint('page finishedddd');
            if (mounted) {
              setState(() => _isLoading = false);
            }
          },
          onWebResourceError: (error) {
            debugPrint('Web resource error: ${error.description}');
            if (mounted) {
              setState(() {
                _hasError = true;
                _retryCount++;
              });
            }
          },
          onNavigationRequest: (request) {
            if (MiniAppsConfig.isAllowedDomain(request.url)) {}
            launchUrl(
              Uri.parse(request.url),
              mode: LaunchMode.externalNonBrowserApplication,
            );
            return NavigationDecision.navigate;

            return NavigationDecision.prevent;
          },
        ),
      )
      ..addJavaScriptChannel(
        'MiniAppChannel',
        onMessageReceived: (JavaScriptMessage message) async {
          try {
            final payload =
                json.decode(message.message) as Map<String, dynamic>;

            final action = payload['functionName'] as String;

            final data = payload['params'] as Map<String, dynamic>;

            debugPrint(' respose from web $data');
            switch (action) {
              case 'initiatePayment':
                await _handlePaymentInitiation(payload['params']);
                break;
              case 'NAVIGATION':
                _handleNavigation(data);
                break;

              case 'ERROR':
                _handleError(data);
                break;

              case 'PREPARE_ORDER_PAYLOAD':
                _handlePrepareOrderPayload(data);
                break;

              default:
                debugPrint('Unknown action: $action');
            }
          } catch (e) {
            debugPrint('Error processing message: $e');
            _sendErrorToWebApp('Failed to process request: $e');
          } finally {
            debugPrint('Take me plead');
          }
        },
      );
    debugPrint('hello dd');

    _loadWebView();
  }

  void _loadWebView() {
    try {
      _controller.loadRequest(
        Uri.parse(widget.url),
        headers: {
          'Content-Security-Policy': "frame-ancestors 'self'",
          'X-Frame-Options': 'SAMEORIGIN',
          'X-Content-Type-Options': 'nosniff',
          'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        },
      );
    } catch (e) {
      debugPrint('Error loading WebView: $e');
      if (mounted) {
        setState(() => _hasError = true);
      }
    }
  }

  Future<void> _handlePaymentInitiation(
    dynamic payLoad,
    //  Map<String, dynamic> payload,
  ) async {
    try {
      debugPrint('_handlePaymentInitiation');

      context.read<UtilityBloc>().add(
            CreatingOrderUtilityEvent(
              data: payLoad,
            ),
          );
    } catch (e) {
      // Send stringified error back to web
      _controller.runJavaScript('''
        if (typeof window.handleDataCallback === 'function') {
          window.handleDataCallback({
            success: false,
            error: ${json.encode(e.toString())}
          });
        }
      ''');
      _handleError({'message': 'Payment initiation failed: $e'});
    } finally {}
  }

  void _handleNavigation(Map<String, dynamic> data) {
    final url = data['url']?.toString() ?? '';
    if (url.isNotEmpty) {
      launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  void _handleError(Map<String, dynamic> data) {
    final message = data['message']?.toString() ?? 'An error occurred';
    if (mounted && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: GoogleFonts.outfit(color: Colors.white),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _sendErrorToWebApp(String errorMessage) {
    _controller.runJavaScript('''
      if (typeof window.handleDataCallback === 'function') {
        window.handleDataCallback({
          action: 'ERROR',
          data: {
            message: "ggg${errorMessage.replaceAll('"', r'\"')}"
          }
        });
      }
    ''');
  }

  void _handlePrepareOrderPayload(Map<String, dynamic> data) {
    try {
      final headers = data['headers'] as Map<String, dynamic>?;

      // Ensure data is properly stringified
      final requestBody = {
        'dataToken': data['dataToken'],
        'serviceType': data['serviceType'] ?? 'traffic_authority',
        'stage': data['stage'] ?? 'UAT',
      };

      final requestHeaders = {
        'Content-Type': 'application/json',
        'deviceuuid': headers?['deviceuuid'] ?? 'test-device-123',
        'sourceapp': headers?['sourceapp'] ?? 'memberapp',
        'x-transfer-flag': headers?['x_transfer_flag'] ?? 'true',
      };

      _controller.runJavaScript('''
        try {
          fetch("${data['url']}", {
            method: "${data['method'] ?? 'POST'}",
            headers: ${json.encode(requestHeaders)},
            body: ${json.encode(json.encode(requestBody))} // Double encode to ensure proper stringification
          })
          .then(response => response.text()) // Get response as text first
          .then(textResponse => {
            try {
              const jsonResponse = JSON.parse(textResponse);
              const aAccessToken = localStorage.getItem("sessionUpdated");
              
              // Prepare the response payload
              const responsePayload = {
                functionName: "initiatePayment",
                params: {
                  orderPayload: jsonResponse,
                  "x-api-key": "74a93bc1d226ad223c15febf8b4b2c5864764b4468312fe9",
                  datatoken: "${data['dataToken']}",
                  "a-access-token": aAccessToken,
                  callbackName: "handleDataCallback",
                  deviceuuid: "${requestHeaders['deviceuuid']}",
                  sourceapp: "${requestHeaders['sourceapp']}",
                  x_transfer_flag: "${requestHeaders['x-transfer-flag']}"
                }
              };

              // Send the stringified response back to Flutter
              if (typeof window.handleDataCallback === 'function') {
                window.handleDataCallback({
                  success: true,
                  data: JSON.stringify(responsePayload)
                });
              }
            } catch (parseError) {
              throw new Error('Failed to parse response: ' + parseError.message);
            }
          })
          .catch(error => {
            console.error('Error:', error);
            if (typeof window.handleDataCallback === 'function') {
              window.handleDataCallback({
                success: false,
                error: JSON.stringify(error.toString())
              });
            }
          });
        } catch (error) {
          console.error('Error in prepare order payload:', error);
          if (typeof window.handleDataCallback === 'function') {
            window.handleDataCallback({
              success: false,
              error: JSON.stringify(error.toString())
            });
          }
        }
      ''');
    } catch (e) {
      _handleError(
        {'message': 'Failed to prepare order payload: $e'},
      );
    }
  }

  void _showConfirmScreenBottomSheet(CreateOrderUtilityDataEntity response) {
    final requiresOtp = response.authorizationType == 'PIN_AND_OTP';

    debugPrint(
      'response.originalCurrency: ${response.originalCurrency} bill amount ${response.billAmount}',
    );
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': '${response.merchantType}',
        'Recipient Name': response.beneficiaryName,
        'Sender Name': response.senderName,
        'Bill Amount': '${response.billAmount}ETB',
        // 'totalAmount': response.totalAmount,
        'Service Charge': '${response.serviceCharge}ETB',
        'VAT': '${response.vat}ETB',
        // 'originalCurrency': response.originalCurrency,
        'Date': AppMapper.safeFormattedDate(response.createdAt),
        // response.billAmount,
        // 'Bill RefNo': response.billRefNo,
        // 'status': 'WAIT',
        //response.status,
      },
      totalAmount: response.totalAmount ?? 0,
      billAmount: response.billAmount ?? 0,
      originalCurrency: response.originalCurrency ?? '',
      status: response.status ?? '',
      confirmButtonText: 'Confirm Change',
      requiresOtp: requiresOtp,
      billRefNo: response.billRefNo ?? '',
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
        totalAmount: transaction.totalAmount ?? 0,
        billAmount: transaction.billAmount ?? 0,
        originalCurrency: transaction.originalCurrency,
        transactionId: transaction.id,
        billRefNo: transaction.billRefNo,
        status: 'Paid',
        title: 'Your Utility transfer was completed successfully',
        {
          'Transaction Type': '${transaction.merchantType}',
          'Sender Name': transaction.senderName,
          'Recipient Name': transaction.beneficiaryName,
          'Recipient Account': transaction.beneficiaryAccountNo,

          // 'totalAmount': transaction.totalAmount,
          // 'amountInUSD': transaction.paidAmount,
          // 'originalCurrency': transaction.originalCurrency,
          ' Amount': "${transaction.billAmount.toStringAsFixed(2) ?? ''}ETB",
          // 'exchangeRate': transaction.exchangeRate.toString(),
          'Service Charge': '${transaction.serviceCharge}ETB',
          'VAT': '${transaction.vat}ETB',
          'Date': AppMapper.safeFormattedDate(transaction.createdAt),
          'Bill RefNo': transaction.billRefNo,
          // 'status': transaction.status,
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        title: Text(
          widget.appName,
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () async {
            if (await _controller.canGoBack()) {
              await _controller.goBack();
            } else {
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            }
          },
        ),
      ),
      body: SafeArea(
        child: BlocConsumer<UtilityBloc, UtilityState>(
          listener: (context, state) {
            if (state is UtilityErrorState) {
              CustomToastification(context, message: state.message);
            }
            if (state is ConfirmedUtilityState) {
              debugPrint('state.utility ${state.utility}  ');
              setState(() {
                billRefNo = state.utility.billRefNo ?? '';
              });
              _showConfirmScreenBottomSheet(state.utility);

              // context.pushNamed(
              //   AppRouteName.utilityConfirmation,
              //   extra: {
              //     'confirm': state.utility,
              //   },
              // );
            }
          },
          builder: (context, state) {
            return Stack(
              children: [
                if (!_hasError) WebViewWidget(controller: _controller),
                if (_isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
                if (_hasError && _retryCount < _maxRetries)
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Failed to load ${widget.appName}',
                          style: GoogleFonts.outfit(
                            fontSize: 16.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 16.h),
                        ElevatedButton(
                          onPressed: _loadWebView,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF065234),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(100.r),
                            ),
                          ),
                          child: Text(
                            'Retry',
                            style: GoogleFonts.outfit(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
