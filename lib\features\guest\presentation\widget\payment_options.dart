import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/guest/presentation/widget/show_guest_mode_bottom_sheet.dart';
import 'package:cbrs/features/home/<USER>/widgets/home_section_headers.dart';
import 'package:cbrs/features/home/<USER>/widgets/home_action_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class PaymentOptions extends StatelessWidget {
  const PaymentOptions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: const HomeSectionHeaders(
            title: 'Payments',
            description: 'Make payments and manage your cash easily.',
          ),
        ),
        SizedBox(height: 16.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildPaymentOption(
                context: context,
                image: MediaRes.merchantPayment,
                label: 'Merchant\nPayment',
                onTap: () {
                  showGuestModeBottomSheet(context);
                },
              ),
              _buildPaymentOption(
                context: context,
                image: MediaRes.utilityPayment,
                label: 'Utility\nPayment',
                onTap: () {
                  showGuestModeBottomSheet(context);
                },
              ),
              _buildPaymentOption(
                context: context,
                image: MediaRes.moneyRequest,
                label: 'Money\nRequest',
                onTap: () {
                  showGuestModeBottomSheet(context);
                },
              ),
              _buildPaymentOption(
                context: context,
                image: MediaRes.cashOutIcon,
                label: 'Cash\nIn/Out',
                onTap: () {
                  showGuestModeBottomSheet(context);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentOption({
    required String image,
    required BuildContext context,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          HomeActionIcons(imageIcon: image),
          SizedBox(height: 6.h),
          Text(
            label,
            textAlign: TextAlign.center,
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}
