{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b803c8b048870b340aa594930bdf4579", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/TOCropViewController/TOCropViewController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/TOCropViewController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TOCropViewController/TOCropViewController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "TOCropViewController", "PRODUCT_NAME": "TOCropViewController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98605c35452afc21c9738f0c6828c71d26", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e5cb69071a46ffa9521a42bae22900d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/TOCropViewController/TOCropViewController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/TOCropViewController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TOCropViewController/TOCropViewController.modulemap", "PRODUCT_MODULE_NAME": "TOCropViewController", "PRODUCT_NAME": "TOCropViewController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c614f91f9b72052667099c32cbe6637", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e5cb69071a46ffa9521a42bae22900d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/TOCropViewController/TOCropViewController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/TOCropViewController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TOCropViewController/TOCropViewController.modulemap", "PRODUCT_MODULE_NAME": "TOCropViewController", "PRODUCT_NAME": "TOCropViewController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a86be8a3d3fc9de309d9529a5e706f38", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a3135a0ca83a194309aee4b10620f370", "guid": "bfdfe7dc352907fc980b868725387e980a4dfaa604a5c973bd4cb5802c9855ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1d1deb50cc385e706879cf2d570828a", "guid": "bfdfe7dc352907fc980b868725387e9863499551374c1c9ce8b5b4772d95cd02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c3ae4b30b8ed31a8cf3404500d6d2b", "guid": "bfdfe7dc352907fc980b868725387e98729b04462a09cdeae6d1711d1cca87d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee8bb2558e0c225ada5250af0d03da8", "guid": "bfdfe7dc352907fc980b868725387e98d7cc822e6ff7acf3dda3e6f4e7a05fe2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98563ce796387cbc02ff5e52ed1395ce2d", "guid": "bfdfe7dc352907fc980b868725387e9869aa9e264a1406d02e21ec0e12782b90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeeac5ad100a42b67a0ee3d8f9ac4275", "guid": "bfdfe7dc352907fc980b868725387e988c61a099ce5bda3b8c423bda9a71d3fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c606e1f18319dfa1b6e0e2580bda97f", "guid": "bfdfe7dc352907fc980b868725387e98969de66fdf5f5ba640fa0b4fd2b1983d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc051a9e8a194f1602ad565a5081ce7a", "guid": "bfdfe7dc352907fc980b868725387e9892aac1b38c1207e05c8972ff02b5577d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee25d1294472118ea79cc7071aac901e", "guid": "bfdfe7dc352907fc980b868725387e98a105d635d93b75cace7bbd27b86b8e73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cadddb0f869a83753e09c575d20a16e", "guid": "bfdfe7dc352907fc980b868725387e98fe2d3380e9a9e674f15a2ea4eea9bba1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869dfb0c05ef1eeb955a2a6ee79656a82", "guid": "bfdfe7dc352907fc980b868725387e98311b7b43caef2eb83b6f84d293bffd52", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989823e64e9e00d6500db27cd56bb64766", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bc473d1afa1bb25bc5ee69b6064314f4", "guid": "bfdfe7dc352907fc980b868725387e98f2854e2e6c4b0efdadd510b4c7df08e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f4c2cb50b53655747b8455e95f1c7aa", "guid": "bfdfe7dc352907fc980b868725387e98862f204c2217ec1fd0f6c2541cb1d7d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e19e1d92e795ff14f834121ae6870aa1", "guid": "bfdfe7dc352907fc980b868725387e98c4192ed237fd06694e27828b8f6eaa20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b36dad426157dacce7e9d71c3344f4ea", "guid": "bfdfe7dc352907fc980b868725387e98314f445b68467d09bc944f6977d6a1e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d09e3d643b63497d6c3e78f493b68f8", "guid": "bfdfe7dc352907fc980b868725387e9899e94270ee0a9fc2990fe6311d9e930c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddb071d882c5e07eec87b9c5905654fe", "guid": "bfdfe7dc352907fc980b868725387e9874865e6e3e9d40fd69956302ea6a16b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893d65f48ed3a3b8f63086b5e611fcbc3", "guid": "bfdfe7dc352907fc980b868725387e9830df155f1ccf9cf1ff06feda787776e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fb441b2229b7ec9fd3660f4b81ad61c", "guid": "bfdfe7dc352907fc980b868725387e98a9f944ecbfbd6276045c1acd677fdc87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a664453ef8ef324654e08f9d6e312305", "guid": "bfdfe7dc352907fc980b868725387e9834639a5bdb4f50681ddcc792d8385313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d1f9fe417e43214581e66775d81934d", "guid": "bfdfe7dc352907fc980b868725387e983da8a6c162fb34e1a67bb7ab1d4b8cc7"}], "guid": "bfdfe7dc352907fc980b868725387e98b973c71a5c8d9944a2711af778a2c3b6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e98269f2208e6af9ec57440cdb8e9a1fdb7"}], "guid": "bfdfe7dc352907fc980b868725387e988710db881c4cd7a5ab2e4ff5e3783e93", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981ebf3826f47dbb7fd582b4ad06af832b", "targetReference": "bfdfe7dc352907fc980b868725387e986dbfa2df59ddcae0f992dedaee8f3553"}], "guid": "bfdfe7dc352907fc980b868725387e98561889aae53f203fe8d20bd552b4212b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e986dbfa2df59ddcae0f992dedaee8f3553", "name": "TOCropViewController-TOCropViewControllerBundle"}], "guid": "bfdfe7dc352907fc980b868725387e987a4af56e2729cecfad7b13d62a9a5fa4", "name": "TOCropViewController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9874cf314b58ac20a75c1512ceb8885e00", "name": "TOCropViewController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}