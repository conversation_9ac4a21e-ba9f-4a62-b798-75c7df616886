class ApiConstants {
  ApiConstants._();

  // Base URLs testcbrs
  /// @description 'https://staging.eaglelionsystems.com/v1.0/cbrs-api';

  static const String baseUrl =
      'https://staging.eaglelionsystems.com/v1.0/cbrs-api';
  // 'https://testcbrs.eaglelionsystems.com/v1.0/cbrs-api';
  //  'https://cbrs.cashgoethiopia.com/v1.0/cbrs-api';

  // Resources URL
  static const String resourcesUrl =
      'https://staging.eaglelionsystems.com/_resources';

  // Car Loan Invoice URLs
  static String getCarLoanInvoiceUrl(String billRefNo, String loanReceipt) =>
      '$baseUrl/car-loans/invoice/$billRefNo/$loanReceipt';
  static String getInvoiceDownloadUrl(String invoicePath) =>
      '$resourcesUrl/$invoicePath';

  // API Versions
  static const String apiVersion = 'v1.0';


// 222300  00000  00007
  // API Endpoints
  static const String createMember = '/members/create';

  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000;

  // Headers
  static const String contentType = 'Content-Type';
  static const String applicationJson = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearer = 'Bearer';
  static const String accept = 'Accept';
  static const String apiKey = 'X-API-Key';
  static const String apiKeyValue =
      'your-api-key-here'; // Replace with your actual API key

  // Error Messages
  static const String connectionError = 'Connection error';
  static const String unauthorizedError = 'Unauthorized access';
  static const String somethingWentWrong = 'Something went wrong';

  // Gift Package endpoints
  static const String fetchPackageDetail = '/packages/{id}/fetch';
  static const String approovConfig =
      '#connect-els#wtRHqUtjWq4XngnBX4nqqqudib2O0OF4kgWFMwBF7Os=';
}
