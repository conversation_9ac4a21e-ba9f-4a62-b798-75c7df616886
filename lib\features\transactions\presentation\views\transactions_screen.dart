import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction_group.dart';
import 'package:cbrs/features/transactions/presentation/widgets/empty_state/filtered_transactions_empty_state.dart';
import 'package:cbrs/features/transactions/presentation/widgets/empty_state/transactions_empty_state.dart';
import 'package:cbrs/features/transactions/presentation/widgets/transaction_card.dart';
import 'package:cbrs/features/transactions/presentation/widgets/transaction_list_shimmer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/instance_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  int selectedIndex = 0;

  // final categories = Get.find<GetAppThemeController>().categories.value;

  DateTimeRange? _selectedDateRange;

  // Add these variables for pagination
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  static const int _perPage = 10;
  bool _isLoadingMore = false;
  String _currentUserId = '';

  // Add caching for transactions
  List<TransactionGroup>? _cachedGroups;
  bool _isInitialLoad = true;
  bool shouldShowLoader = false;

  void _showDateRangePicker() {
    final categories = Get.find<GetAppThemeController>().categories.value;
    final transactionBloc =
        context.read<TransactionBloc>(); // Get the existing HomeBloc instance

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocProvider.value(
        value: transactionBloc, // Provide the existing HomeBloc instance
        child: BlocBuilder<TransactionBloc, TransactionState>(
          builder: (context, state) {
            final primaryColor = Theme.of(context).primaryColor;

            return Container(
              // margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Title
                  Container(
                    alignment: Alignment.centerLeft,
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      'Select Date Range',
                      style: GoogleFonts.outfit(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Calendar
                  CalendarDatePicker2(
                    config: CalendarDatePicker2Config(
                      calendarType: CalendarDatePicker2Type.range,
                      selectedDayHighlightColor: primaryColor,
                    ),
                    value: _selectedDateRange != null
                        ? [_selectedDateRange!.start, _selectedDateRange!.end]
                        : [],
                    onValueChanged: (dates) {
                      if (dates.length == 2) {
                        setState(() {
                          _selectedDateRange = DateTimeRange(
                            start: dates[0],
                            end: dates[1],
                          );
                        });
                      }
                    },
                  ),

                  // Buttons Row
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        // Cancel Button

                        Expanded(
                          child: CustomRoundedBtn(
                            btnText: 'Cancel',
                            textColor: Theme.of(context).primaryColor,
                            isLoading: false,
                            bgColor: Colors.white,
                            borderSide: BorderSide(
                              color: Theme.of(context).primaryColor,
                            ),
                            onTap: () {
                              Navigator.pop(context);
                            },
                          ),
                        ),
                        const SizedBox(width: 12),

                        Expanded(
                          child: CustomRoundedBtn(
                            btnText: 'Done',
                            isLoading: false,
                            borderSide: BorderSide(
                              color: Theme.of(context).primaryColor,
                            ),
                            onTap: () {
                              if (_selectedDateRange == null) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Please select both start and end dates',
                                      style: GoogleFonts.outfit(),
                                    ),
                                    behavior: SnackBarBehavior.floating,
                                    margin: EdgeInsets.only(
                                      bottom:
                                          MediaQuery.of(context).size.height -
                                              200,
                                      right: 20,
                                      left: 20,
                                    ),
                                  ),
                                );
                                return;
                              }

                              debugPrint('FilterByDateRangeEvent');
                              setState(() {
                                shouldShowLoader = true;
                              });

                              context.read<TransactionBloc>().add(
                                    FilterByDateRangeEvent(
                                      startDate: _selectedDateRange!.start,
                                      endDate: _selectedDateRange!.end,
                                      transactionType:
                                          categories[selectedIndex],
                                    ),
                                  );
                              setState(() {
                                shouldShowLoader = true;
                              });
                              Navigator.pop(context);
                            },
                          ),
                        ),
/*



                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(
                                color: primaryColor,
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                color: primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Done Button
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              if (_selectedDateRange == null) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Please select both start and end dates',
                                      style: GoogleFonts.outfit(),
                                    ),
                                    behavior: SnackBarBehavior.floating,
                                    margin: EdgeInsets.only(
                                      bottom:
                                          MediaQuery.of(context).size.height -
                                              200,
                                      right: 20,
                                      left: 20,
                                    ),
                                  ),
                                );
                                return;
                              }

                              debugPrint('FilterByDateRangeEvent');
                              setState(() {
                                shouldShowLoader = true;
                              });

                              context.read<TransactionBloc>().add(
                                    FilterByDateRangeEvent(
                                      startDate: _selectedDateRange!.start,
                                      endDate: _selectedDateRange!.end,
                                      transactionType:
                                          categories[selectedIndex],
                                    ),
                                  );
                              setState(() {
                                shouldShowLoader = true;
                              });
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: primaryColor,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text(
                              'Done',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),

                        */
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _loadCurrentUserId();
    context.read<TransactionBloc>().add(
          FetchTransactionsEvent(page: _currentPage, perPage: _perPage),
        );

    // Add scroll listener for pagination
    _scrollController.addListener(_onScroll);

    // Add listener for wallet changes
    context.read<HomeBloc>().stream.listen((state) {
      if (state is HomeLoaded && mounted) {
        setState(() {
          selectedIndex = 0; // Reset to 'All' when wallet changes
        });
        context.read<TransactionBloc>().add(
              const FetchTransactionsEvent(page: 1, perPage: _perPage),
            );
      }
    });
  }

  Future<void> _loadCurrentUserId() async {
    final authLocalDataSource = context.read<AuthLocalDataSource>();
    final userId = await authLocalDataSource.getUserId();
    if (userId != null) {
      setState(() {
        _currentUserId = userId;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _handleRefresh() async {
    // Reset pagination

    final categories = Get.find<GetAppThemeController>().categories.value;

    setState(() {
      _currentPage = 1;
      _isLoadingMore = false;
      shouldShowLoader = false;
      // Don't reset cached data here
    });

    // Get current selected category
    final selectedCategory = categories[selectedIndex];

    // Fetch fresh transactions with filter
    if (selectedCategory == 'All') {
      context.read<TransactionBloc>().add(
            FetchTransactionsEvent(page: _currentPage, perPage: _perPage),
          );
    } else {
      context.read<TransactionBloc>().add(
            FilterTransactionsEvent(selectedCategory),
          );
    }

    // Give time for transactions to load
    await Future.delayed(const Duration(milliseconds: 500));
  }

  void _onScroll() {
    if (!_isLoadingMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent * 0.8) {
      setState(() {
        _isLoadingMore = true;
      });

      _loadMoreTransactions();
    }
  }

  void _loadMoreTransactions() {
    _currentPage++;
    context.read<TransactionBloc>().add(
          FetchMoreTransactionsEvent(page: _currentPage, perPage: _perPage),
        );
  }

  void _handleCategoryTap(int index) {
    setState(() {
      selectedIndex = index;
      shouldShowLoader = true;
    });

    final categories = Get.find<GetAppThemeController>().categories.value;

    context
        .read<TransactionBloc>()
        .add(FilterTransactionsEvent(categories[index]));
  }

  String _formatCategoryText(String category) {
    if (category == 'All') return category;

    return category
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    debugPrint('print categroy');
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Transactions',
        ),
        titleSpacing: 22.w,
        automaticallyImplyLeading: false,
        actions: [
          MultiBlocProvider(
            providers: [
              BlocProvider<TransactionBloc>.value(
                value: sl<TransactionBloc>(),
              ),
              BlocProvider<HomeBloc>.value(
                value: sl<HomeBloc>(),
              ),
            ],
            child: BlocBuilder<TransactionBloc, TransactionState>(
              builder: (context, state) {
                // state is HomeLoaded && state.data.selectedWallet == 'USD';

                return GestureDetector(
                  onTap: _showDateRangePicker,
                  child: Container(
                    width: 30.w,
                    height: 30.h,
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      color: Theme.of(context).secondaryHeaderColor,
                      borderRadius: BorderRadius.circular(32.r),
                    ),
                    margin: EdgeInsets.symmetric(horizontal: 10.w),
                    child: Center(
                      child: Image.asset(
                        'assets/vectors/solar_calendar_broken.png',
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Categories section - Always visible
          SizedBox(
            height: 24.h,
          ),

          SizedBox(
            height: 40.h,
            child: Obx(
              () => ListView.builder(
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                itemCount: Get.find<GetAppThemeController>().categories.length,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                itemBuilder: (context, index) {
                  final isSelected = selectedIndex == index;
                  return MultiBlocProvider(
                    providers: [
                      BlocProvider<TransactionBloc>.value(
                        value: sl<TransactionBloc>(),
                      ),
                      BlocProvider<HomeBloc>.value(
                        value: sl<HomeBloc>(),
                      ),
                    ],
                    child: BlocBuilder<HomeBloc, HomeState>(
                      builder: (context, state) {
                        final isUSD = state is HomeLoaded &&
                            state.data.selectedWallet == 'USD';

                        return GestureDetector(
                          onTap: () => _handleCategoryTap(index),
                          child: Container(
                            width: index == 0 ? 100 : null,
                            // height: 100,
                            margin: EdgeInsets.only(right: 10.w),
                            padding: EdgeInsets.symmetric(
                              horizontal: 24.w,
                              // vertical: 10.h,
                            ),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Theme.of(context).primaryColor
                                  : Colors.white,
                              borderRadius: BorderRadius.circular(32.r),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.white.withOpacity(0.06),
                                  blurRadius: 24,
                                ),
                              ],
                            ),
                            child: Center(
                              child: CustomBuildText(
                                text: _formatCategoryText(
                                  Get.find<GetAppThemeController>()
                                      .categories
                                      .value[index],
                                ),

                                textAlign: TextAlign.center,
                                color: isSelected
                                    ? Colors.white
                                    : Theme.of(context).primaryColor,
                                // isUSD
                                //     ? const Color(0xFF0D3C89)
                                //     : theme.primaryColor,
                                fontWeight: isSelected
                                    ? FontWeight.w700
                                    : FontWeight.w500,
                                fontSize: isSelected ? 16.sp : 14.sp,
                              ),

                              // Text(
                              //   " ${_formatCategoryText(
                              //     Get.find<GetAppThemeController>()
                              //         .categories
                              //         .value[index],
                              //   ).substring(0, 1).toUpperCase()}${_formatCategoryText(
                              //     Get.find<GetAppThemeController>()
                              //         .categories
                              //         .value[index],
                              //   ).substring(1).toLowerCase()}",
                              //   textAlign: TextAlign.center,
                              //   style: GoogleFonts.outfit(
                              //     color: isSelected
                              //         ? Colors.white
                              //         : Theme.of(context).primaryColor,
                              //     // isUSD
                              //     //     ? const Color(0xFF0D3C89)
                              //     //     : theme.primaryColor,
                              //     fontWeight: isSelected
                              //         ? FontWeight.w700
                              //         : FontWeight.w500,
                              //     fontSize: isSelected ? 16.sp : 14.sp,
                              //   ),
                              // ),
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ),

          // Modified Content section
          Expanded(
            child: BlocConsumer<TransactionBloc, TransactionState>(
              listener: (context, state) {
                if (state is TransactionLoaded) {
                  setState(() {
                    _cachedGroups = state.groupedTransactions;
                    _isInitialLoad = false;
                    _isLoadingMore = false;
                  });
                }
              },
              builder: (context, state) {
                // // Show error state if there's an error
                // if (state is TransactionError) {
                //   return CustomErrorRetry(
                //     message: state.message,
                //     onRetry: () {
                //       context.read<TransactionBloc>().add(
                //             FetchTransactionsEvent(
                //               page: _currentPage,
                //               perPage: _perPage,
                //             ),
                //           );
                //     },
                //   );
                // }

                debugPrint('stats is $state');
                if (state is TransactionLoading && !state.keepCache) {
                  debugPrint('shhhho');
                  return const TransactionListShimmer();
                }
                // Show loading shimmer only on initial load without cached data
                if (state is TransactionLoading &&
                    _isInitialLoad &&
                    _cachedGroups == null) {
                  return const TransactionListShimmer();
                }

                if (state is TransactionLoading && shouldShowLoader) {
                  return const TransactionListShimmer();
                }

                // Get transactions from state or cache
                final groupedTransactions = state is TransactionLoaded
                    ? state.groupedTransactions
                    : _cachedGroups;

                // Show empty state if needed
                if (groupedTransactions != null &&
                    groupedTransactions.isEmpty) {
                  final categories =
                      Get.find<GetAppThemeController>().categories.value;

                  return RefreshIndicator(
                    onRefresh: _handleRefresh,
                    color: Theme.of(context).primaryColor,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: Container(
                            height: constraints.maxHeight,
                            alignment: Alignment.center,
                            child: selectedIndex == 0
                                ? const TransactionsEmptyState()
                                : FilteredTransactionsEmptyState(
                                    category: _formatCategoryText(
                                      categories[selectedIndex],
                                    ),
                                  ),
                          ),
                        );
                      },
                    ),
                  );
                }

                // Show transaction list
                if (groupedTransactions != null) {
                  return RefreshIndicator(
                    onRefresh: _handleRefresh,
                    color: Theme.of(context).primaryColor,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        return SingleChildScrollView(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              minHeight: constraints.maxHeight,
                            ),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                              ),
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 16.h,
                                  ),
                                  if (groupedTransactions.isEmpty)
                                    SizedBox(
                                      height: constraints.maxHeight,
                                      child: const Center(
                                        child: TransactionsEmptyState(),
                                      ),
                                    )
                                  else
                                    ...List.generate(
                                      groupedTransactions.length,
                                      (index) {
                                        final group =
                                            groupedTransactions[index];
                                        return Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding:
                                                  EdgeInsets.only(bottom: 12.h),
                                              child: Text(
                                                group.title,
                                                style: GoogleFonts.outfit(
                                                  fontSize: 16.sp,
                                                  fontWeight: FontWeight.w600,
                                                  color:
                                                      const Color(0xFFAAAAAA),
                                                ),
                                              ),
                                            ),
                                            Container(
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.05),
                                                    blurRadius: 10,
                                                    offset: const Offset(0, 2),
                                                  ),
                                                ],
                                              ),
                                              child: Column(
                                                children: List.generate(
                                                  group.transactions.length,
                                                  (index) => Column(
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(16),
                                                        child: TransactionCard(
                                                          title: group
                                                                      .transactions[
                                                                          index]
                                                                      .transactionType
                                                                      .toLowerCase() ==
                                                                  'wallet_transfer'
                                                              ? (group
                                                                          .transactions[
                                                                              index]
                                                                          .senderId ==
                                                                      _currentUserId
                                                                  ? group
                                                                          .transactions[
                                                                              index]
                                                                          .beneficiaryName ??
                                                                      'Unknown'
                                                                  : group
                                                                      .transactions[
                                                                          index]
                                                                      .senderName)
                                                              : (group
                                                                          .transactions[
                                                                              index]
                                                                          .transactionType ==
                                                                      'change_to_birr'
                                                                  ? group
                                                                      .transactions[
                                                                          index]
                                                                      .senderName
                                                                  : group
                                                                          .transactions[
                                                                              index]
                                                                          .beneficiaryName ??
                                                                      'Unknown'),
                                                          transactionType: group
                                                              .transactions[
                                                                  index]
                                                              .transactionType,
                                                          amount: group
                                                                      .transactions[
                                                                          index]
                                                                      .transactionType ==
                                                                  'change_to_birr'
                                                              ? 'USD ${group.transactions[index].billAmount}'
                                                              : '${group.transactions[index].originalCurrency ?? 'null'} ${group.transactions[index].billAmount}',
                                                          date: group
                                                              .transactions[
                                                                  index]
                                                              .createdAt
                                                              .toString(),
                                                          currentUserId:
                                                              _currentUserId,
                                                          senderId: group
                                                              .transactions[
                                                                  index]
                                                              .senderId,
                                                          beneficiaryId: group
                                                                  .transactions[
                                                                      index]
                                                                  .beneficiaryId ??
                                                              '',
                                                          transaction: group
                                                                  .transactions[
                                                              index],
                                                          onTap: () {
                                                            context.pushNamed(
                                                              AppRouteName
                                                                  .transactionDetails,
                                                              pathParameters: {
                                                                'id': group
                                                                    .transactions[
                                                                        index]
                                                                    .id,
                                                              },
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      if (index <
                                                          group.transactions
                                                                  .length -
                                                              1)
                                                        Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                            horizontal: 16,
                                                          ),
                                                          child: SizedBox(
                                                            width:
                                                                double.infinity,
                                                            height: 1,
                                                            child: CustomPaint(
                                                              painter:
                                                                  DottedLinePainter(),
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                          ],
                                        );
                                      },
                                    ),
                                  if (_isLoadingMore)
                                    const Padding(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 16),
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  );
                }

                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }
}
