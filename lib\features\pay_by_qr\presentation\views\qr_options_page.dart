import 'dart:convert';
import 'dart:io';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/common/widgets/custom_toast.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/home/<USER>/widgets/quick_wallet/quick_wallet_recipients_bottom_sheet.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:ui' as ui;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/features/pay_by_qr/application/bloc/parse_qr_bloc.dart';
import 'package:cbrs/features/pay_by_qr/application/bloc/parse_qr_event.dart';
import 'package:cbrs/features/pay_by_qr/application/bloc/parse_qr_state.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:image_picker/image_picker.dart';

class QrOptionsPage extends StatefulWidget {
  const QrOptionsPage({required this.isUSD, super.key});
  final bool isUSD;

  @override
  State<QrOptionsPage> createState() => _QrOptionsPageState();
}

class _QrOptionsPageState extends State<QrOptionsPage>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  MobileScannerController? controller;
  bool _hasPermission = false;
  bool _isTorchOn = false;
  final GlobalKey _qrKey = GlobalKey();
  bool _isScanning = true;
  bool _isDownloading = false;
  // Store direct bloc reference if used

  @override
  void initState() {
    super.initState();

    _requestCameraPermission();
    getUserId();

    // _verifyBlocRegistration();
    // Add as observer to track app lifecycle changes
    WidgetsBinding.instance.addObserver(this);
  }

  String currentUserId = '';

  Future<void> getUserId() async {
    final id = await sl<AuthLocalDataSource>().getUserId() ?? '';
    setState(() {
      currentUserId = id;
    });
  }

  // Handle app lifecycle changes
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // When app is resumed (including when returning from other screens)
      if (selectedTab == 'Scan QR' && _isScanning) {
        _restartScanner();
      }
    } else if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      // When app is paused or inactive, stop scanner to save resources
      controller?.stop();
    }
  }

  void _restartScanner() {
    if (controller != null && _isScanning) {
      setState(() => _isScanning = true);
      controller?.start();
    }
  }

  Future<void> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    if (mounted) {
      setState(() {
        _hasPermission = status.isGranted;
        if (_hasPermission) {
          controller = MobileScannerController(
            torchEnabled: _isTorchOn,
          );
        }
      });
    }
  }

  Future<void> _reRequestCameraPermission() async {
    final status = await Permission.camera.status;

    if (status.isGranted) {
      _initializeScanner();
    } else if (status.isDenied) {
      final result = await Permission.camera.request();
      if (result.isGranted) {
        _initializeScanner();
      }
      // else if (result.isPermanentlyDenied) {
      //   openAppSettings();
      // }
    } else if (status.isPermanentlyDenied) {
      openAppSettings();
      setState(() {});
    }
  }

  void _initializeScanner() {
    if (!mounted) return;
    setState(() {
      _hasPermission = true;
      controller = MobileScannerController(
        torchEnabled: _isTorchOn,
      );
    });
  }

  void _toggleTorch() {
    if (controller != null) {
      setState(() {
        _isTorchOn = !_isTorchOn;
        controller?.toggleTorch();
      });
    }
  }

  bool _isEmail(String value) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(value);
  }

  @override
  void dispose() {
    // _directBloc = null;
    controller?.stop();
    controller?.dispose();

    // Remove observer
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // Reset scanner state when a navigation has completed
  void _resetScannerAfterNavigation() {
    if (selectedTab == 'Scan QR') {
      setState(() => _isScanning = true);
      _restartScanner();
    }
  }

  String selectedTab = 'Scan QR';
  final List<String> tabList = ['Scan QR', 'My QR'];

  void onTap(String tabName) {
    if (tabName != selectedTab) {
      setState(() {
        selectedTab = tabName;
      });

      debugPrint('ss s $selectedTab');

      if (selectedTab == 'Scan QR') {
        // When switching to scan tab, restart the scanner

        if (!_hasPermission) {
          _requestCameraPermission();
          return;
        }
        _restartScanner();
      } else {
        // When switching to My QR tab, stop the scanner and generate QR
        controller?.stop();
        context.read<ParseQrBloc>().add(GenerateQrRequested());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ParseQrBloc, ParseQrState>(
      listener: (context, state) {
        if (state is ParseQrFailure) {
          CustomToastification(
            context,
            message: state.message,
          );

          // Add a small delay before re-enabling scanning
          Future.delayed(const Duration(milliseconds: 300), () {
            if (mounted) {
              // Re-enable scanning after error
              setState(() => _isScanning = true);
              _restartScanner();
            }
          });
        } else if (state is ParseQrSuccess) {
          print('🎉 QR Parse Success: ${state.response}');
          // Keep scanning disabled after success
          setState(() => _isScanning = false);
          controller?.stop();

          final userCodeLower = state.response.userCode.toLowerCase();

          switch (userCodeLower) {
            case 'merchant':
              print('🎯 Navigating to merchant payment page');
              context.pushReplacementNamed(
                AppRouteName.merchantPaymentByQrAddAmount,
                extra: <String, dynamic>{
                  'merchantName': state.response.name,
                  'merchantId': state.response.accountNumber,
                  'merchantTill':
                      state.response.additionalInformation['merchant_till'],
                  'amount': state.response.amount,
                },
              );
            case 'agent':
              context.pushReplacementNamed(
                AppRouteName.cashOutAgentAddAmount,
                extra: <String, dynamic>{
                  'agentName': state.response.name,
                  'agentId': state.response.accountNumber,
                  'showAgentContainer': true,
                  'agentCode':
                      state.response.additionalInformation['merchant_till'],
                },
              );
            case 'member':
              // Get current user ID

              // Check if scanned QR is from current user
              if (currentUserId == state.response.accountNumber) {
                // Show toast for current user's QR
                CustomToastification(
                  context,
                  message: "This is current user's QR code",
                );

                Future.delayed(const Duration(milliseconds: 300), () {
                  if (mounted) {
                    // Re-enable scanning after error
                    setState(() => _isScanning = true);
                    _restartScanner();
                  }
                });

                return;
              }

              final merchantTill = state
                  .response.additionalInformation['merchant_till'] as String;
              final isEmail = _isEmail(merchantTill);

              context.pushReplacementNamed(
                AppRouteName.walletTransferAddAmount,
                pathParameters: {
                  'currency': state.response.currency,
                },
                extra: {
                  'isFromQuick': true,
                  'recipent': RecentRecipient(
                    id: '',
                    email: isEmail ? merchantTill : '',
                    phone: isEmail ? '' : merchantTill,
                    name: state.response.name,
                    date: DateTime.now(),
                  ),
                },
              );

            // context.pushReplacementNamed(
            //   AppRouteName.qrWalletTransferAddAmount,
            //   pathParameters: {'currency': state.response.currency},
            //   extra: {
            //     'email': isEmail ? merchantTill : '',
            //     'phoneNumber': isEmail ? '' : merchantTill,
            //     'fullName': state.response.name,
            //     'isUSD': widget.isUSD,
            //   },
            // );
            default:
              CustomToastification(
                context,
                message: 'Invalid user type in QR code',
              );
              Future.delayed(const Duration(milliseconds: 300), () {
                if (mounted) {
                  // Re-enable scanning after error
                  setState(() => _isScanning = true);
                  _restartScanner();
                }
              });
          }

          // Add listener to re-enable scanning when returning from navigation
          // This will be triggered when the navigation is complete
          /*
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // Using a delayed future to reset after navigation completes
            Future.delayed(const Duration(milliseconds: 1000), () {
              if (mounted) {
                _resetScannerAfterNavigation();
              }
            });
          });

          */
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          //   _tabController.index == 0 ? Colors.transparent : Colors.white,

          extendBodyBehindAppBar: true,
          body: SafeArea(
            bottom: false,
            child: Stack(
              fit: StackFit.expand,
              children: [
                // Tab Bar at top

                if (state is ParseQrLoading)
                  const CustomConnectLoader()
                else if (selectedTab == 'My QR')
                  _buildMyQRTab()
                else if (!_hasPermission)
                  _buildCameraPermissionNeeded(context)
                else if (_isScanning) ...[
                  MobileScanner(
                    controller: controller,
                    onDetect: (capture) {
                      final barcodes = capture.barcodes;
                      if (barcodes.isNotEmpty && _isScanning) {
                        final qrString = barcodes.first.rawValue;
                        if (qrString != null) {
                          print(
                            '📊 QR Code detected by camera: $qrString',
                          );
                          setState(() => _isScanning = false);
                          controller?.stop();

                          try {
                            print(
                              'Using ParseQrBloc with isUSD: ${widget.isUSD}',
                            );
                            // CustomToastification(context, message: qrString);

                            parsingQRCode(qrString);
                          } catch (e) {
                            print(
                              'Error processing camera QR scan: $e',
                            );
                            CustomToastification(
                              context,
                              message: 'Failed to process QR code: $e',
                            );
                            setState(() => _isScanning = true);
                            _restartScanner();
                          }
                        }
                      }
                    },
                  ),

                  // Scanning area overlay
                  CustomPaint(
                    size: Size.infinite,
                    painter: QRScannerOverlayShape(),
                  ),
                  if (state is ParseQrLoading)
                    ColoredBox(
                      color: Colors.black.withOpacity(0.3),
                      child: const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],

                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: SizedBox(
                    height: 400,
                    child: CustomRoundedTabs(
                      onTap: onTap,
                      selectedTab: selectedTab,
                      tabList: tabList,
                    ),
                  ),
                ),
                // Bottom buttons
                if (_hasPermission && selectedTab != 'My QR') ...[
                  Positioned(
                    bottom: 170,
                    left: 16,
                    right: 16,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Gallery and Light buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            InkWell(
                              onTap: _scanFromGallery,
                              child: _qrActions(
                                label: 'Scan from Gallery',
                                icon: MediaRes.qrGalleryIcon,
                              ),
                            ),
                            InkWell(
                              onTap: _toggleTorch,
                              child: _qrActions(
                                label: _isTorchOn ? 'Light Off' : 'Light On',
                                icon: MediaRes.qrLightIcon,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 46),
                        // Cancel button
                      ],
                    ),
                  ),
                ],
                Positioned(
                  bottom: 25,
                  left: 16,
                  right: 16,
                  child: Column(
                    children: [
                      if (!_hasPermission) ...[
                        CustomRoundedBtn(
                          btnText: 'Enable Camera',
                          isLoading: false,
                          onTap: _reRequestCameraPermission,
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        CustomRoundedBtn(
                          btnText: 'Back',
                          isLoading: false,
                          bgColor: Colors.white,
                          textColor: Theme.of(context).primaryColor,
                          borderSide: BorderSide(
                            color: Theme.of(context).primaryColor,
                          ),
                          onTap: () {
                            Navigator.pop(context);
                          },
                        ),
                      ] else
                        CustomRoundedBtn(
                          btnText: 'Cancel',
                          isLoading: false,
                          onTap: () {
                            Navigator.pop(context);
                          },
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String extractTransactionId(String decoded) {
    // random-sdjhkbsdjksd-sdsd-sd-dssd-sd-random

    final firstHyphen = decoded.indexOf('-');
    final lastHypehn = decoded.lastIndexOf('-');
    if (firstHyphen != -1 && lastHypehn != -1 && firstHyphen < lastHypehn) {
      return decoded.substring(firstHyphen + 1, lastHypehn);
    }
    return '';
  }

// TODDO
  void parsingQRCode(String qrString) {
    try {
      // CustomToastification(context, message: 'message');
      debugPrint('base64Decoded checkign');

      final base64Decoded = utf8.decode(base64Url.decode(qrString));

      debugPrint('base64Decoded $base64Decoded');

      final extractString = extractTransactionId(base64Decoded);

      if (extractString.isNotEmpty) {
        context.pushReplacementNamed(
          AppRouteName.validateTransaction,
          extra: {'transactionId': extractString},
        );
      } else {
        context.read<ParseQrBloc>().add(
              ParseQrRequested(
                qrString: qrString,
              ),
            );
      }
    } catch (e) {
      context.read<ParseQrBloc>().add(
            ParseQrRequested(
              qrString: qrString,
            ),
          );
    } finally {
      CustomToastification(context, message: 'message ');
    }
  }

  Widget _qrActions({
    required String icon,
    required String label,
    VoidCallback? onTap,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.16),
        boxShadow: const [
          BoxShadow(
            // color: Colors.white,
            color: Colors.transparent,

            blurRadius: 4,
          ),
        ],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Text(
            label,
            style: GoogleFonts.outfit(
              color: Colors.white,
              fontSize: 16.sp,
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          Image.asset(icon, width: 20),
        ],
      ),
    );
  }

  Widget _buildCameraPermissionNeeded(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) => ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: constraints.maxHeight,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                MediaRes.camera,
                width: 56.w,
                height: 56.h,
              ),
              SizedBox(
                height: 14.h,
              ),
              const CustomBuildText(
                text: 'Camera Permission required',
                fontWeight: FontWeight.bold,
                fontSize: 17,
              ),
              const SizedBox(
                height: 4,
              ),
              CustomBuildText(
                text:
                    'We don’t have access to your camera, You can enable access in privacy settings',
                color: Colors.black.withOpacity(0.4),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _scanFromGallery() async {
    final parseQrBloc = context.read<ParseQrBloc>();
    try {
      final picker = ImagePicker();
      final image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        final file = File(image.path);
        print('📷 Selected image path: ${file.path}');

        // Stop current scanner to avoid conflicts
        controller?.stop();

        // Set isScanning to false to prevent camera scanning
        setState(() => _isScanning = false);

        final galleryController = MobileScannerController();

        try {
          print('🔍 Analyzing image for QR code...');
          final capture = await galleryController.analyzeImage(file.path);

          if (capture != null && capture.barcodes.isNotEmpty) {
            print('✅ Found ${capture.barcodes.length} barcodes in image');
            for (final barcode in capture.barcodes) {
              if (barcode.rawValue != null) {
                print('📊 QR code data: ${barcode.rawValue}');

                // Get the qr string from the barcode
                final qrString = barcode.rawValue!;

                try {
                  print('Using ParseQrBloc with isUSD: ${widget.isUSD}');

                  // First try to use the BlocProvider's bloc (which should have correct isUSD)
                  context
                      .read<ParseQrBloc>()
                      .add(ParseQrRequested(qrString: qrString));

                  // Add listener to re-enable scanning when returning from navigation
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    // Using a delayed future to reset after navigation completes
                    Future.delayed(const Duration(milliseconds: 1000), () {
                      if (mounted) {
                        _resetScannerAfterNavigation();
                      }
                    });
                  });
                } catch (blocError) {
                  print('Error using ParseQrBloc from context: $blocError');

                  try {
                    // If that fails, try to create a new bloc directly
                    print(
                      'Attempting to create a new ParseQrBloc with isUSD=${widget.isUSD}',
                    );
                    // final newBloc = sl<ParseQrBloc>(param1: widget.isUSD);
                    // _directBloc = newBloc; // Store reference to the direct bloc

                    // Create an overlay for progress indicator
                    late final OverlayEntry overlayEntry;

                    overlayEntry = OverlayEntry(
                      builder: (context) => BlocProvider.value(
                        value: parseQrBloc,
                        child: BlocListener<ParseQrBloc, ParseQrState>(
                          listener: (context, state) {
                            if (state is ParseQrFailure) {
                              // Show toast for error
                              CustomToastification(
                                context,
                                message: state.message,
                              );

                              // Remove overlay after a brief delay
                              Future.delayed(const Duration(milliseconds: 600),
                                  () {
                                overlayEntry.remove();
                                setState(() => _isScanning = true);
                                _restartScanner();
                              });
                            } else if (state is ParseQrSuccess) {
                              overlayEntry.remove();

                              // Add listener to re-enable scanning when returning from navigation
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                // Using a delayed future to reset after navigation completes
                                Future.delayed(
                                    const Duration(milliseconds: 1000), () {
                                  if (mounted) {
                                    _resetScannerAfterNavigation();
                                  }
                                });
                              });
                            }
                          },
                          child: Material(
                            type: MaterialType.transparency,
                            child: ColoredBox(
                              color: Colors.black.withOpacity(0.3),
                              child: const Center(
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );

                    // Insert overlay into the widget tree
                    Overlay.of(context).insert(overlayEntry);

                    // Process the QR code
                    parseQrBloc.add(ParseQrRequested(qrString: qrString));
                  } catch (directBlocError) {
                    print(
                      'Fatal error creating ParseQrBloc directly: $directBlocError',
                    );
                    CustomToastification(
                      context,
                      message: 'Error processing QR code: $directBlocError',
                    );
                    setState(() => _isScanning = true);
                    _restartScanner();
                  }
                }
                return; // Exit after successful processing
              }
            }
          } else {
            print('❌ No barcodes found in image');
            CustomToastification(
              context,
              message: 'No QR code found in the image',
            );
            setState(() => _isScanning = true);
            _restartScanner();
          }
        } catch (scanError) {
          print('🔥 Error scanning image: $scanError');
          CustomToastification(
            context,
            message: 'Failed to scan QR code from image: $scanError',
          );
          setState(() => _isScanning = true);
          _restartScanner();
        } finally {
          galleryController.dispose();
        }
      }
    } catch (e) {
      print('🔥 Gallery picker error: $e');
      CustomToastification(
        context,
        message: 'Failed to pick image from gallery',
      );
      setState(() => _isScanning = true);
      _restartScanner();
    }
  }

  Widget _buildMyQRTab() {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Tab Bar
          Center(
            child: BlocBuilder<ParseQrBloc, ParseQrState>(
              builder: (context, state) {
                if (state is GenerateQrLoading) {
                  return const CustomConnectLoader();
                } else if (state is GenerateQrFailure) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        state.message,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          context
                              .read<ParseQrBloc>()
                              .add(GenerateQrRequested());
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  );
                } else if (state is GenerateQrSuccess) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        margin: const EdgeInsets.all(16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.4),
                          ),
                        ),
                        child: RepaintBoundary(
                          key: _qrKey,
                          child: QrImageView(
                            data: state.response.qr,
                            size: 280,
                            backgroundColor: Colors.white,
                            eyeStyle: QrEyeStyle(
                              eyeShape: QrEyeShape.square,
                              color: Theme.of(context).primaryColor,
                            ),
                            dataModuleStyle: QrDataModuleStyle(
                              dataModuleShape: QrDataModuleShape.square,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildActionButton(
                            icon: MediaRes.icRoundShare,
                            backgroundColor:
                                Theme.of(context).secondaryHeaderColor,
                            onTap: _shareQrCode,
                          ),
                          SizedBox(width: 16.w),
                          _buildActionButton(
                            icon: MediaRes.downloadIcon,
                            backgroundColor:
                                Theme.of(context).secondaryHeaderColor,
                            onTap: _saveQrToGallery,
                          ),
                        ],
                      ),
                    ],
                  );
                } else {
                  // Initial state or any other state, trigger QR generation
                  if (selectedTab == 'My QR') {
                    context.read<ParseQrBloc>().add(GenerateQrRequested());
                  }
                  return const SizedBox();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String icon,
    required VoidCallback onTap,
    required Color backgroundColor,
  }) {
    return Container(
      width: 36.w,
      height: 36.w,
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        child: _isDownloading && icon == MediaRes.downloadIcon
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              )
            : Image.asset(
                icon,
                color: Theme.of(context).primaryColor,
              ),
      ),
    );
  }

  Future<void> _shareQrCode() async {
    try {
      // Capture QR code as image
      debugPrint('share qr code');
      final boundary =
          _qrKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) return;

      final image = await boundary.toImage(pixelRatio: 3);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) return;

      // Create temporary file
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/qr_code.png');
      await file.writeAsBytes(byteData.buffer.asUint8List());

      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'My Connect Cash-In QR Code',
      );
    } catch (e) {
      if (mounted) {
        CustomToastification(
          context,
          message: 'Failed to share QR Code',
        );
      }
    }
  }

  Future<void> _saveQrToGallery() async {
    debugPrint('downloading qr code');

    if (_isDownloading) return; // Prevent multiple simultaneous downloads

    setState(() => _isDownloading = true);

    try {
      // Request appropriate permissions based on platform and Android version
      PermissionStatus? status;

      if (Platform.isAndroid) {
        if (await Permission.photos.request().isGranted) {
          status = await Permission.photos.status;
        } else {
          status = await Permission.storage.request();
        }
      } else {
        status = await Permission.photos.request();
      }

      if (!status.isGranted) {
        if (mounted) {
          CustomToastification(
            context,
            message: 'Storage permission is required to save QR code',
          );
        }
        return;
      }

      // Capture QR code as image
      final boundary =
          _qrKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) return;

      final image = await boundary.toImage(pixelRatio: 3);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) return;

      // Save to gallery
      final result = await ImageGallerySaverPlus.saveImage(
        byteData.buffer.asUint8List(),
        name: 'qr code:_${DateTime.now().millisecondsSinceEpoch}',
        quality: 100,
      );

      if (mounted) {
        if (result['isSuccess'] == true) {
          CustomToastification(
            context,
            message: 'QR Code saved to gallery successfully',
            isError: false,
          );
        } else {
          CustomToastification(
            context,
            message: 'Failed to save QR Code to gallery',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        CustomToastification(
          context,
          message: 'Error saving QR Code: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isDownloading = false);
      }
    }
  }
}

// Add QRScannerOverlayShape class
class QRScannerOverlayShape extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final scanAreaSize = size.width * 0.7;
    final scanAreaLeft = (size.width - scanAreaSize) / 2;
    final scanAreaTop = (size.height - scanAreaSize) / 2;
    const cornerRadius = 20.0;

    final overlayPaint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    // Create rounded rectangle for scanner area
    final scanRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        scanAreaLeft,
        scanAreaTop,
        scanAreaSize,
        scanAreaSize,
      ),
      const Radius.circular(cornerRadius),
    );

    // Draw the semi-transparent overlay with rounded cutout
    final overlayPath = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRRect(scanRect);

    canvas.drawPath(overlayPath, overlayPaint);

    // Draw the scanning area border with rounded corners
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    canvas.drawRRect(scanRect, borderPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
