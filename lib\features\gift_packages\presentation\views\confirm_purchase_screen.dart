import 'package:cbrs/core/common/widgets/confirm/custom_transaction_confirm_screen.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_row_transaction.dart';
import 'package:cbrs/core/common/widgets/custom_toast.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/price_formatter.dart';
import 'package:cbrs/features/gift_packages/domain/entities/package_item.dart';
import 'package:cbrs/features/gift_packages/domain/repositories/gift_package_repository.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ConfirmPurchaseScreen extends StatefulWidget {
  final PackageItem item;
  final int quantity;
  final String recipientName;
  final String recipientPhone;
  final String sellingPrice;
  final String merchantName;

  const ConfirmPurchaseScreen({
    super.key,
    required this.item,
    required this.quantity,
    required this.recipientName,
    required this.recipientPhone,
    required this.sellingPrice,
    required this.merchantName,
  });

  @override
  State<ConfirmPurchaseScreen> createState() => _ConfirmPurchaseScreenState();
}

class _ConfirmPurchaseScreenState extends State<ConfirmPurchaseScreen> {
  bool _isProcessing = false;

  double _getResponsiveFontSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.sizeOf(context).width;
    return baseSize * (screenWidth / 375).clamp(0.8, 1.2);
  }

  @override
  Widget build(BuildContext context) {
    final totalAmount = double.parse(widget.sellingPrice) * widget.quantity;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Gift Packages',
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SafeArea(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 24.h),
                  child: BlocBuilder<HomeBloc, HomeState>(
                    builder: (context, state) {
                      String customerName = '';

                      if (state is HomeProfileLoadedState) {
                        customerName = state.localUser.fullName;
                      }

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const CustomPageHeader(
                            pageTitle: 'Confirm Purchase',
                            description:
                                'Complete payment to finalize your gift package purchase for your loved ones.',
                          ),
                          SizedBox(height: 24.h),

                          // Using CustomTransactionConfirmScreen
                          CustomTransactionConfirmScreen(
                            totalAmount: totalAmount,
                            isUsdTransfer: true,
                            child: Column(
                              children: [
                                CustomRowTransaction(
                                  label: 'Customer name',
                                  value: customerName,
                                ),
                                CustomRowTransaction(
                                  label: 'Recipient name',
                                  value: widget.recipientName,
                                ),
                                CustomRowTransaction(
                                  label: 'Recipient Phone',
                                  value: widget.recipientPhone,
                                ),
                                CustomRowTransaction(
                                  label: 'Package name',
                                  value: widget.item.name,
                                ),
                                CustomRowTransaction(
                                  label: 'Package Price',
                                  value: PriceFormatter.formatPrice(
                                      widget.sellingPrice),
                                ),
                                CustomRowTransaction(
                                  label: 'Quantity',
                                  value: widget.quantity.toString(),
                                ),
                                CustomRowTransaction(
                                  label: 'Super Market',
                                  value: widget.merchantName,
                                ),
                                Padding(
                                  padding: EdgeInsets.symmetric(vertical: 16.h),
                                  child: CustomPaint(
                                    painter: DottedLinePainter(
                                      color: Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.4),
                                    ),
                                    size: Size(double.infinity, 1),
                                  ),
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Total',
                                      style: GoogleFonts.outfit(
                                        fontSize: 18.sp,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.black,
                                      ),
                                    ),
                                    Text(
                                      PriceFormatter.formatPrice(
                                          totalAmount.toString()),
                                      style: GoogleFonts.outfit(
                                        fontSize: 18.sp,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          ),

          // Bottom container with shadow for confirm button
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  offset: const Offset(0, -2),
                  blurRadius: 8,
                ),
              ],
            ),
            child: SafeArea(
              top: false,
              child:BlocBuilder<HomeBloc, HomeState>(
                    builder: (context, state) {
                      String customerName = '';

                      if (state is HomeProfileLoadedState) {
                        customerName = state.localUser.fullName;
                      }

                  return CustomButton(
                    text: _isProcessing ? 'Processing...' : 'Confirm Purchase',
                    onPressed: _isProcessing
                        ? null
                        : () async {
                            try {
                              setState(() {
                                _isProcessing = true;
                              });

                              final repository =
                                  context.read<GiftPackageRepository>();
                              // final result =
                              //     await repository.purchaseGiftPackage(
                              //   giftPackageId: widget.item.id,
                              //   recipientName: widget.recipientName,
                              //   recipientPhone: widget.recipientPhone,
                              //   quantity: widget.quantity,
                              // );

                              if (!mounted) return;

                              // Navigate to success screen
                              context.pushReplacementNamed(
                                AppRouteName.purchaseSuccess,
                                extra: {
                                  // 'amount':
                                  //     (widget.item.unitPrice * widget.quantity)
                                  //         .toStringAsFixed(2),
                                  // 'recipientName': widget.recipientName,
                                  // 'recipientPhone': widget.recipientPhone,
                                  // 'customerName': customerName,
                                  // 'packageName': widget.item.name,
                                  // 'orderCode': result['orderId'].toString(),
                                  // 'paymentReference':
                                  //     result['billRefNo'].toString(),
                                  // 'quantity': widget.quantity,
                                  // 'supermarketName': widget.merchantName,
                                  // 'sellingPrice': totalAmount.toString(),
                                  // 'unitPrice': widget.sellingPrice,
                                },
                              );
                            } on ApiException catch (e) {
                              if (!mounted) return;

                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: CustomToast(
                                    message: e.message,
                                    isError: true,
                                  ),
                                  behavior: SnackBarBehavior.floating,
                                  backgroundColor: Colors.transparent,
                                  elevation: 0,
                                ),
                              );
                            } finally {
                              if (mounted) {
                                setState(() {
                                  _isProcessing = false;
                                });
                              }
                            }
                          },
                    options: CustomButtonOptions(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      color: _isProcessing
                          ? Colors.grey
                          : LightModeTheme().primaryColorUSD,
                      textStyle: GoogleFonts.outfit(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                      borderRadius: BorderRadius.circular(32.r),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
