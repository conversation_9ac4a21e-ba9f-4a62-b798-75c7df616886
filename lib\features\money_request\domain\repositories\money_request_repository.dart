import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/money_request/domain/entities/member_lookup_entity.dart';
import 'package:cbrs/features/money_request/domain/entities/my_requests_list_entity.dart';
import 'package:cbrs/features/money_request/domain/entities/wallet_detail_entity.dart';

abstract class MoneyRequestRepository {
  ResultFuture<MemberLookupEntity> memberLookUp({
    required String? emailAddress,
    required String? phoneNumber,
  });

  ResultFuture<MoneyRequestListEntity> getMoneyRequestList({
    required int page,
    String? currency,
  });

  ResultFuture<MoneyRequestEntity> getRequestDetail({
    required String? transactionId,
  });

  ResultFuture<MoneyRequestEntity> acceptOrRejectOrCancelRequest({
    required String? transactionId,
    required String? status,
    required double? amount,
    String? reason,
  });

  ResultFuture<MoneyRequestEntity> confirmRequest({
    required String? billRefNo,
    required String? pin,
  });

  ResultFuture<WalletEntity> getWalletDetail();

  ResultFuture<MoneyRequestEntity> sendMoneyRequest({
    required double amount,
    required String transactionType,
    required String currency,
    required String memberId,
    String? reason,
  });
}
