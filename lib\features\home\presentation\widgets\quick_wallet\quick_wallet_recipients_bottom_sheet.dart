import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_error_retry.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/data/models/transaction_with_avatar.dart';
import 'package:cbrs/features/transactions/domain/entities/quick_wallet_detail_entity.dart';
import 'package:cbrs/features/transactions/presentation/views/quick%20wallet%20transfer/add_amount_quick_wallet.dart';
import 'package:cbrs/features/transactions/presentation/views/quick%20wallet%20transfer/widgets/quick_wallet_transfer_wallet_selection_modal.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/recent_wallet_transfer_hive.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';

class QuickWalletRecipientsBottomSheet extends StatefulWidget {
  const QuickWalletRecipientsBottomSheet({super.key});

  @override
  State<QuickWalletRecipientsBottomSheet> createState() =>
      _QuickWalletRecipientsBottomSheetState();
}

class _QuickWalletRecipientsBottomSheetState
    extends State<QuickWalletRecipientsBottomSheet> {
  // List<RecentWalletTransferHive>? recentWalletTransactions;

  @override
  void initState() {
    debugPrint('Fetttchug qauick wallet');
    super.initState();


  }

  void _fetchRecentRecipients() {
    debugPrint('ffetching recent recipents');

    context.read<RecentWalletTransferBloc>().add(
          const GetRecentWalletTransferEvent(limit: 10),
        );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomSheetHeight = screenHeight * 0.65;

    return BlocBuilder<RecentWalletTransferBloc, WalletTransferState>(
      builder: (context, state) => Container(
        padding: EdgeInsets.only(
          top: 20.h,
          bottom: MediaQuery.of(context).padding.bottom + 20.h,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 64.w,
              height: 2.h,
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 19, 12, 12),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            SizedBox(height: 16.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quick Wallet Transfer',
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Instantly transfer funds from your wallet to previously sent relatives with ease.',
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFFAAAAAA),
                    ),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                ],
              ),
            ),
            LayoutBuilder(
              builder: (context, constraints) {
                return ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.5,
                  ),
                  child: state is WalletTransferLoading
                      ? _buildShimmerLoading()
                      : state is LoadedRecentWalletTransferState
                          ? state is WalletTransferError
                              ? SizedBox(
                                  height:
                                      MediaQuery.of(context).size.height * 0.25,
                                  child: CustomErrorRetry(
                                    onTap: _fetchRecentRecipients,
                                  ),
                                )
                              : _buildRecipientsList(
                                  state.recentWalletTransactions,
                                )
                          :
                          //  _buildRecipientsList( WalletTransferError

                          const SizedBox.shrink(),
                );
              },
            ),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecipientsList(List<RecentWalletTransferHive> recipients) {
    return ListView.separated(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      shrinkWrap: true,
      itemCount: recipients.length,
      physics: const ClampingScrollPhysics(),
      separatorBuilder: (context, index) => SizedBox(height: 8.h),
      itemBuilder: (context, index) {
        final transfer = recipients[index];

        final name = transfer.recipientName ?? '';
        final phone = transfer.recipientPhone ?? '';
        final email = transfer.recipientEmail ?? '';

        final avatarUrl = transfer.avatar;
        //transfer.avatar ?? '';

        debugPrint('avataar $avatarUrl');
        final date = transfer.createdAt;

        return _buildRecipientTile(
          RecentRecipient(
            id: transfer.recipientId ?? '',
            name: name,
            phone: phone,
            date: date,
            email: email,
            avatarUrl: avatarUrl,
          ),
          context,
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            MediaRes.transferIcon,
            width: 120.w,
            height: 120.h,
          ),
          SizedBox(height: 16.h),
          Text(
            'No recent recipients found',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Start a new wallet transfer to see recipients here',
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          CustomButton(
            text: 'Start New Transfer',
            onPressed: () {
              Navigator.pop(context);
              context.pushNamed(AppRouteName.walletTransferPage);
            },
            options: CustomButtonOptions(
              width: 180.w,
              padding: EdgeInsets.symmetric(vertical: 12.h),
              color: Theme.of(context).primaryColor,
              textStyle: GoogleFonts.outfit(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              borderRadius: BorderRadius.circular(32.r),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerLoading() {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: 5,
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Shimmer.fromColors(
            baseColor: const Color(0xFFE8ECF4),
            highlightColor: const Color(0xFFF5F7FA),
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
              child: Row(
                children: [
                  // Avatar shimmer with better styling
                  Container(
                    width: 52.w,
                    height: 52.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.04),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16.w),

                  // Content area with varying widths for realistic shimmer
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          height: 17.h,
                          width: _getNameWidth(index),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                        ),
                        SizedBox(height: 8.h),
                        Container(
                          height: 14.h,
                          width: 100.w - (index % 3) * 10.w,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(3.r),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Date and chevron with improved styling
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        height: 14.h,
                        width: 75.w + (index % 3) * 5.w,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(3.r),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Icon(
                        Icons.chevron_right_rounded,
                        size: 24.r,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  double _getNameWidth(int index) {
    // Varies the width for a more natural shimmer effect
    final baseWidth = 120.w;
    final variation = (index % 3) * 20.w;
    return baseWidth + variation;
  }

  Widget _buildRecipientTile(RecentRecipient recipient, BuildContext context) {
    return InkWell(
      onTap: () => _navigateToQuickTransfer(context, recipient),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).primaryColor,
              ),
              clipBehavior: Clip.antiAlias,
              child: recipient.avatarUrl.isNotEmpty
                  ? CustomCachedImage(
                      url: recipient.avatarUrl,
                    )
                  : _buildInitialsAvatar(recipient, context),
            ),
            SizedBox(width: 8.w),

            // Name and phone number
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomBuildText(
                    text: recipient.name,
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    recipient.phone,
                    style: GoogleFonts.outfit(
                      color: Colors.black.withOpacity(0.4),
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      letterSpacing: 0.1,
                    ),
                  ),
                ],
              ),
            ),

            // Date and chevron
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  DateFormat('MMM d, yyyy').format(recipient.date),
                  style: GoogleFonts.outfit(
                    color: Colors.black.withOpacity(0.4),
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                SizedBox(width: 16.w),
                Image.asset(
                  MediaRes.forwardIcon,
                  width: 20,
                  color: Colors.black.withOpacity(0.4),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialsAvatar(RecentRecipient recipient, BuildContext context) {
    return Center(
      child: Text(
        recipient.name.isNotEmpty
            ? recipient.name
                .substring(0, min(2, recipient.name.length))
                .toUpperCase()
            : '??',
        style: GoogleFonts.outfit(
          color: Colors.white,
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _navigateToQuickTransfer(
    BuildContext context,
    RecentRecipient recipient,
  ) {
    // Close the bottom sheet first
    Navigator.pop(context);

    // Access UserBloc to get wallet data
    // final userState = context.read<UserBloc>().state;


if(6>9) {
    // if (userState is UserLoaded) {
    //   _showWalletSelectionModal(context, userState.user.wallets, recipient);
    
    } else {
      // If user data isn't loaded yet, show a message and use the fallback method
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Loading wallet information...')),
      );

      // Navigate to the original quick wallet transfer screen as fallback
      context.pushNamed(
        AppRouteName.quickWalletTransfer,
        extra: {
          'recipientName': recipient.name,
          'recipientPhone': recipient.phone,
          'recipientEmail': '', // Add email if available
          'senderName': '', // Get from user profile
        },
      );
    }
  }

  void _showWalletSelectionModal(
    BuildContext context,
    List<dynamic> wallets,
    RecentRecipient recipient,
  ) {
    // Convert wallets to WalletEntity format for the modal
    final walletDetails = wallets.map((wallet) {
      // Handle the dynamic balance value
      var balanceValue = 0.0;
      if (wallet.balance != null) {
        if (wallet.balance is double) {
          balanceValue = wallet.balance as double;
        } else if (wallet.balance is int) {
          balanceValue = (wallet.balance as int).toDouble();
        } else {
          try {
            balanceValue = double.parse(wallet.balance.toString());
          } catch (e) {
            balanceValue = 0.0;
          }
        }
      }

      return QuickWalletDetailEntity(
        currency: wallet.currency?.toString() ?? '',
        balance: balanceValue,
        id: wallet.walletCode?.toString() ?? '',
        isSelected: false,
      );
    }).toList();

    final walletEntity = QuickWalletEntity(wallets: walletDetails);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
        ),
        child: QuickWalletTransferWalletSelectionModal(
          wallet: walletEntity,
          recipientName: recipient.name,
          recipientEmail: recipient.email,
          recipientPhone: recipient.phone,
          recipientAvatar: recipient.avatarUrl,
          onWalletSelected: (selectedWallet) {
            Navigator.pop(context);
            GlobalVariable.currentlySelectedWallet = selectedWallet.currency;

            AddAmountQuickWallet.show(
              context,
              walletBalance: selectedWallet.balance,
              recipientEmail: recipient.email,
              recipientPhone: recipient.phone,
              recipientAvatar: recipient.avatarUrl,
              recipientName: recipient.name,
              senderName: '',
              currency: selectedWallet.currency,
            );
          },
        ),
      ),
    );
  }

  int min(int a, int b) => a < b ? a : b;
}

class RecentRecipient {
  RecentRecipient({
    required this.id,
    required this.name,
    required this.phone,
    required this.date,
    this.email,
    this.avatarUrl = '',
  });
  final String id;
  final String name;
  final String phone;
  final String? email;
  final DateTime date;
  final String avatarUrl;
}
