import 'dart:ui';

import 'package:cbrs/features/home/<USER>/widgets/home_section_headers.dart';
import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';

class FinancialServicesSection extends StatelessWidget {
  const FinancialServicesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const HomeSectionHeaders(
                title: 'Financial Services',
                description:
                    'Explore loan options and send gifts to loved ones.',
              ),
              Sized<PERSON><PERSON>(height: 16.h),
              IntrinsicHeight(
                child: Row(
                  children: [
                    Expanded(
                      child: _buildFinancialServiceCard(
                        title: 'Car Loan',
                        description:
                            'Check out the available cars for loan and apply for financing today.',
                        onTap: () =>
                            context.pushNamed(AppRouteName.carLoanRoute),
                        bgImage: MediaRes.carLoanBg,
                        context: context,
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildFinancialServiceCard(
                        title: 'Mortgage Loan',
                        description:
                            'Browse a list of apartments & apply for loans easily.',
                        onTap: () =>
                            context.pushNamed(AppRouteName.mortgageRoute),
                        bgImage: MediaRes.homeLoanBg,
                        context: context,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),
              _buildFinancialServiceCard(
                title: 'Gift Packages',
                description:
                    'Browse gift packages and send meaningful gifts to loved ones.',
                onTap: () => context.pushNamed(AppRouteName.giftPackages),
                bgImage: MediaRes.giftBg,
                context: context,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialServiceCard({
    required String title,
    required String description,
    required String bgImage,
    required VoidCallback onTap,
    required BuildContext context,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: Stack(
          children: [
            // Background image
            Image.asset(
              bgImage,
              height: 155.h,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
            // Glassmorphism overlay (bottom 40% + 28px)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              height: (155.h * 0.4) + 28, // Bottom 40% + 28px
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r),
                ),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.05),
                          Colors.black.withOpacity(0.3),
                        ],
                      ),
                    ),
                    padding: EdgeInsets.all(12.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: GoogleFonts.outfit(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          description,
                          style: GoogleFonts.outfit(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w300,
                            color: Colors.white.withOpacity(0.9),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
