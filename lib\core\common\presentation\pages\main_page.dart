import 'dart:async';

import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/presentation/widgets/main_bottom_nav_bar.dart';
import 'package:cbrs/core/services/firebase_notification_service/fcm_service.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/home/<USER>/views/home_screen.dart';
import 'package:cbrs/features/mini_apps/application/bloc/miniapp_bloc.dart';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_screen.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_bloc.dart';
import 'package:cbrs/features/my_loan/presentation/views/screen_my_loan.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
import 'package:cbrs/features/profile/presentation/views/profile_screen.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/presentation/views/transactions_screen.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_recent_transaction_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key, this.extra});
  final Map<String, dynamic>? extra;

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> with WidgetsBindingObserver {
  late int _currentIndex;
  Timer? _backgroundTimer;
  static const sessionTimeout = Duration(minutes: 2);
  DateTime? _backgroundStartTime;
  Timer? _tabIndexWatcher;

  final Map<int, Widget> _cachedWidgets = {};
  bool isDollarAccount = true;

  late List<Widget Function(BuildContext)> _widgetBuilders;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize from GlobalVariable if set, otherwise from extra (for backward compatibility)
    if (GlobalVariable.currentTabIndex > 0) {
      _currentIndex = GlobalVariable.currentTabIndex;
    } else {
      _currentIndex = widget.extra?['initialTab'] as int? ?? 0;
      // Update GlobalVariable to match
      GlobalVariable.currentTabIndex = _currentIndex;
    }

    debugPrint('MainPage initialized with index: $_currentIndex');

    // Watch for changes to GlobalVariable.currentTabIndex
    _tabIndexWatcher = Timer.periodic(const Duration(milliseconds: 300), (_) {
      if (GlobalVariable.currentTabIndex != _currentIndex) {
        setState(() {
          _currentIndex = GlobalVariable.currentTabIndex;
          // Pre-cache the widget if needed
          _cachedWidgets.putIfAbsent(
            _currentIndex,
            () => _widgetBuilders[_currentIndex](context),
          );
        });
      }
    });

    _widgetBuilders = [
      (_) => const HomeScreen(),
      (context) {
        // final state = context.read<HomeBloc>().state;
        // final isDollarAccount = state is HomeLoaded && state.data.selectedWallet == 'USD';
        // debugPrint('Tab 1 - isDollarAccount: $isDollarAccount');
        // return isDollarAccount ?
        return GlobalVariable.currentlySelectedWallet == 'USD'
            ? const ScreenMyLoan()
            : const MiniAppScreen();
      },
      (_) => const TransactionsScreen(),
      (_) => const ProfileScreen(),
    ];

    // Pre-cache the initial tab's widget
    _cachedWidgets[_currentIndex] = _widgetBuilders[_currentIndex](context);
  }

  // @override
  // void initState() {
  //   super.initState();
  //   WidgetsBinding.instance.addObserver(this);
  //   _currentIndex = widget.extra?['initialTab'] as int? ?? 0;
  //   debugPrint('MainPage initialized with index: $_currentIndex');
  // }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _backgroundTimer?.cancel();
    _tabIndexWatcher?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      _startBackgroundTimer();
      updateUserStatus(isOnline: false);
    } else if (state == AppLifecycleState.resumed) {
      updateUserStatus(isOnline: true);
      _checkSessionTimeout();
    }
  }

  void _startBackgroundTimer() {
    _backgroundStartTime = DateTime.now();
    _backgroundTimer = Timer(sessionTimeout, () {
      context.read<AuthBloc>().add(const LogoutEvent());
    });
  }

  void _checkSessionTimeout() {
    _backgroundTimer?.cancel();

    if (_backgroundStartTime != null) {
      final timeInBackground = DateTime.now().difference(_backgroundStartTime!);
      if (timeInBackground >= sessionTimeout) {
        context.read<AuthBloc>().add(const LogoutEvent());
      }
    }
    _backgroundStartTime = null;
  }

  // void _onItemTapped(int index) {
  //   debugPrint('Tab tapped: $index');
  //   setState(() => _currentIndex = index);
  // }

  void _onItemTapped(int index) {
    debugPrint('Tab tapped: $index');
    setState(() {
      _currentIndex = index;
      // Update the global variable
      GlobalVariable.currentTabIndex = index;
      // Instantiate widget if not cached
      _cachedWidgets.putIfAbsent(index, () => _widgetBuilders[index](context));
    });

    if (index == 3) {
      debugPrint('fetch new transaction');
      sl<TransactionBloc>().add(
        const FetchTransactionsEvent(
          page: 1,
          perPage: 10,
          keepCache: false,
        ),
      );
    }
  }

  void _onDoubleTap(int index) {
    debugPrint(' _onDoubleTap Tab tapped: $index');
    if (index == 0) {
      setState(() => _currentIndex = index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: MultiBlocProvider(
        providers: [
          BlocProvider<HomeBloc>(
            create: (context) => sl<HomeBloc>(),

            //  {
            //   debugPrint('Creating HomeBloc in MainPage');
            //   final bloc = GetIt.I<HomeBloc>();
            //   bloc.add(LoadHomeDataEvent());
            //   return bloc;
            // },
          ),
          BlocProvider(
            create: (context) => sl<WalletBalanceBloc>(),
          ),
          BlocProvider<TransactionBloc>(
            create: (context) => GetIt.I<TransactionBloc>(),
          ),
          BlocProvider<MiniappBloc>(
            create: (context) => GetIt.I<MiniappBloc>(),
          ),
          BlocProvider(
            create: (context) => sl<RepaymentBloc>(),
          ),
          BlocProvider(
            create: (context) => sl<ProfileBloc>(),
          ),
          BlocProvider(
            create: (context) => sl<RecentWalletTransferBloc>(),
          ),
        ],
        child: BlocConsumer<WalletBalanceBloc, HomeState>(
          listener: (context, state) {
            debugPrint(
              'MainPage - HomeBloc State Changed: ${state.runtimeType}',
            );
            if (state is WalletLoadedState) {
              if (_cachedWidgets.containsKey(1)) {
                isDollarAccount = state.isUsdWallet;
                setState(() {
                  _cachedWidgets[1] = isDollarAccount
                      ? const ScreenMyLoan()
                      : const MiniAppScreen();
                });
              }
            }
          },
          builder: (context, state) {
            final pages = List.generate(_widgetBuilders.length, (index) {
              return _cachedWidgets[index] ?? Container();
            });

            return BlocBuilder<HomeBloc, HomeState>(
              builder: (context, state) {
                return BlocListener<AuthBloc, AuthState>(
                  listener: (context, state) {
                    if (state is LoggedOutState) {
                      context.go(AppRouteName.tokenDeviceLogin);
                    }
                  },
                  
                  child: Scaffold(
                    body: IndexedStack(
                      index: _currentIndex,
                      children: pages,
                    ),
                    bottomNavigationBar: MainBottomNavBar(
                      currentIndex: _currentIndex,
                      onTap: _onItemTapped,
                      onDoubleTap: _onDoubleTap,
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
