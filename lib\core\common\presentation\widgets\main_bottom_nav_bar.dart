import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/extensions/context_extensions.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/my_connect/presentation/views/my_connect_page.dart';

class MainBottomNavBar extends StatefulWidget {
  const MainBottomNavBar({
    required this.currentIndex,
    required this.onTap,
    required this.onDoubleTap,
    super.key,
  });
  final int currentIndex;
  final void Function(int) onTap;
  final void Function(int) onDoubleTap;

  @override
  State<MainBottomNavBar> createState() => _MainBottomNavBarState();
}

class _MainBottomNavBarState extends State<MainBottomNavBar> {
  // Add a helper method for handling tab changes directly
  void _handleTabChange(int index) {
    GlobalVariable.currentTabIndex = index;
    widget.onTap(index);
  }

  bool isDollarAccount = true;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WalletBalanceBloc, HomeState>(
      listener: (context, state) {
        if (state is WalletLoadedState) {
          setState(() {
            isDollarAccount = state.isUsdWallet;
          });
        }
      },

      // selector: (state) => state is WalletLoadedState && state.isUsdWallet,
      builder: (context, state) {
        return SafeArea(
          top: false,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              ClipPath(
                child: Container(
                  height: 72.h,
                  padding: EdgeInsets.only(
                    left: 8.w,
                    right: 8.w,
                    top: 4.h,
                    bottom: 4.h,
                  ),
                  decoration: BoxDecoration(
                    color: context.theme.cardColor,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        blurRadius: 4,
                        offset: const Offset(0, -6),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildNavItem(
                        context,
                        HugeIcons.strokeRoundedHome02,
                        HugeIcons.strokeRoundedHome02,
                        'Home',
                        0,
                        hasCustomIcon: true,
                        customActiveIconLocation: MediaRes.activeHome,
                        customInActiveIconLocation: MediaRes.inactiveHome,
                      ),
                      _buildNavItem(
                        context,
                        isDollarAccount
                            ? HugeIcons.strokeRoundedPlaySquare
                            : HugeIcons.strokeRoundedAppStore,
                        isDollarAccount
                            ? HugeIcons.strokeRoundedPlaySquare
                            : HugeIcons.strokeRoundedAppStore,
                        isDollarAccount ? 'My Loans' : 'Mini Apps',
                        1,
                        hasCustomIcon: true,
                        customActiveIconLocation: isDollarAccount
                            ? MediaRes.activeLoanIcon
                            : MediaRes.activeMiniApps,
                        customInActiveIconLocation: isDollarAccount
                            ? MediaRes.inactiveLoanIcon
                            : MediaRes.inactiveMiniApps,
                      ),
                      // Spacer for center button
                      SizedBox(width: 80.w),
                      _buildNavItem(
                        context,
                        FluentIcons.history_24_regular,
                        FluentIcons.history_24_regular,
                        'Transactions',
                        2,
                        hasCustomIcon: true,
                        customActiveIconLocation: MediaRes.activeTransaction,
                        customInActiveIconLocation:
                            MediaRes.inactiveTransactionc,
                      ),
                      _buildNavItem(
                        context,
                        FluentIcons.person_24_regular,
                        FluentIcons.person_24_regular,
                        'Profile',
                        3,
                        hasCustomIcon: true,
                        customActiveIconLocation: MediaRes.activePerson,
                        customInActiveIconLocation: MediaRes.inactivePerson,
                      ),
                    ],
                  ),
                ),
              ),
              // Center floating button positioned in the notch
              Positioned(
                left: 0,
                right: 0,
                top: -15.h,
                child: Center(
                  child: _buildCenterButton(context),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    IconData icon,
    IconData activeIcon,
    String label,
    int index, {
    bool hasCustomIcon = false,
    String customActiveIconLocation = '',
    String customInActiveIconLocation = '',
  }) {
    final isSelected = widget.currentIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: () => _handleTabChange(index),
        onDoubleTap: () => widget.onDoubleTap(index),
        behavior: HitTestBehavior.translucent,
        child: Container(
          padding: const EdgeInsets.only(top: 8, bottom: 8),
          // color: Colors.red,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: hasCustomIcon
                    ? ShaderMask(
                        shaderCallback: (Rect bounds) {
                          if (!isSelected) {
                            return LightModeTheme()
                                .grayGradient
                                .createShader(bounds);
                          }
                          return isDollarAccount
                              ? LightModeTheme()
                                  .usdGradient
                                  .createShader(bounds)
                              : LightModeTheme()
                                  .primaryGradient
                                  .createShader(bounds);
                        },
                        child: Image.asset(
                          width: 24.w,
                          height: 24.h,
                          isSelected
                              ? customActiveIconLocation
                              : customInActiveIconLocation,
                          color: Colors.white,
                          key: ValueKey(isSelected),
                        ),
                      )
                    : ShaderMask(
                        shaderCallback: (Rect bounds) {
                          if (!isSelected) {
                            return LightModeTheme()
                                .grayGradient
                                .createShader(bounds);
                          }
                          return isDollarAccount
                              ? LightModeTheme()
                                  .usdGradient
                                  .createShader(bounds)
                              : LightModeTheme()
                                  .primaryGradient
                                  .createShader(bounds);
                        },
                        child: Icon(
                          isSelected ? activeIcon : icon,
                          size: 24,
                          color: Colors.white,
                          key: ValueKey(isSelected),
                        ),
                      ),
              ),
              const SizedBox(height: 4),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color: isSelected
                      ? isDollarAccount
                          ? LightModeTheme().primaryColorUSD
                          : LightModeTheme().primaryColor
                      : const Color(0xFFCACACA),
                ),
                child: Text(label),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCenterButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Navigate to MyConnectPage when center button is pressed
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => MyConnectPage(),
          ),
        );
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Positioned(
          //   top: 0,
          //   child: Container(
          //     width: 80.w,
          //     height: 80.h,
          //     decoration: BoxDecoration(
          //       color: context.theme.cardColor,
          //       border: const Border(
          //         left: BorderSide(color: Colors.white, width: 3),
          //         right: BorderSide(color: Colors.white, width: 3),
          //         top: BorderSide(color: Colors.white, width: 3),
          //       ),
          //       borderRadius: BorderRadius.only(
          //         topLeft: Radius.circular(80.r),
          //         topRight: Radius.circular(80.r),
          //       ),
          //     ),
          //   ),
          // ),
          // Main circular button
          Container(
            width: 60.w,
            height: 60.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: isDollarAccount
                  ? LightModeTheme().usdGradient
                  : LightModeTheme().primaryGradient,
            ),
            child: Center(
              child: SvgPicture.asset(
                MediaRes.connectSvg,
                width: 40.w,
                height: 40.h,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
