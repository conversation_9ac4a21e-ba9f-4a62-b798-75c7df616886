part of 'miniapp_bloc.dart';

abstract class MiniappEvent extends Equatable {
  const MiniappEvent();

  @override
  List<Object?> get props => [];
}

class GettingMiniappEvent extends MiniappEvent {
  const GettingMiniappEvent({
    required this.page,
    required this.perPage,
    required this.stage,
  });

  final int page;
  final int perPage;
  final String stage;

  @override
  List<Object?> get props => [page, perPage, stage];
}

class CreatingOrderMiniappEvent extends MiniappEvent {
  const CreatingOrderMiniappEvent({
    required this.data,
  });

  final dynamic data;

  @override
  List<Object?> get props => [];
}

class SubmitPinMiniappEvent extends MiniappEvent {
  const SubmitPinMiniappEvent({
    required this.transactionType,
    required this.billRefNo,
    required this.pin,
  });

  final String transactionType;
  final String billRefNo;
  final String pin;

  @override
  List<Object?> get props => [transactionType, billRefNo, pin];
}

class SubmitOtpMiniappEvent extends MiniappEvent {
  const SubmitOtpMiniappEvent({
    required this.transactionType,
    required this.billRefNo,
    required this.otp,
  });

  final String transactionType;
  final String billRefNo;
  final String otp;

  @override
  List<Object?> get props => [transactionType, billRefNo, otp];
}



/*
class LoadUtilitiesEvent extends MiniappEvent {
  final int page;
  final int perPage;
  final String stage;

  const LoadUtilitiesEvent({
    required this.page,
    required this.perPage,
    required this.stage,
  });

  @override
  List<Object?> get props => [page, perPage, stage];
}

class ProcessUtilityPaymentEvent extends MiniappEvent {
  final Map<String, dynamic> request;

  const ProcessUtilityPaymentEvent({required this.request});

  @override
  List<Object?> get props => [request];
}

class SubmitMiniappPinEvent extends MiniappEvent {
  final String pin;
  final String billRefNo;
  // final String transactionType;

  const SubmitMiniappEventPinEvent({
    required this.pin,
    required this.billRefNo,
    // required this.transactionType,
  });

  @override
  List<Object?> get props => [pin, billRefNo];
}

class GetMiniappTransactionEvent extends MiniappEvent {
  final String transactionId;

  const GetUtilityTransactionEvent({required this.transactionId});

  @override
  List<Object?> get props => [transactionId];
}
*/