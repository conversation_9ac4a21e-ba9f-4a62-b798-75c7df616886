import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp_success_entity.dart';

class MiniappSuccessModel extends MiniappSuccessEntity {
  const MiniappSuccessModel({
    required super.statusCode,
    required super.success,
    required MiniappSuccessDataModel super.data,
  });

  factory MiniappSuccessModel.fromJson(Map<String, dynamic> json) {
    return MiniappSuccessModel(
      statusCode: AppMapper.safeInt(json['statusCode']),
      success: AppMapper.safeBool(json['success']),
      data: MiniappSuccessDataModel.fromJson(AppMapper.safeMap(json['data'])),
    );
  }


}

class MiniappSuccessDataModel extends MiniappSuccessDataEntity {
  MiniappSuccessDataModel({
    required super.id,
    required String? typeId,
    required super.senderId,
    required String? customerName,
    required super.senderName,
    required super.senderPhone,
    required super.senderEmail,
    required super.transactionOwner,
    required String? sessionID,
    required String? cardNumber,
    required String? redirectURL,
    required String? mpgsReference,
    required String? beneficiaryId,
    required String? beneficiaryName,
    required String? beneficiaryPhone,
    required String? beneficiaryAccountNo,
    required String? beneficiaryEmail,
    required String? bankName,
    required String? bankCode,
    required String? bankId,
    required String? bankLogo,
    required String? senderAvatar,
    required String? beneficiaryAvatar,
    required String? senderConnectCode,
    required String? beneficiaryConnectCode,
    required super.transactionType,
    required String? orderId,
    required String? giftPackageId,
    required super.merchantId,
    required super.merchantType,
    required String? paidUsing,
    required String? totalGiftPackageQty,
    required String? packageDiscountLevel,
    required String? packageDiscountId,
    required String? packageCampaignId,
    required super.billAmount,
    required super.originalCurrency,
    required super.serviceCharge,
    required super.vat,
    required super.totalAmount,
    required super.changedCurrency,
  }) : super(
    typeId: typeId ?? '',
    customerName: customerName ?? '',
    sessionID: sessionID ?? '',
    cardNumber: cardNumber ?? '',
    redirectURL: redirectURL ?? '',
    mpgsReference: mpgsReference ?? '',
    beneficiaryId: beneficiaryId ?? '',
    beneficiaryName: beneficiaryName ?? '',
    beneficiaryPhone: beneficiaryPhone ?? '',
    beneficiaryAccountNo: beneficiaryAccountNo ?? '',
    beneficiaryEmail: beneficiaryEmail ?? '',
    bankName: bankName ?? '',
    bankCode: bankCode ?? '',
    bankId: bankId ?? '',
    bankLogo: bankLogo ?? '',
    senderAvatar: senderAvatar ?? '',
    beneficiaryAvatar: beneficiaryAvatar ?? '',
    senderConnectCode: senderConnectCode ?? '',
    beneficiaryConnectCode: beneficiaryConnectCode ?? '',
    orderId: orderId ?? '',
    giftPackageId: giftPackageId ?? '',
    paidUsing: paidUsing ?? '',
    totalGiftPackageQty: totalGiftPackageQty ?? '',
    packageDiscountLevel: packageDiscountLevel ?? '',
    packageDiscountId: packageDiscountId ?? '',
    packageCampaignId: packageCampaignId ?? '',
  );

  factory MiniappSuccessDataModel.fromJson(Map<String, dynamic> json) {
    return MiniappSuccessDataModel(

      id: AppMapper.safeString(json['id']),
      typeId: AppMapper.safeString(json['typeId']),
      senderId: AppMapper.safeString(json['senderId']),
      customerName: AppMapper.safeString(json['customerName']),
      senderName: AppMapper.safeString(json['senderName']),
      senderPhone: AppMapper.safeString(json['senderPhone']),

      senderEmail: AppMapper.safeString(json['senderEmail']),
      transactionOwner: AppMapper.safeString(json['transactionOwner']),
      sessionID: AppMapper.safeString(json['sessionID']),
      cardNumber: AppMapper.safeString(json['cardNumber']),
      redirectURL: AppMapper.safeString(json['redirectURL']),
      mpgsReference: AppMapper.safeString(json['mpgsReference']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      beneficiaryPhone: AppMapper.safeString(json['beneficiaryPhone']),
      beneficiaryAccountNo: AppMapper.safeString(json['beneficiaryAccountNo']),
      beneficiaryEmail: AppMapper.safeString(json['beneficiaryEmail']),
      bankName: AppMapper.safeString(json['bankName']),
      bankCode: AppMapper.safeString(json['bankCode']),
      bankId: AppMapper.safeString(json['bankId']),
      bankLogo: AppMapper.safeString(json['bankLogo']),
      senderAvatar: AppMapper.safeString(json['senderAvatar']),
      beneficiaryAvatar: AppMapper.safeString(json['beneficiaryAvatar']),
      senderConnectCode: AppMapper.safeString(json['senderConnectCode']),
      beneficiaryConnectCode: AppMapper.safeString(json['beneficiaryConnectCode']),
      transactionType: AppMapper.safeString(json['transactionType']),
      orderId: AppMapper.safeString(json['orderId']),
      giftPackageId: AppMapper.safeString(json['giftPackageId']),
      merchantId: AppMapper.safeString(json['merchantId']),

      merchantType: AppMapper.safeString(json['merchantType']),
      paidUsing: AppMapper.safeString(json['paidUsing']),
      totalGiftPackageQty: AppMapper.safeString(json['totalGiftPackageQty']),
      packageDiscountLevel: AppMapper.safeString(json['packageDiscountLevel']),
      packageDiscountId: AppMapper.safeString(json['packageDiscountId']),
      packageCampaignId: AppMapper.safeString(json['packageCampaignId']),


      billAmount: AppMapper.safeDouble(json['billAmount']),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      serviceCharge: AppMapper.safeDouble(json['serviceCharge']),
      vat: AppMapper.safeDouble(json['vat']),
      totalAmount: AppMapper.safeDouble(json['totalAmount']),

      changedCurrency: AppMapper.safeString(json['changedCurrency']),

    );
  }



}
