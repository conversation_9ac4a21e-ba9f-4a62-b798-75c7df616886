import 'package:cbrs/core/common/widgets/custom_eth_switch_card.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/localization/controllers/language_controller.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/domain/entities/user.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_event.dart';
import 'package:cbrs/features/profile/presentation/widgets/custom_dot_line.dart';
import 'package:cbrs/features/profile/presentation/widgets/custom_section_title.dart';
import 'package:cbrs/features/profile/presentation/widgets/custom_shadow_container.dart';
import 'package:cbrs/features/profile/presentation/widgets/logout_device_confirmation.dart';
import 'package:cbrs/features/profile/presentation/widgets/profile_list_tile.dart';
import 'package:cbrs/features/profile/presentation/widgets/unlink_device_confirmation.dart';
import 'package:cbrs/features/profile/presentation/widgets/user_profile_container.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileViewState();
}

class _ProfileViewState extends State<ProfileScreen> {
  final LanguageController languageController = Get.find();

  LocalUser? userDto;
  @override
  void initState() {
    super.initState();
    _fetchLocalUserData();
    setState(() {});
  }

  // user local data

  Future<void> _fetchLocalUserData() async {
    final user = await sl<AuthLocalDataSource>().getCachedUserData();

    setState(() {
      userDto = user;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      appBar: AppBar(
        title: I18nText(
          'common.profile.profile',
        ),
        titleSpacing: 22.w,
        automaticallyImplyLeading: false,
      ),
      body: BlocConsumer<ProfileBloc, ProfileState>(
        listener: (context, state) {
          if (state is ProfileLoading) {
            // const Center(child: CircularProgressIndicator());
          } else if (state is LoggedOutState) {
            context.goNamed(AppRouteName.tokenDeviceLogin);
          } else if (state is ProfilePictureDeleteComplete) {
            const Center(child: CircularProgressIndicator());
          }
        },
        builder: (context, state) {
          return _buildProfileBody(context);
        },
      ),
    );
  }

  Widget _buildProfileBody(
    BuildContext context,
  ) {
    final hasPhone = userDto?.phoneNumber?.isNotEmpty ?? false;
    final hasEmail = userDto?.email?.isNotEmpty ?? false;

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      physics: const ClampingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///TODO - uncomment
          if (userDto != null)
            UserProfileContainer(
              userDto: userDto!,
            ),

          SizedBox(height: 16.h),
          const CustomSectionTitle(
            title: 'common.profile.account_info.account_information',
          ),
          SizedBox(height: 8.h),
          CustomShadowContainer(
            child: Column(
              children: [
                /*"common.profile.profile",*/
                ProfileListTile(
                  icon: Icons.person_outline,
                  title: 'common.profile.account_info.profile_info',
                  onTap: () async {
                    final x = await context.pushNamed(
                      AppRouteName.profileInfo,
                    );
                  },
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  height: 1.h,
                  child: CustomPaint(
                    painter: DottedLinePainter(),
                    size: const Size(double.infinity, 1),
                  ),
                ),
                ProfileListTile(
                  icon: Icons.lock_outline,
                  title: 'common.profile.account_info.change_pin',
                  onTap: () => context.pushNamed(
                    AppRouteName.updatePin,
                  ),
                ),
                const CustomDotLine(),

                ///TODO - uncomment
                _buildProfileTile(hasEmail, hasPhone),
                const CustomDotLine(),
                ProfileListTile(
                  icon: Icons.language_outlined,
                  title: 'common.profile.account_info.language',
                  onTap: () {
                    showLanguageSelectionBottomSheet(context);
                  },
                ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          const CustomSectionTitle(
            title: 'common.profile.faq_and.faq_and_legacy',
          ),
          SizedBox(height: 12.h),
          CustomShadowContainer(
            child: Column(
              children: [
                ProfileListTile(
                  icon: Icons.support_agent_outlined,
                  title: 'common.profile.faq_and.customer_support',
                  onTap: () => context.pushNamed(AppRouteName.customerSupport),
                ),
                const CustomDotLine(),
                ProfileListTile(
                  icon: Icons.help_outline,
                  title: 'common.profile.faq_and.faqs',
                  onTap: () => context.pushNamed(AppRouteName.faq),
                ),
                const CustomDotLine(),
                ProfileListTile(
                  icon: Icons.privacy_tip_outlined,
                  title: 'common.profile.faq_and.privacy_policy',
                  onTap: () => context.pushNamed(AppRouteName.privacyPolicy),
                ),
                const CustomDotLine(),
                ProfileListTile(
                  icon: Icons.description_outlined,
                  title: 'common.profile.faq_and.terms_and_conditions',
                  onTap: () =>
                      context.pushNamed(AppRouteName.termsAndConditions),
                ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          CustomShadowContainer(
            child: Column(
              children: [
                ProfileListTile(
                  icon: FluentIcons.phone_desktop_24_regular,
                  title: 'common.profile.faq_and.unlink_device',
                  onTap: () => _showUnlinkDeviceDialog(context),
                ),
                const CustomDotLine(),
                ProfileListTile(
                  icon: FluentIcons.sign_out_24_regular,
                  title: 'common.profile.faq_and.logout',
                  onTap: () => _showLogoutDialog(context),
                ),
              ],
            ),
          ),
          SizedBox(height: 56.h),
        ],
      ),
    );
  }

  Widget _buildEmailTile(bool hasEmail) {
    return ProfileListTile(
      icon: CupertinoIcons.mail,
      title: hasEmail ? (userDto?.email ?? '') : 'Email',
      onTap: () async {
        final x = await context.pushNamed(
          AppRouteName.addEmail,
          extra: {
            'email': userDto?.email ?? '',
          },
        );

        if (x == true) {
          _fetchLocalUserData();
//
          debugPrint(
            '\n\n\nNatty =========================================== Updated\n\n',
          );
          // setState(() {});
        }
      },
      hasExtraOption: true,
      extraText: hasEmail ? 'Verify Email' : 'Add Email',
    );
  }

  Widget _buildPhoneTile(bool hasPhone) {
    return ProfileListTile(
      icon: CupertinoIcons.phone,
      title: hasPhone ? (userDto?.phoneNumber ?? '') : 'Phone',
      onTap: () async {
        if (!(userDto?.isPhoneVerified ?? false)) {
          final x = await context.pushNamed(
            AppRouteName.addPhone,
            extra: {
              'phoneNumber': userDto?.phoneNumber ?? '',
            },
          );

          if (x == true) {
            _fetchLocalUserData();
          }
        }
      },
      hasExtraOption: true,
      isReadOnly: userDto?.isPhoneVerified ?? false,
      trailingIcon: userDto?.isPhoneVerified ?? false
          ? Icons.verified
          : Icons.arrow_forward_ios,
      extraText: userDto?.isPhoneVerified ?? false
          ? ''
          : hasPhone
              ? 'Verify Phone'
              : 'Add Phone',
    );
  }

  Widget _buildProfileTile(bool hasEmail, bool hasPhone) {
    return (!(userDto?.isEmailVerified ?? false))
        ? _buildEmailTile(hasEmail)
        : _buildPhoneTile(hasPhone);
  }

  // void showLanguageSelectionBottomSheet(BuildContext context) {
  void _showLogoutDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return BlocProvider.value(
          value: context.read<AuthBloc>(),
          child: const LogoutDeviceConfirmationDialog(),
        );
      },
    );
  }

  void _handleLogoutConfirm(BuildContext context) {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => WillPopScope(
        onWillPop: () async => false,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );

    // Only trigger logout (which now only removes access token)
    context.read<AuthBloc>().add(const LogoutEvent());
  }

  void _showUnlinkDeviceDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return BlocProvider.value(
          value: context.read<AuthBloc>(),
          child: const UnlinkDeviceConfirmationDialog(),
        );
      },
    );
  }

  void showLanguageSelectionBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        var langCode = '';
        var countryCode = '';
        final isSelected = languageController.selectedLanguage;

        // bool isSelected = languageController.selectedLanguage.value == langCode;

        return SafeArea(
          child: Container(
            // margin: EdgeInsets.fromLTRB(0.w, 0, 0.h, 10.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24.r),
            ),
            child: Obx(
              () => Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding:
                        EdgeInsets.only(right: 16.w, top: 20.h, left: 16.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Select Language',
                          textScaler: const TextScaler.linear(1),
                          style:
                              Theme.of(context).textTheme.titleMedium!.copyWith(
                                    fontSize: 22.sp,
                                  ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Image.asset(
                            'assets/images/close.png',
                            height: 20.h, // Maintain the same size as before
                            color:
                                Colors.grey.shade700, // Apply color if needed
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 12.h),
                  _buildLanguageTile(
                      context, 'English', 'en', 'US', isSelected.value == 'en',
                      () {
                    langCode = 'en';
                    countryCode = 'US';
                    isSelected.value = 'en';
                  }),
                  SizedBox(height: 8.h),
                  _buildLanguageTile(
                      context, 'አማርኛ', 'am', 'ET', isSelected.value == 'am',
                      () {
                    langCode = 'am';
                    countryCode = 'ET';
                    isSelected.value = 'am';
                  }),
                  SizedBox(height: 8.h),
                  _buildLanguageTile(context, 'Afaan Oromoo', 'om', 'ET',
                      isSelected.value == 'om', () {
                    langCode = 'om';
                    countryCode = 'ET';
                    isSelected.value = 'om';
                  }),
                  SizedBox(height: 8.h),
                  _buildLanguageTile(
                      context, 'ትግርኛ', 'ti', 'ET', isSelected.value == 'ti',
                      () {
                    langCode = 'ti';
                    countryCode = 'ET';
                    isSelected.value = 'ti';
                  }),
                  SizedBox(height: 8.h),
                  _buildLanguageTile(context, 'Af-soomaali', 'so', 'SO',
                      isSelected.value == 'so', () {
                    langCode = 'so';
                    countryCode = 'ET';
                    isSelected.value = 'so';
                  }),
                  SizedBox(height: 24.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.pop(context),
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(vertical: 16.w),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.r),
                                side: BorderSide(
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: GoogleFonts.outfit(
                                fontSize: 16.sp,
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () async {
                              if (langCode.isNotEmpty &&
                                  countryCode.isNotEmpty) {
                                await FlutterI18n.refresh(
                                  context,
                                  Locale(langCode),
                                );
                                setState(() {});

                                // SaveLanguageCode(langCode);
                              }
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              padding: EdgeInsets.symmetric(vertical: 16.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                            ),
                            child: Text(
                              'Done',
                              style: GoogleFonts.outfit(
                                fontSize: 16.sp,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLanguageTile(
    BuildContext context,
    String language,
    String langCode,
    String countryCode,
    bool isSelected,
    VoidCallback onTap,
  ) {
    // bool isSelected = languageController.selectedLanguage.value == langCode;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        padding: EdgeInsets.symmetric(horizontal: 24.0.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: const Color(0xFFF7F7F7),
          borderRadius: BorderRadius.circular(8.r),
          border: isSelected
              ? Border.all(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                )
              : null,
        ),
        child: Row(
          children: [
            Container(
              width: 20.w,
              height: 20.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected
                    ? Theme.of(context).primaryColor.withOpacity(0.8)
                    : Colors.white,
                border: Border.all(
                  color: isSelected ? Colors.transparent : Colors.grey.shade500,
                ),
              ),
              child: isSelected
                  ? Icon(Icons.check, color: Colors.white, size: 15.h)
                  : null,
            ),
            SizedBox(
              width: 8.w,
            ),
            Expanded(
              child: Text(
                language,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.black.withOpacity(0.5),
                      fontWeight: FontWeight.w800,
                      fontSize: 16.sp,
                    ),
                textScaler: const TextScaler.linear(1),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Permission Denied'),
          content: const Text(
            'Camera permission is required to take photos. Please enable it in settings.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
