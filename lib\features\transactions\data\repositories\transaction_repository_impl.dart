import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/transactions/data/datasources/transaction_local_data_source.dart';
import 'package:cbrs/features/transactions/data/datasources/transaction_remote_datasource.dart';
import 'package:cbrs/features/transactions/data/models/invoice-response_model.dart';
import 'package:cbrs/features/transactions/data/models/transfer_limit_model.dart';
import 'package:cbrs/features/transactions/data/models/transaction_with_avatar.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:cbrs/features/transactions/domain/entities/transfer_limit.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';
import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:cbrs/features/transactions/data/models/confirm_transfer_response_model.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';

class TransactionRepositoryImpl implements TransactionRepository {
  TransactionRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });
  final TransactionRemoteDataSource remoteDataSource;
  final TransactionLocalDataSource localDataSource;

  @override
  Future<Either<Failure, List<Transaction>>> getTransactions({
    required int page,
    required int perPage,
    String? transactionType,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final result = await remoteDataSource.getTransactions(
        page: page,
        perPage: perPage,
        transactionType: transactionType,
        startDate: startDate,
        endDate: endDate,
      );

      await localDataSource.cacheTransactions(result);

      return Right(result);
    } on ApiException catch (e) {
      try {
        final cachedTransactions =
            await localDataSource.getCachedTransactions();

        return Right(cachedTransactions);
      } catch (_) {
        return Left(ServerFailure(message: e.message));
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Transaction>> getTransactionDetails(String id) async {
    try {
      final transaction = await remoteDataSource.getTransactionDetails(id);
      // Cache the transaction details
      await localDataSource.cacheTransactionDetails(id, transaction);
      return Right(transaction);
    } on ApiException catch (e) {
      // Try to get cached details if remote fails
      try {
        final cachedTransaction =
            await localDataSource.getCachedTransactionDetails(id);
        if (cachedTransaction != null) {
          return Right(cachedTransaction);
        }
      } catch (_) {}
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  Future<Either<Failure, Transaction>> validateTransactions(String id) async {
    try {
      final transaction = await remoteDataSource.validateTransaction(id);
      return Right(transaction);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  Future<Either<Failure, InvoiceResponseModel>> getInvoice(
    String billRefNo,
  ) async {
    try {
      final invoice = await remoteDataSource.getInvoice(billRefNo);
      return Right(invoice);
    } on ApiException catch (e) {
      return Left(
        ServerFailure(
          message: e.message,
        ),
      );
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Map<String, String>> fetchUserAvatars(List<String> userIds) async {
    try {
      return await remoteDataSource.fetchUserAvatars(userIds);
    } on ApiException catch (e) {
      return {};
    } catch (e) {
      return {};
    }
  }

  @override
  Future<Either<Failure, List<TransactionWithAvatar>>>
      getRecentWalletTransfers({
    required int limit,
    required String currentUserId,
  }) async {
    try {
      final transactions = await remoteDataSource.getRecentWalletTransfers(
        limit: limit,
        currentUserId: currentUserId,
      );

      await localDataSource.cacheRecentWalletTransfers(transactions);

      return Right(transactions);
    } on ApiException catch (e) {
      try {
        // Try to get from cache if available
        final cachedTransfers =
            await localDataSource.getCachedRecentWalletTransfers();
        if (cachedTransfers.isNotEmpty) {
          return Right(cachedTransfers);
        }
        return Left(ServerFailure(message: e.message));
      } catch (_) {
        return Left(ServerFailure(message: e.message));
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, TransferLimit>> getTransferLimit({
    required String transactionType,
    required String currency,
  }) async {
    try {
      final transferLimit = await remoteDataSource.getTransferLimit(
        transactionType: transactionType,
        currency: currency,
      );
      return Right(transferLimit);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  ResultFuture<String> getLoanInvoice({
    required String billRefNo,
    required LoanReceipt loanReceipt,
  }) async {
    try {
      final invoice = await remoteDataSource.getLoanInvoice(
        billRefNo: billRefNo,
        loanReceipt: loanReceipt,
      );
      return Right(invoice);
    } on ApiException catch (e) {
      return Left(
        ServerFailure(
          message: e.message,
        ),
      );
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ConfirmTransferResponseModel>> confirmTransfer({
    required String pin,
    required String billRefNo,
    required String transactionType,
    String? otp,
  }) async {
    try {
      final result = await remoteDataSource.confirmTransfer(
        pin: pin,
        billRefNo: billRefNo,
        transactionType: transactionType,
        otp: otp,
      );
      return Right(result);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  }) async {
    try {
      final result = await remoteDataSource.verifyOtp(
        billRefNo: billRefNo,
        otpFor: otpFor,
        otpCode: otpCode,
      );
      return const Right(true);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, dynamic>> resendOtp({
    required String billRefNo,
    required String otpFor,
  }) async {
    try {
      final result = await remoteDataSource.resendOtp(
        billRefNo: billRefNo,
        otpFor: otpFor,
      );
      return Right(result);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
