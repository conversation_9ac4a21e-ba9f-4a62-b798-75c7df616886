import 'dart:ui';

import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/features/transactions/presentation/views/quick wallet transfer/quick_pay_recipent_card.dart';

class CustomTopUpAmount extends StatefulWidget {
  const CustomTopUpAmount({
    required this.walletBalance,
    required this.onContinue,
    super.key,
    this.onClose,
  });
  final double walletBalance;

  final VoidCallback? onClose;
  final Function(String amount) onContinue;

  /// Shows this component as a bottom sheet.
  static void show(
    BuildContext context, {
    required double walletBalance,
    required void Function(String amount) onContinue,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          BlocConsumer<WalletTransferBloc, WalletTransferState>(
        listener: (context, state) {
          if (state is WalletTransferError) {
            CustomToastification(
              context,
              message: state.message,
            );
          }
        },
        builder: (context, state) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.8,
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Stack(
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 2, sigmaY: 1),
                    child: Container(
                      color: Colors.black.withOpacity(0.03),
                    ),
                  ),
                ),
                Container(
                  clipBehavior: Clip.antiAlias,
                  decoration: const ShapeDecoration(
                    color: Color(0xFFFCFCFC),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24),
                      ),
                    ),
                  ),
                  child: CustomTopUpAmount(
                    walletBalance: walletBalance,
                    onClose: () => Navigator.pop(context),
                    onContinue: (value) => onContinue(value),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  State<CustomTopUpAmount> createState() => _CustomTopUpAmountState();
}

class _CustomTopUpAmountState extends State<CustomTopUpAmount> {
  late CurrencyInputController _currencyController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    context.read<WalletBalanceBloc>().add(
          const FetchWalletEvent(
            isUsdWallet: false,
            forceTheme: false,
          ),
        );

    final state = context.read<WalletBalanceBloc>().state;

    if (state is WalletLoadedState) {
      setState(() {
        walletBalance = state.isUsdWallet ? state.usdBalance : state.etbBalance;
      });
    }
    _currencyController = CurrencyInputController(
      currencyType: CurrencyType.etb,
      maxBalance: widget.walletBalance,
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    try {
      final amount = _currencyController.numericAmount;

      // Validate amount is greater than 0
      if (amount <= 0) {
        CustomToastification(
          context,
          message: 'Amount must be greater than 0',
        );
        return;
      }

      Navigator.canPop(context);
      widget.onContinue(
        AppMapper.safeFormattedNumberWithDecimal(amount)
            .replaceAll(',', '')
            .replaceAll(RegExp(r'\.\d*'), ''),
      );
    } catch (e) {
      CustomToastification(
        context,
        message: 'Invalid Amount',
      );
    }
  }

  double walletBalance = 0;

  String _formatBalance() {
    return CurrencyFormatter.formatWalletBalance(walletBalance, 'ETB');
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<WalletTransferBloc>(
          create: (_) => sl<WalletTransferBloc>(),
        ),
        BlocProvider<TransactionBloc>(
          create: (_) => sl<TransactionBloc>(),
        ),
      ],
      child: BlocListener<WalletTransferBloc, WalletTransferState>(
        listener: (context, state) {
          if (state is CheckingWalletTransferRules) {
            setState(() => _isLoading = true);
          } else if (state is WalletTransferRulesChecked) {
            setState(() => _isLoading = false);

            // Close the bottom sheet if needed
            if (widget.onClose != null) {
              widget.onClose!();
            }

            // Navigate to confirm page with rules data
            context.pushNamed(
              AppRouteName.confirmWalletTransfer,
              extra: {
                'amount': _currencyController.cleanAmount,
                'transferRules': state.rulesResponse,
              },
            );
          } else if (state is WalletTransferFailure) {
            setState(() => _isLoading = false);
            CustomToastification(
              context,
              message: state.message,
            );
          }
        },
        child: BlocListener<WalletBalanceBloc, HomeState>(
          listener: (context, state) {
            // CustomToastification(context, message: 'tttkkekkeke $state');

            if (state is WalletLoadedState) {
              setState(() {
                walletBalance =
                    state.isUsdWallet ? state.usdBalance : state.etbBalance;
                _currencyController = CurrencyInputController(
                  currencyType: CurrencyType.usd,
                  maxBalance: walletBalance,
                );
              });
            }
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Padding(
                  padding: EdgeInsets.only(top: 12.h, bottom: 16.h),
                  child: Container(
                    width: 36.w,
                    height: 2.h,
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                  ),
                ),
              ),
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                  ),
                  child: Text(
                    'Top-Up Amount',
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 4.h,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                ),
                child: Text(
                  'Enter a custom top-up amount and recharge airtime for your relatives.',
                  // 'Enter the Enter the amount you wish to send to the recipient and submit.',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.outfit(
                    fontSize: 12.sp,
                    color: const Color(0xFFAAAAAA),
                  ),
                ),
              ),
              Expanded(
                child: CurrencyInputWidget(
                  controller: _currencyController,
                  title: '',
                  subtitle: '',
                  balanceLabel: _formatBalance(),
                  isLoading: _isLoading,
                  onContinue: _onContinuePressed,
                  transactionType: 'top_up',
                ),
              ),
              const SizedBox(
                height: 8,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
