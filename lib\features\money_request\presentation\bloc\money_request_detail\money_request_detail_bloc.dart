import 'package:cbrs/features/money_request/domain/entities/wallet_detail_entity.dart';
import 'package:cbrs/features/money_request/domain/usecases/accept_request_usecase.dart';
import 'package:cbrs/features/money_request/domain/usecases/cancel_request_usercase.dart';
import 'package:cbrs/features/money_request/domain/usecases/confirm_request_usecase.dart';
import 'package:cbrs/features/money_request/domain/usecases/get_wallet_detail_usecase.dart';
import 'package:cbrs/features/money_request/domain/usecases/reject_request_usecase.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

part 'money_request_detail_event.dart';
part 'money_request_detail_state.dart';

class MoneyRequestDetailBloc
    extends Bloc<MoneyRequestDetailEvent, MoneyRequestDetailState> {
  MoneyRequestDetailBloc(
    this._acceptRequest,
    this._rejectRequest,
    this._cancelRequest,
    this._confirmRequest,
    this._getWalletDetail,
  ) : super(const MoneyRequestDetailStateInitial()) {
    on<AcceptRequestActionEvent>(_onAcceptRequestAction);
    on<RejectRequestActionEvent>(_onRejectRequestAction);
    on<CancelRequestActionEvent>(_onCancelRequestAction);
    on<MoneyRequestDetailConfirmPinEvent>(_onMoneyRequestDetailConfirmPinEvent);
    on<GetWalletDetailEvent>(_onGetWalletDetailEvent);
  }

  final CancelRequest _cancelRequest;
  final AcceptRequest _acceptRequest;
  final RejectRequest _rejectRequest;
  final ConfirmRequest _confirmRequest;
  final GetWalletDetail _getWalletDetail;

  Future<void> _onAcceptRequestAction(
    AcceptRequestActionEvent event,
    Emitter<MoneyRequestDetailState> emit,
  ) async {
    emit(
      const MoneyRequestDetailLoadingState(
        actionState: MoneyRequestAction.accept,
      ),
    );

    final result = await _acceptRequest(
      AcceptRequestParam(
        transactionId: event.transactionID,
        status: 'ACCEPTED',
        amount: event.amount,
      ),
    );

    result.fold(
      (error) => emit(
        MoneyRequestDetailActionState(
          message: error.message,
          isSuccess: false,
          actionState: MoneyRequestAction.accept,
        ),
      ),
      (data) => emit(
        MoneyRequestDetailActionState(
          message: data.message,
          isSuccess: true,
          actionState: MoneyRequestAction.accept,
        ),
      ),
    );
  }

  Future<void> _onRejectRequestAction(
    RejectRequestActionEvent event,
    Emitter<MoneyRequestDetailState> emit,
  ) async {
    emit(
      const MoneyRequestDetailLoadingState(
        actionState: MoneyRequestAction.reject,
      ),
    );

    final result = await _rejectRequest(
      RejectRequestParam(
        transactionId: event.transactionID,
        status: 'REJECTED',
        amount: event.amount,
        reason: event.reason,
      ),
    );

    result.fold(
      (error) => emit(
        MoneyRequestDetailActionState(
          message: error.message,
          isSuccess: false,
          actionState: MoneyRequestAction.reject,
        ),
      ),
      (data) => emit(
        MoneyRequestDetailActionState(
          message: data.message,
          isSuccess: true,
          actionState: MoneyRequestAction.reject,
        ),
      ),
    );
  }

  Future<void> _onCancelRequestAction(
    CancelRequestActionEvent event,
    Emitter<MoneyRequestDetailState> emit,
  ) async {
    emit(
      const MoneyRequestDetailLoadingState(
        actionState: MoneyRequestAction.cancel,
      ),
    );

    final result = await _cancelRequest(
      CancelRequestParam(
        transactionId: event.transactionID,
        status: 'CANCELED',
        amount: event.amount,
      ),
    );

    result.fold(
      (error) => emit(
        MoneyRequestDetailActionState(
          message: error.message,
          isSuccess: false,
          actionState: MoneyRequestAction.cancel,
        ),
      ),
      (data) => emit(
        MoneyRequestDetailActionState(
          message: data.message,
          isSuccess: true,
          actionState: MoneyRequestAction.cancel,
        ),
      ),
    );
  }

  Future<void> _onMoneyRequestDetailConfirmPinEvent(
    MoneyRequestDetailConfirmPinEvent event,
    Emitter<MoneyRequestDetailState> emit,
  ) async {
    final result = await _confirmRequest(
      ConfirmRequestParam(
        pin: event.pin,
        billRefNo: event.billRefNo,
      ),
    );

    result.fold(
      (error) {
        emit(MoneyRequestDetailErrorState(message: error.message));
      },
      (data) => emit(
        MoneyRequestDetailResultState(isSuccess: true, message: data.message),
      ),
    );
  }

  Future<void> _onGetWalletDetailEvent(
    GetWalletDetailEvent event,
    Emitter<MoneyRequestDetailState> emit,
  ) async {
    final result = await _getWalletDetail();

    result.fold(
      (error) {
        emit(MoneyRequestDetailErrorState(message: error.message));
      },
      (data) => emit(
        GetWalletDetailState(wallet: data),
      ),
    );
  }
}
