import 'dart:io';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class PdfViewerPage extends StatefulWidget {
  const PdfViewerPage({required this.url, super.key});
  final String url;

  @override
  State<PdfViewerPage> createState() => _PdfViewerPageState();
}

class _PdfViewerPageState extends State<PdfViewerPage> {
  Future<void> _downloadPDF(BuildContext context) async {
    try {
      _getStoragePermission();

      if (_isDownloading) return;
      setState(() {
        _isDownloading = true;
      });

      final filename = widget.url.split('/').last;

      Directory? dir;
      if (Platform.isAndroid) {
        dir = Directory('/storage/emulated/0/Download');
        if (!await dir.exists()) {
          dir = await getExternalStorageDirectory();
        }
      } else {
        dir = await getApplicationDocumentsDirectory();
      }

      final filePath = '${dir!.path}/$filename';
      final file = File(filePath);

      final response = await Dio().download(
        widget.url,
        file.path,
        onReceiveProgress: (rec, total) {
          setState(() {
            downloadProgress = (rec / total * 100).toStringAsFixed(0);
          });
        },
      );

      if (response.statusCode == 200) {
        CustomToastification(
          context,
          message: 'Saved to ${file.path}',
          isError: false,
        );
      } else {
        throw Exception('Download failed');
      }
    } catch (e) {
      debugPrint('Download error: $e');
      CustomToastification(
        context,
        message: "Failed to Download the reciept. Please take screenshot'",
      );
    } finally {
      setState(() {
        _isDownloading = false;
        downloadProgress = '0';
      });
    }
  }

  bool permissionGranted = false;

  String downloadProgress = '0';
  bool _isDownloading = false;

  Future<void> _getStoragePermission() async {
    if (Platform.isAndroid) {
      final plugin = DeviceInfoPlugin();
      final android = await plugin.androidInfo;
      if (android.version.sdkInt < 33) {
        if (await Permission.storage.request().isGranted) {
          setState(() {
            permissionGranted = true;
          });
        } else if (await Permission.storage.request().isPermanentlyDenied) {
          await openAppSettings();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Receipt'),
      ),
      body: SfPdfViewer.network(widget.url),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Theme.of(context).primaryColor,
        onPressed: () => _downloadPDF(context),
        child: _isDownloading
            ? Text('$downloadProgress %')
            : const Icon(Icons.download),
      ),
    );
  }
}
