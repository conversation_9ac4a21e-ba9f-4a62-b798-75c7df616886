import 'dart:async';
import 'package:cbrs/core/common/models/transaction_types.dart';
import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/common/widgets/success/custom_success_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_state.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_status_response.dart';
import 'package:cbrs/features/load_wallet/presentation/view/failure_page.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:go_router/go_router.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_bloc.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;

class LoadToWalletWebView extends StatefulWidget {
  const LoadToWalletWebView({
    required this.url,
    required this.redirectURL,
    required this.billRefNo,
    super.key,
  });
  final String url;
  final String redirectURL;
  final String billRefNo;

  @override
  State<LoadToWalletWebView> createState() => _LoadToWalletWebViewState();
}

class _LoadToWalletWebViewState extends State<LoadToWalletWebView> {
  Timer? _statusCheckTimer;
  late final WebViewController _controller;
  bool _hasError = false;
  late TransactionBottomSheetsManager _bottomSheetsManager;
  final TextEditingController _pinController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initWebView();

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.loadToWallet,
      pinController: _pinController,
      onPinSubmitted: (pin) {},
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        // _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  void _initWebView() {
    debugPrint('Received URL in WebView: ${widget.url}');
    if (widget.url.isEmpty) {
      _hasError = true;
      return;
    }

    debugPrint('Loading URL: ${widget.url}');

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..enableZoom(false)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
            debugPrint('Page started loading: $url');
            _checkUrlChange(url);
          },
          onPageFinished: (url) {
            debugPrint('Page finished loading: $url');
            _checkUrlChange(url);
          },
          onWebResourceError: (error) {
            debugPrint('Web resource error: ${error.description}');
            setState(() => _hasError = true);
          },
          onNavigationRequest: (request) {
            debugPrint('Navigation request to: ${request.url}');
            _checkUrlChange(request.url);
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(
        Uri.parse(widget.url),
        headers: {
          'Content-Security-Policy':
              "frame-ancestors 'self' https://mtf.gateway.mastercard.com/",
          'X-Frame-Options': 'ALLOW-FROM https://mtf.gateway.mastercard.com/',
        },
      );
  }


    void _checkUrlChange(String currentUrl) {
    debugPrint('✅Current URL: $currentUrl');
    
    // Check if URL contains loading.html and resultIndicator
    if (currentUrl.contains('loading.html') && currentUrl.contains('resultIndicator')) {
      // Extract resultIndicator from URL if needed
      final uri = Uri.parse(currentUrl);
      final resultIndicator = uri.queryParameters['resultIndicator'];
      
      debugPrint('✅Payment completed, resultIndicator: $resultIndicator');
      
      // Start final status check with shorter timeout
      _startStatusCheck();
    }
  }

  // void _checkUrlChange(String currentUrl) {
  //   debugPrint('✅Current URL: $currentUrl');
  //   debugPrint('✅Redirect URL: ${widget.redirectURL}');
  //   debugPrint('✅Session URL: ${widget.url}');

  //   if (widget.redirectURL.isNotEmpty &&
  //       currentUrl.contains(widget.redirectURL)) {
  //     debugPrint(
  //       '✅URL changed from session URL to redirect URL, starting status check',
  //     );
  //     _startStatusCheck();
  //   }
  // }

  void _startStatusCheck() {
    _statusCheckTimer?.cancel();
    _statusCheckTimer = Timer.periodic(
      const Duration(seconds: 5),
      (_) {
        debugPrint('Checking payment status...');
        context.read<LoadWalletBloc>().add(
              CheckLoadWalletStatus(widget.billRefNo),
            );
      },
    );
  }

  @override
  void dispose() {
    _statusCheckTimer?.cancel();
    super.dispose();
  }

  void _showSuccessScreenBottomSheet(
    LoadWalletStatusData transaction,
  ) {
    debugPrint(
      '  paid amount ${transaction.paidAmount} total ${transaction.totalAmount}',
    );
    _bottomSheetsManager.showSuccessScreenBottomSheet(
      {
        'Card Holder Name': transaction.cardHolderName,
        'Card Number': transaction.cardNumber,
        'Service Charge': transaction.serviceCharge,
        'VAT': transaction.vat,
        'Amount': transaction.billAmount,
        'Date': AppMapper.safeFormattedDate(transaction.paidDate),
        'BillRefNo': transaction.billRefNo,
        'MPGS Ref': transaction.mpgsReference,
        'Bank Ref': transaction.ftNumber,
      },
      totalAmount: transaction.totalAmount,
      billAmount: transaction.billAmount,
      transactionId: transaction.id,
      billRefNo: transaction.billRefNo,


      status: 'Paid',
      originalCurrency: 'USD',
      title: 'You have successfully loaded to your wallet.',
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LoadWalletBloc, LoadWalletState>(
      listener: (context, state) {
        if (state is LoadWalletCompleted) {
          _statusCheckTimer?.cancel();
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // final response = ConfirmTransferResponse(
            //   statusCode: 200,
            //   success: true,
            //   message: 'Load to wallet successfull',
            //   transaction: ConfirmTransferDetail(
            //     authorizationType: 'free',
            //     id: '${state.amount}${state.transactionReference}',
            //     transactionType: tx_type.TransactionType.loadToWallet,
            //     billAmount: AppMapper.safeDouble(state.amount),

            //     originalCurrency: 'USD',
            //     serviceCharge: state.transactio,
            //     billRefNo: state.transactionReference,
            //     cardNumber: state.cardNumber,
            //     cardHoderName: state.cardHolderName,
            //     vat: 0,
            //     totalAmount: AppMapper.safeDouble(state.amount),
            //     paidAmount: AppMapper.safeDouble(state.amount),
            //     status: 'Paid',

            //     createdAt: DateTime.now(),
            //     lastModified: DateTime.now(),
            //     // TransactionType.loadToWallet,
            //   ),
            // );

            _showSuccessScreenBottomSheet(state.loadWalletStatusData);

            // showModalBottomSheet(
            //   context: context,
            //   isScrollControlled: true,
            //   backgroundColor: Colors.transparent,
            //   builder: (context) {
            //     return CustomSuccessTransactionBottomSheet(
            //       data: {
            //         'amount': state.amount,
            //         'transactionType': state.transactionType,
            //         'cardNumber': state.cardNumber,
            //         'cardHolderName': state.cardHolderName,
            //         'transactionDate': state.transactionDate,
            //         'transactionReference': state.transactionReference,
            //       },
            //       onContinue: () {
            //         Navigator.pop(context);
            //         context.go(AppRouteName.home);
            //       },
            //     );
            //   },
            // );

            // Navigator.pushReplacement(
            //   context,
            //   MaterialPageRoute(
            //     builder: (_) => SuccessPage(
            //       amount: state.amount,
            //       transactionType: state.transactionType,
            //       cardNumber: state.cardNumber,
            //       cardHolderName: state.cardHolderName,
            //       transactionDate: state.transactionDate,
            //       transactionReference: state.transactionReference,
            //     ),
            //   ),
            // );
          });
        } else if (state is LoadWalletFailure) {
          _statusCheckTimer?.cancel();
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (_) => const FailurePage(),
              ),
            );
          });
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(title: const Text('Load to Wallet')),
          body: SafeArea(
            child: Stack(
              children: [
                if (!_hasError && widget.url.isNotEmpty)
                  WebViewWidget(controller: _controller),
                if (state is LoadWalletLoading)
                  const Center(child: CustomConnectLoader()),
              ],
            ),
          ),
        );
      },
    );
  }
}
