import 'dart:async';

import 'package:cbrs/core/common/widgets/confirm/custom_confirm_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_otp_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_pin_input.dart';
import 'package:cbrs/core/common/widgets/custom_pin_screen.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/success/custom_success_transaction_bottom_sheet.dart';
import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_event.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_state.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/upfront_payment_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RepaymentBottomSheetsManager {
  RepaymentBottomSheetsManager({
    required this.context,
    required this.transactionType,
    required this.pinController,
    required this.onPinSubmitted,
    required this.onTransactionSuccess,
    required this.onTransactionComplete,
  });
  final BuildContext context;
  final tx_type.TransactionType transactionType;
  final TextEditingController pinController;

  final Function(String, String, bool) onPinSubmitted;
  final Function(dynamic, bool) onTransactionSuccess;
  final Function() onTransactionComplete;

  void showConfirmScreenBottomSheet({
    required Map<String, dynamic> data,
    required bool requiresOtp,
    required String billRefNo,
    required double billAmount,
    required double totalAmount, // TODO,,
    required tx_type.TransactionType transactionType,
    String status = 'Wait',
    String originalCurrency = '', //

    bool isRepayment = false,
    String confirmButtonText = 'Confirm Transfer',
  }) {
    showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, updateState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.9,
                ),
                child: CustomConfirmTransactionBottomSheet(
                  data: data,
                  transactionType: transactionType.value,
                  confirmButtonText: confirmButtonText,
                  status: status,
                  billAmount: billAmount,
                  totalAmount: totalAmount,
                  originalCurrency: originalCurrency,
                  onContinue: () {
                    debugPrint('money req uest');

                    if (Navigator.of(context).canPop()) {
                      Navigator.pop(context);
                    }
                    if (requiresOtp) {
                      showConfirmOtpScreenBottomSheet(
                        billRefNo: billRefNo,
                        isRepayment: isRepayment,
                      );
                    } else {
                      showConfirmPinScreenBottomSheet(
                        billRefNo: billRefNo,
                        isRepayment: isRepayment,
                      );
                    }
                  },
                ),
              ),
            );
          },
        );
      },
    );
  }

  void showConfirmOtpScreenBottomSheet({
    required String billRefNo,
    bool isRepayment = false,
  }) {
    final valueBloc = context.read<UpfrontPaymentBloc>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.9,
            ),
            child: BlocProvider<UpfrontPaymentBloc>.value(
              value: valueBloc,
              child: OtpBottomSheet(
                billRefNo: billRefNo,
                otpFor: transactionType.value,
                transactionType: transactionType,
                focusNode: FocusNode(),
                onOtpVerified: (otpCode) {
                  Navigator.pop(context);
                  showConfirmPinScreenBottomSheet(
                    billRefNo: billRefNo,
                    isRepayment: isRepayment,
                  );
                },
                onResendSuccess: () {
                  // Optional: actions after OTP is resent
                },
              ),
            ),
          ),
        );
      },
    );
  }

  bool _isCheckingPin = false;

  void showConfirmPinScreenBottomSheet({
    required String billRefNo,
    bool isRepayment = false,
  }) {
    final blocValue = BlocProvider.of<UpfrontPaymentBloc>(context);

    showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return BlocProvider.value(
              value: blocValue,
              child: BlocConsumer<UpfrontPaymentBloc, RepaymentLoanState>(
                listener: (context, state) {
                  if (state is RepaymentErrorState) {
                    debugPrint('hello');
                    CustomToastification(
                      context,
                      message: state.message,
                    );
                    resetPinState(setModalState);
                  } else if (state is ConfirmedUpfrontPaymentState) {
                    setModalState(() {});

                    onTransactionSuccess(state.upfrontLoan, false);

                    // showUpfrontSuccessBottomSheet(state.upfrontLoan);
                    // CustomToastification(context, message: "Payment succceed");
                    // onTransactionSuccess(state.transaction);
                  } else if (state is ConfirmedMonthlyRePaymentState) {
                    setModalState(() {});

                    onTransactionSuccess(state.monthlyRepayment, true);
                  }
                },
                builder: (context, state) {
                  return CustomPinScreen(
                    controller: pinController,
                    onChanged: (keys, isKey) {
                      if (_isCheckingPin) return;

                      setModalState(() {
                        if (!isKey) {
                          pinController.text = pinController.text.isNotEmpty
                              ? pinController.text
                                  .substring(0, pinController.text.length - 1)
                              : '';
                          pinController.selection = TextSelection.fromPosition(
                            TextPosition(offset: pinController.text.length),
                          );
                        }
                        pinController.text = "${pinController.text}$keys";
                        pinController.selection = TextSelection.fromPosition(
                          TextPosition(offset: pinController.text.length),
                        );
                      });
                    },
                    onSubmitted: (pin) {
                      if (_isCheckingPin) return;

                      setModalState(() => _isCheckingPin = true);
                      onPinSubmitted(pin, billRefNo, isRepayment);
                    },
                    isLoading: _isCheckingPin,
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  Future<void> resetPinState(StateSetter setModalState) async {
    setModalState(() {
      _isCheckingPin = false;
      pinController.clear();
    });
  }

  void showSuccessScreenBottomSheet(
    Map<String, dynamic> data, {
    required double totalAmount,
    required double billAmount,
    required String transactionId,
    required String billRefNo,
    required LoanReceipt loanReciept,
    String status = 'Wait',
    String originalCurrency = '',
    String title = '', //
    bool showActionButtons = true,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) {
        return CustomSuccessTransactionBottomSheet(
          data: data,
          isFromChat: false,
          buttonText: 'Back to my loan',
          originalCurrency: originalCurrency,
          totalAmount: totalAmount,
          billAmount: billAmount,
          transactionId: transactionId,
          billRefNo: billRefNo,
          status: status,
          title: title,
          isFromLoan: true,
          loanReceipt: loanReciept,
          transactionType: transactionType.value,
          onContinue: onTransactionComplete,
          showActionButtons: showActionButtons,
        );
      },
    );
  }
}

class OtpBottomSheet extends StatefulWidget {
  const OtpBottomSheet({
    required this.onOtpVerified,
    required this.billRefNo,
    required this.transactionType,
    required this.focusNode,
    this.otpFor = 'TRANSACTION',
    this.onResendSuccess,
    this.onSubmitingPin,
    super.key,
  });

  final Function(String otpCode) onOtpVerified;

  final Function(String otpCode)? onSubmitingPin;

  final String billRefNo;
  final String otpFor;
  final tx_type.TransactionType transactionType;
  final VoidCallback? onResendSuccess;
  final FocusNode focusNode;

  @override
  State<OtpBottomSheet> createState() => _OtpBottomSheetState();
}

class _OtpBottomSheetState extends State<OtpBottomSheet> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  Timer? _timer;
  int _timeLeft = 59;
  bool _isResending = false;
  bool _isVerifying = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.focusNode.requestFocus();
    });
    _startTimer();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer?.cancel();
    setState(() {
      _timeLeft = 59;
    });
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeLeft == 0) {
        timer.cancel();
      } else {
        setState(() {
          _timeLeft--;
        });
      }
    });
  }

  void _handleResendOtp() {
    if (_timeLeft > 0 || _isResending) return;

    setState(() {
      _isResending = true;
    });

    context.read<UpfrontPaymentBloc>().add(
          ResendOtpEvent(
            billRefNo: widget.billRefNo,
            otpFor: widget.otpFor,
          ),
        );
  }

  void _handleVerifyOtp() {
    if (_controller.text.length < 4 || _isVerifying) return;

    setState(() {
      _isVerifying = true;
    });

    try {
      final otpCode = int.parse(_controller.text);
      debugPrint(
        'So we were here with $otpCode and ${widget.otpFor} and ${widget.billRefNo}',
      );

      context.read<UpfrontPaymentBloc>().add(
            VerifyOtpEvent(
              billRefNo: widget.billRefNo,
              otpFor: widget.otpFor,
              otpCode: otpCode,
            ),
          );
    } catch (e) {
      setState(() {
        _isVerifying = false;
      });
      CustomToastification(
        context,
        message: 'Please enter a valid OTP code',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UpfrontPaymentBloc, RepaymentLoanState>(
      listener: (context, state) {
        /*
        if (state is OtpResendSuccess) {
          setState(() {
            _isResending = false;
          });
          _startTimer();
          CustomToastification(
            context,
            message: 'OTP has been resent to your email',
            isError: false,
          );
          widget.onResendSuccess?.call();
        } else if (state is OtpResendError) {
          setState(() {
            _isResending = false;
            _controller.clear();
          });
          CustomToastification(
            context,
            message: state.message,
          );
        } else if (state is OtpVerificationSuccess) {
          setState(() {
            _isVerifying = false;
          });
          widget.onOtpVerified(_controller.text);
        } else if (state is OtpVerificationError) {
          debugPrint('this is whatsapp;');
          setState(() {
            _isVerifying = false;
          });
          CustomToastification(
            context,
            message: state.message,
          );
        }
        */
      },
      child: IntrinsicHeight(
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          child: Container(
            clipBehavior: Clip.antiAlias,
            decoration: const ShapeDecoration(
              color: Color(0xFFFCFCFC),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(36),
                  topRight: Radius.circular(36),
                ),
              ),
            ),
            child: Column(
              children: [
                Center(
                  child: Container(
                    width: 64,
                    height: 6,
                    margin: EdgeInsets.only(
                      top: 16.h,
                      left: 16.w,
                      right: 16.w,
                      bottom: 8,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFBCBCBC),
                      borderRadius: BorderRadius.circular(40),
                    ),
                  ),
                ),
                Flexible(
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    child: Container(
                      color: Colors.white,
                      padding: EdgeInsets.only(
                        left: 16.w,
                        right: 16.w,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(
                            height: 8,
                          ),
                          CustomBuildText(
                            text: 'Confirm transfer',
                            fontSize: 20.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                          CustomBuildText(
                            text:
                                "We've sent a verification code to your email. Please enter it to confirm the transfer.",
                            textAlign: TextAlign.center,
                            color: Colors.black.withOpacity(0.3),
                            caseType: 'default',
                            fontSize: 14.sp,
                          ),
                          SizedBox(
                            height: 42.h,
                          ),
                          Center(
                            child: CustomBuildText(
                              text:
                                  '00:${_timeLeft.toString().padLeft(2, '0')} Sec',
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          SizedBox(
                            height: 36.h,
                          ),
                          IgnorePointer(
                            ignoring: _isVerifying,
                            child: CustomPinInput(
                              controller: _controller,
                              autoFocus: true,
                              pinFocusNode: widget.focusNode,
                            ),
                          ),
                          SizedBox(
                            height: 36.h,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CustomBuildText(
                                text: "Didn't receive code?",
                                color: const Color(0xFF7A7A7A),
                                fontSize: 14.sp,
                              ),
                              SizedBox(width: 8.w),
                              GestureDetector(
                                onTap: _timeLeft == 0 ? _handleResendOtp : null,
                                child: CustomBuildText(
                                  text: 'Resend OTP',
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                  color: _timeLeft == 0
                                      ? Theme.of(context).primaryColor
                                      : Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.5),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 40.h,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(
                    top: 16.h,
                    left: 16.w,
                    right: 16.w,
                    bottom: 20.h,
                  ),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: CustomRoundedBtn(
                    btnText: 'Confirm',
                    isLoading: _isVerifying,
                    onTap: _handleVerifyOtp,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionList({
    required String label,
    required String value,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CustomBuildText(
          text: label,
          textAlign: TextAlign.center,
          fontSize: 14.sp,
          caseType: 'default',
        ),
        CustomBuildText(
          text: value,
          textAlign: TextAlign.center,
          fontSize: 14.sp,
          caseType: 'default',
        ),
      ],
    );
  }
}
