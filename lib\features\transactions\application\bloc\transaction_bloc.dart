import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/transactions/data/models/transaction_with_avatar.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction_group.dart';
import 'package:cbrs/features/transactions/domain/entities/transfer_limit.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:cbrs/features/transactions/domain/usecases/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_invoice.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_loan_invoice.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_recent_wallet_transfers.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_transactions.dart';
import 'package:cbrs/features/transactions/domain/usecases/get_transfer_limit.dart';
import 'package:cbrs/features/transactions/domain/usecases/resend_otp.dart';
import 'package:cbrs/features/transactions/domain/usecases/verify_otp.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:url_launcher/url_launcher.dart';

part 'transaction_event.dart';
part 'transaction_state.dart';

class TransactionBloc extends Bloc<TransactionEvent, TransactionState> {
  TransactionBloc({
    required this.getTransactions,
    required this.authLocalDataSource,
    required this.getRecentWalletTransfers,
    required this.getTransferLimit,
    required this.getLoanInvoice,
    required this.confirmTransfer,
    required this.verifyTransactionOtp,
    required this.resendTransactionOtp,
    required this.getInvoice,
  }) : super(TransactionInitial()) {
    on<FetchTransactionsEvent>((event, emit) async {
      emit(TransactionLoading(keepCache: event.keepCache));
      final result = await getTransactions(
        GetTransactionsParams(
          page: event.page,
          perPage: event.perPage,
        ),
      );

      debugPrint('Is freeeeech');
      result.fold(
        (failure) => emit(TransactionError(message: failure.message)),
        (transactions) {
          _allTransactions = transactions;
          final groupedTransactions = _groupTransactions(_allTransactions);
          emit(
            TransactionLoaded(
              allTransactions: _allTransactions,
              groupedTransactions: groupedTransactions,
              bloc: this,
            ),
          );
        },
      );
    });

    on<FetchMoreTransactionsEvent>((event, emit) async {
      final currentState = state;
      if (currentState is TransactionLoaded) {
        final result = await getTransactions(
          GetTransactionsParams(
            page: event.page,
            perPage: event.perPage,
            transactionType: currentState.currentFilter == 'All'
                ? null
                : currentState.currentFilter,
            startDate: currentState.startDate?.toIso8601String(),
            endDate: currentState.endDate?.toIso8601String(),
          ),
        );
        result.fold(
          (failure) => emit(TransactionError(message: failure.message)),
          (newTransactions) {
            _allTransactions = [..._allTransactions, ...newTransactions];
            final groupedTransactions = _groupTransactions(_allTransactions);
            emit(
              TransactionLoaded(
                allTransactions: _allTransactions,
                groupedTransactions: groupedTransactions,
                bloc: this,
                currentFilter: currentState.currentFilter,
                startDate: currentState.startDate,
                endDate: currentState.endDate,
              ),
            );
          },
        );
      }
    });

    on<FilterTransactionsEvent>(_handleFilterTransactions);
    on<FilterByDateRangeEvent>(_handleFilterByDateRange);
    on<FilterBySingleDateEvent>(_handleFilterBySingleDate);

    on<FetchRecentWalletTransfersEvent>(_onFetchRecentWalletTransfers);

    on<FetchTransferLimitEvent>(_onFetchTransferLimit);

    on<GetLoanInvoiceEvent>(_onGetLoanInvoice);

    on<DownloadReceiptEvent>(_onDownloadReceipt);

    on<ConfirmTransferEvent>(_onConfirmTransfer);
    on<VerifyOtpEvent>(_onVerifyOtp);
    on<ResendOtpEvent>(_onResendOtp);
  }
  final GetTransactions getTransactions;
  final AuthLocalDataSource authLocalDataSource;
  final GetRecentWalletTransfers getRecentWalletTransfers;
  final GetTransferLimit getTransferLimit;
  final GetLoanInvoice getLoanInvoice;
  final ConfirmTransfer confirmTransfer;
  final VerifyTransactionOtp verifyTransactionOtp;
  final ResendTransactionOtp resendTransactionOtp;
  final GetInvoice getInvoice;
  List<Transaction> _allTransactions = [];

  Future<void> _handleFilterTransactions(
    FilterTransactionsEvent event,
    Emitter<TransactionState> emit,
  ) async {
    emit(const TransactionLoading());

    final transactionType = event.category == 'All' ? null : event.category;

    final result = await getTransactions(
      GetTransactionsParams(
        page: 1,
        perPage: 10,
        transactionType: transactionType,
      ),
    );

    result.fold(
      (failure) => emit(TransactionError(message: failure.message)),
      (transactions) {
        _allTransactions = transactions;
        final groupedTransactions = _groupTransactions(_allTransactions);
        emit(
          TransactionLoaded(
            allTransactions: _allTransactions,
            groupedTransactions: groupedTransactions,
            bloc: this,
            currentFilter: event.category,
          ),
        );
      },
    );
  }

  Future<void> _handleFilterByDateRange(
    FilterByDateRangeEvent event,
    Emitter<TransactionState> emit,
  ) async {
    debugPrint(
      'start adat ${event.startDate.toIso8601String()} and end adate ${event.endDate.toIso8601String()}',
    );
    emit(const TransactionLoading());

    final result = await getTransactions(
      GetTransactionsParams(
        page: 1,
        perPage: 10,
        startDate: event.startDate.toIso8601String(),
        endDate: event.endDate.toIso8601String(),
        transactionType: event.transactionType,
      ),
    );

    debugPrint('trnansc tio');

    result.fold(
      (failure) => emit(TransactionError(message: failure.message)),
      (transactions) {
        _allTransactions = transactions;
        final groupedTransactions = _groupTransactions(_allTransactions);
        emit(
          TransactionLoaded(
            allTransactions: _allTransactions,
            groupedTransactions: groupedTransactions,
            bloc: this,
            currentFilter: state is TransactionLoaded
                ? (state as TransactionLoaded).currentFilter
                : 'All',
            startDate: event.startDate,
            endDate: event.endDate,
          ),
        );
      },
    );
  }

  Future<void> _handleFilterBySingleDate(
    FilterBySingleDateEvent event,
    Emitter<TransactionState> emit,
  ) async {
    emit(const TransactionLoading());

    final result = await getTransactions(
      GetTransactionsParams(
        page: 1,
        perPage: 10,
        startDate: event.date.toIso8601String(),
        endDate: event.date.toIso8601String(),
      ),
    );

    result.fold(
      (failure) => emit(TransactionError(message: failure.message)),
      (transactions) {
        _allTransactions = transactions;
        final groupedTransactions = _groupTransactions(_allTransactions);
        emit(
          TransactionLoaded(
            allTransactions: _allTransactions,
            groupedTransactions: groupedTransactions,
            bloc: this,
            startDate: event.date,
            endDate: event.date,
          ),
        );
      },
    );
  }

  List<TransactionGroup> _groupTransactions(List<Transaction> transactions) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    transactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    final todayTransactions = transactions.where((t) {
      final date =
          DateTime(t.createdAt.year, t.createdAt.month, t.createdAt.day);
      return date.isAtSameMomentAs(today);
    }).toList();

    final yesterdayTransactions = transactions.where((t) {
      final date =
          DateTime(t.createdAt.year, t.createdAt.month, t.createdAt.day);
      return date.isAtSameMomentAs(yesterday);
    }).toList();

    final olderTransactions = transactions.where((t) {
      final date =
          DateTime(t.createdAt.year, t.createdAt.month, t.createdAt.day);
      return date.isBefore(yesterday);
    }).toList();

    final groups = <TransactionGroup>[];

    if (todayTransactions.isNotEmpty) {
      groups.add(
        TransactionGroup(title: 'Today', transactions: todayTransactions),
      );
    }

    if (yesterdayTransactions.isNotEmpty) {
      groups.add(
        TransactionGroup(
          title: 'Yesterday',
          transactions: yesterdayTransactions,
        ),
      );
    }

    if (olderTransactions.isNotEmpty) {
      groups.add(
        TransactionGroup(
          title: 'All Transactions',
          transactions: olderTransactions,
        ),
      );
    }

    return groups;
  }









  Future<Map<String, String>> fetchUserAvatars(
    List<Transaction> transactions,
  ) async {
    debugPrint('👥 Fetching avatars for ${transactions.length} transactions');

    final userIds = transactions
        .where((t) => t.beneficiaryId != null)
        .map((t) => t.beneficiaryId!)
        .toSet()
        .toList();

    debugPrint('🆔 Unique user IDs to fetch: ${userIds.length}');
    if (userIds.isEmpty) {
      debugPrint('⚠️ No valid user IDs found for avatar fetch');
      return {};
    }

    try {
      return await getTransactions.repository.fetchUserAvatars(userIds);
    } catch (e) {
      debugPrint('❌ Error fetching avatars: $e');
      return {};
    }
  }

  Future<void> _onFetchRecentWalletTransfers(
    FetchRecentWalletTransfersEvent event,
    Emitter<TransactionState> emit,
  ) async {
    final currentState = state;

    if (currentState is! RecentWalletTransfersLoaded) {
      emit(const TransactionLoading());
    }

    try {
      final user = await GetIt.I<AuthLocalDataSource>().getCachedUserData();
      final userId = user?.id ?? '';

      if (userId.isEmpty) {
        emit(const TransactionError(message: 'User ID not available'));
        return;
      }

      final result = await getRecentWalletTransfers(
        RecentWalletTransfersParams(
          limit: event.limit,
          currentUserId: userId,
        ),
      );

      result.fold((failure) => emit(TransactionError(message: failure.message)),
          (transfers) {
        debugPrint('Recent wallet transfes ${transfers.length}');
        emit(
          RecentWalletTransfersLoaded(
            transfers: transfers,
            fetchedAt: DateTime.now(),
          ),
        );
      });
    } catch (e) {
      emit(TransactionError(message: e.toString()));
    }
  }

  Future<void> _onFetchTransferLimit(
    FetchTransferLimitEvent event,
    Emitter<TransactionState> emit,
  ) async {
    emit(const TransactionLoading());

    try {
      final result = await getTransferLimit(
        GetTransferLimitParams(
          transactionType: event.transactionType,
          currency: event.currency,
        ),
      );

      result.fold(
        (failure) => emit(TransactionError(message: failure.message)),
        (transferLimit) {
          emit(
            TransferLimitLoaded(
              transferLimit: transferLimit,
              fetchedAt: DateTime.now(),
            ),
          );
        },
      );
    } catch (e) {
      emit(TransactionError(message: e.toString()));
    }
  }

  Future<void> _onGetLoanInvoice(
    GetLoanInvoiceEvent event,
    Emitter<TransactionState> emit,
  ) async {
    emit(const TransactionLoading());

    try {
      final result = await getLoanInvoice(
        GetLoanInvoiceParams(
          billRefNo: event.billRefNo,
          loanReceipt: event.loanReceipt,
        ),
      );

      result.fold(
        (failure) => emit(TransactionError(message: failure.message)),
        (invoiceUrl) => emit(
          LoanInvoiceLoaded(
            invoiceUrl: invoiceUrl,
          ),
        ),
      );
    } catch (e) {
      emit(TransactionError(message: e.toString()));
    }
  }

  Future<void> _onConfirmTransfer(
    ConfirmTransferEvent event,
    Emitter<TransactionState> emit,
  ) async {
    emit(ConfirmTransferLoading());

    debugPrint(
        "bill ${event.billRefNo} pin ${event.pin} and transac ${event.transactionType}");

    try {
      final result = await confirmTransfer(
        ConfirmTransferParams(
          pin: event.pin,
          billRefNo: event.billRefNo,
          transactionType: event.transactionType,
          otp: event.otp,
        ),
      );

      result.fold(
        (failure) => emit(
          PinErrorState(
            message: failure.message,
            statusCode: failure.statusCode,
            billRefNo: event.billRefNo,
          ),
        ),
        (response) {
          debugPrint('sthhtht');
          emit(ConfirmTransferSuccess(response));
        },
      );
    } catch (e) {
      emit(
        ConfirmTransferError(
          message: e.toString(),
          billRefNo: event.billRefNo,
        ),
      );
    }
  }

  Future<void> _onVerifyOtp(
    VerifyOtpEvent event,
    Emitter<TransactionState> emit,
  ) async {
    emit(OtpVerificationLoading());

    try {
      debugPrint(
        'So we were here with hmm ${event.otpCode} and ${event.otpFor} and ${event.billRefNo}',
      );

      final result = await verifyTransactionOtp(
        VerifyOtpParams(
          billRefNo: event.billRefNo,
          otpFor: event.otpFor,
          otpCode: event.otpCode,
        ),
      );

      result.fold(
        (failure) => emit(
          OtpVerificationError(
            message: failure.message,
            statusCode: failure.statusCode,
          ),
        ),
        (response) => emit(OtpVerificationSuccess(result)),
      );
    } catch (e) {
      emit(OtpVerificationError(message: e.toString()));
    }
  }

  Future<void> _onResendOtp(
    ResendOtpEvent event,
    Emitter<TransactionState> emit,
  ) async {
    emit(OtpResendLoading());

    try {
      final result = await resendTransactionOtp(
        ResendOtpParams(
          billRefNo: event.billRefNo,
          otpFor: event.otpFor,
        ),
      );

      result.fold(
        (failure) => emit(
          OtpResendError(
            message: failure.message,
            statusCode: failure.statusCode,
          ),
        ),
        (response) => emit(
          OtpResendSuccess(
            response: response,
            timestamp: DateTime.now(),
          ),
        ),
      );
    } catch (e) {
      emit(OtpResendError(message: e.toString()));
    }
  }

  Future<void> _onDownloadReceipt(
    DownloadReceiptEvent event,
    Emitter<TransactionState> emit,
  ) async {
    debugPrint('Downloading receipt for billRefNo: ${event.billRefNo}');
    emit(ReceiptDownloadLoading());

    try {
      debugPrint('Calling getInvoice...');
      final result = await getInvoice(event.billRefNo);
      debugPrint('getInvoice result received');

      result.fold(
        (failure) {
          debugPrint('Receipt download failed: ${failure.message}');
          emit(
            ReceiptDownloadError(
              message: failure.message,
              statusCode: failure.statusCode,
            ),
          );
        },
        (receipt) async {
          final receiptUrl = receipt.invoiceUrl;
          debugPrint('Receipt URL: $receiptUrl');

          try {
            final uri = Uri.parse(receiptUrl);
            debugPrint('Checking if URL can be launched: $uri');
            final canLaunch = await canLaunchUrl(uri);
            debugPrint('Can launch URL: $canLaunch');

            if (canLaunch) {
              debugPrint('Launching URL...');
              final launched = await launchUrl(uri, mode: LaunchMode.externalApplication);
              debugPrint('URL launched: $launched');

              emit(ReceiptDownloadSuccess(
                receiptUrl: receiptUrl,
                billRefNo: event.billRefNo,
              ));
            } else {
              debugPrint('Could not launch receipt URL');
              emit(ReceiptDownloadError(
                message: 'Could not launch receipt URL',
              ));
            }
          } catch (e) {
            debugPrint('Error launching URL: ${e.toString()}');
            emit(ReceiptDownloadError(
              message: 'Error launching URL: ${e.toString()}',
            ));
          }
        },
      );
    } catch (e) {
      debugPrint('Exception in _onDownloadReceipt: ${e.toString()}');
      emit(ReceiptDownloadError(message: e.toString()));
    }
  }
}
