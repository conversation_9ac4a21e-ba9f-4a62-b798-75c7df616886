import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/money_request/domain/entities/wallet_detail_entity.dart';
import 'package:cbrs/features/money_request/domain/repositories/money_request_repository.dart';

class GetWalletDetail extends UsecaseWithoutParams<WalletEntity> {
  GetWalletDetail(this._repository);

  final MoneyRequestRepository _repository;

  @override
  ResultFuture<WalletEntity> call() => _repository.getWalletDetail();
}
