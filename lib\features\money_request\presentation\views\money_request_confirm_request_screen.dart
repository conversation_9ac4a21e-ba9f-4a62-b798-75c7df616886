import 'package:cbrs/core/common/widgets/custom_app_bar.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_pin_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/custom_pin_screen.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/money_request/domain/entities/my_requests_list_entity.dart';
import 'package:cbrs/features/money_request/presentation/bloc/money_request_detail/money_request_detail_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class MoneyRequestConfirmRequestScreen extends StatefulWidget {
  const MoneyRequestConfirmRequestScreen({
    required this.moneyRequest,
    super.key,
  });

  final MoneyRequestEntity moneyRequest;

  @override
  State<MoneyRequestConfirmRequestScreen> createState() =>
      _MoneyRequestConfirmRequestScreenState();
}

class _MoneyRequestConfirmRequestScreenState
    extends State<MoneyRequestConfirmRequestScreen> {
  @override
  Widget build(BuildContext context) {
    final isUSD = widget.moneyRequest.currency == 'USD';
    return BlocConsumer<MoneyRequestDetailBloc, MoneyRequestDetailState>(
      listener: (context, state) {
        if (state is MoneyRequestDetailActionState && state.isAcceptSuccess) {
          _handleSubmitPin(context);
          return;
        }

        if (state is MoneyRequestDetailErrorState) {
          CustomToastification(context, message: state.message);
        }

        if (state is MoneyRequestDetailActionState &&
            state.message.isNotEmpty) {
          CustomToastification(context, message: state.message);
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: CustomAppBar(
            context: context,
            title: 'Confirm Transfer',
          ),
          body: SafeArea(
            child: Padding(
              padding: EdgeInsets.only(
                top: 21.h,
                left: 16.w,
                right: 16.w,
                bottom: 10.w,
              ),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const CustomPageHeader(
                            pageTitle: 'Confirm Transfer',
                            description:
                                'Review the amount and request details, then confirm the transfer.',
                          ),
                          SizedBox(height: 75.h),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 24.h,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16.r),
                              boxShadow: const [
                                BoxShadow(
                                  color: Color(0x0F000000),
                                  blurRadius: 24,
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Center(
                                  child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      '${widget.moneyRequest.billAmount} '
                                      '${widget.moneyRequest.currency}',
                                      style: GoogleFonts.outfit(
                                        fontSize: 40.sp,
                                        fontWeight: FontWeight.w800,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 36.h),
                                Text(
                                  'Transaction Details',
                                  style: GoogleFonts.outfit(
                                    fontSize: 18.sp,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                SizedBox(height: 16.h),
                                _buildTransactionDetail(
                                  context,
                                  'Requested From:',
                                  widget.moneyRequest.senderName,
                                ),
                                _buildTransactionDetail(
                                  context,
                                  'Requested Email:',
                                  widget.moneyRequest.senderEmail,
                                ),
                                _buildTransactionDetail(
                                  context,
                                  'Date:',
                                  DateFormat('MMM dd, yyyy')
                                      .format(widget.moneyRequest.createdAt),
                                ),
                                Padding(
                                  padding: EdgeInsets.symmetric(vertical: 16.h),
                                  child: CustomPaint(
                                    painter: DottedLinePainter(
                                      color: const Color(0xFF065234)
                                          .withOpacity(0.1),
                                    ),
                                    size: const Size(double.infinity, 0.6),
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8.h),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Total:',
                                        style: GoogleFonts.outfit(
                                          fontSize: 16.sp,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                      Flexible(
                                        child: Text(
                                          '${widget.moneyRequest.billAmount} '
                                          '${widget.moneyRequest.currency}',
                                          style: GoogleFonts.outfit(
                                            fontSize: 24.sp,
                                            color: Colors.black,
                                            fontWeight: FontWeight.w700,
                                          ),
                                          textAlign: TextAlign.end,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  CustomButton(
                    text: 'Continue',
                    onPressed: () {
                      context.read<MoneyRequestDetailBloc>().add(
                            AcceptRequestActionEvent(
                              transactionID: widget.moneyRequest.transactionId,
                              amount: widget.moneyRequest.billAmount,
                            ),
                          );
                    },
                    options: CustomButtonOptions(
                      height: 56.h,
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      color: isUSD
                          ? LightModeTheme().primaryColorUSD
                          : LightModeTheme().primaryColorBirr,
                      textStyle: GoogleFonts.outfit(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                      borderRadius: BorderRadius.circular(32),
                      disabledTextColor: Colors.white,
                      disabledColor: Colors.grey[350],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  final TextEditingController pinController = TextEditingController();

  bool _isCheckingPin = false;

  void _handleSubmitPin(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => StatefulBuilder(
        builder: (BuildContext context, StateSetter setModalState) {
          return BlocConsumer<MoneyRequestDetailBloc, MoneyRequestDetailState>(
            bloc: context.read<MoneyRequestDetailBloc>(),
            listener: (context, state) {
              if (state is MoneyRequestDetailErrorState) {
                _resetPinState(setModalState);
                CustomToastification(
                  context,
                  message: state.message,
                );
              } else if (state is MoneyRequestDetailResultState &&
                  state.isSuccess) {
                context.pop();
                context.pushNamed(
                  AppRouteName.sendMoneyRequestSuccessScreen,
                  extra: widget.moneyRequest,
                );
              }
            },
            builder: (context, state) => CustomPinScreen(
              isLoading: _isCheckingPin,
              controller: pinController,
              onSubmitted: (value) async {
                setState(() => _isCheckingPin = true);
                context.read<MoneyRequestDetailBloc>().add(
                      MoneyRequestDetailConfirmPinEvent(
                        pin: value,
                        billRefNo: widget.moneyRequest.billRefNo,
                      ),
                    );
              },
              onChanged: (keys, isKey) {
                if (_isCheckingPin) return;

                setModalState(() {
                  if (!isKey) {
                    pinController.text = pinController.text.isNotEmpty
                        ? pinController.text
                            .substring(0, pinController.text.length - 1)
                        : '';
                    pinController.selection = TextSelection.fromPosition(
                      TextPosition(offset: pinController.text.length),
                    );
                    return;
                  }
                  pinController.text = "${pinController.text}$keys";
                  pinController.selection = TextSelection.fromPosition(
                    TextPosition(offset: pinController.text.length),
                  );
                });
              },
            ),
          );
        },
      ),
    );
  }

  Future<void> _resetPinState(StateSetter setModalState) async {
    setModalState(() {
      _isCheckingPin = false;
      pinController.clear();
    });
  }

  Widget _buildTransactionDetail(
      BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.outfit(
              color: Colors.black.withOpacity(0.5),
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.outfit(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
