import 'package:flutter/material.dart';

class SelfieOverlayPainter extends CustomPainter {
  final bool isFaceDetected;
  final Color color;

  SelfieOverlayPainter({
    required this.isFaceDetected,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Main oval
    final paint = Paint()
      ..color = isFaceDetected 
          ? Colors.green.withOpacity(0.8)
          : color.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 5
      ..shader = LinearGradient(
        colors: isFaceDetected ? [
          Colors.green.withOpacity(0.9),
          Colors.green.withOpacity(0.6),
        ] : [
          color.withOpacity(0.9),
          color.withOpacity(0.6),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final ovalRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height * 0.4),
      width: size.width * 0.8,
      height: size.height * 0.5,
    );

    canvas.drawOval(ovalRect, paint);

    // Guide lines with gradient effect
    final guidePaint = Paint()
      ..shader = LinearGradient(
        colors: [
          color.withOpacity(0.3),
          color.withOpacity(0.1),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // Horizontal center line
    canvas.drawLine(
      Offset(size.width * 0.1, size.height * 0.4),
      Offset(size.width * 0.9, size.height * 0.4),
      guidePaint,
    );

    // Vertical center line
    canvas.drawLine(
      Offset(size.width / 2, size.height * 0.15),
      Offset(size.width / 2, size.height * 0.65),
      guidePaint,
    );

    // Corner indicators
    final cornerPaint = Paint()
      ..color = color.withOpacity(0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    const cornerSize = 20.0;
    const cornerOffset = 10.0;

    // Top-left corner
    canvas.drawLine(
      Offset(cornerOffset, size.height * 0.25),
      Offset(cornerOffset + cornerSize, size.height * 0.25),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(cornerOffset, size.height * 0.25),
      Offset(cornerOffset, size.height * 0.25 + cornerSize),
      cornerPaint,
    );

    // Top-right corner
    canvas.drawLine(
      Offset(size.width - cornerOffset, size.height * 0.25),
      Offset(size.width - cornerOffset - cornerSize, size.height * 0.25),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(size.width - cornerOffset, size.height * 0.25),
      Offset(size.width - cornerOffset, size.height * 0.25 + cornerSize),
      cornerPaint,
    );

    // Bottom-left corner
    canvas.drawLine(
      Offset(cornerOffset, size.height * 0.55),
      Offset(cornerOffset + cornerSize, size.height * 0.55),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(cornerOffset, size.height * 0.55),
      Offset(cornerOffset, size.height * 0.55 - cornerSize),
      cornerPaint,
    );

    // Bottom-right corner
    canvas.drawLine(
      Offset(size.width - cornerOffset, size.height * 0.55),
      Offset(size.width - cornerOffset - cornerSize, size.height * 0.55),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(size.width - cornerOffset, size.height * 0.55),
      Offset(size.width - cornerOffset, size.height * 0.55 - cornerSize),
      cornerPaint,
    );
  }

  @override
  bool shouldRepaint(SelfieOverlayPainter oldDelegate) =>
      isFaceDetected != oldDelegate.isFaceDetected ||
      color != oldDelegate.color;
}
