import 'package:flutter/material.dart';

class FormValidation {
  static String? validateName(String? name, String fieldName) {
    if (name == null || name.isEmpty) return '$fieldName is required';
    if (name.length <= 1) return '$fieldName is too short';
    if (!RegExp(r'^[a-zA-Z ]+$').hasMatch(name))
      return '$fieldName can only contain letters';
    if (name.contains('\t')) return 'No tabs allowed in $fieldName';
    return null;
  }

  static String? validateGender(String? gender) {
    if (gender == null || gender.isEmpty) {
      return 'Gender is required';
    }
    return null;
  }

  /// TODO 🏆 Needs re-addressed
  static String? validateDateOfBirth(String? dateOfBirth) {
    if (dateOfBirth == null || dateOfBirth.isEmpty) {
      return 'Date of birth is required';
    }
    return null;
  }

  static String? validateEmailWithoutPhone(
    String? email,
  ) {
    // If both email and phone are empty
    if (email == null || email.isEmpty) {
      return 'Email is required.';
    }

    // If email is provided, validate its format

    final emailRegExp = RegExp(r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$');
    if (!emailRegExp.hasMatch(email)) {
      return 'Please enter a valid email address';
    }

    const disposableDomains = [
      'tempmail.com',
      '10minutemail.com',
      'mailinator.com',
      'guerrillamail.com',
      'yopmail.com',
      'temp-mail.org',
      'emailondeck.com',
      ' throwawaymail.com',
      'getnada.com',
      'maildrop.cc',
      'mohmal.com',
      'fakemail.net',
      'trashmail.com',
      'mytemp.email',
      'easytrashmail.com',
      'dispostable.com',
      'spamgourmet.com',
      'burnermail.io',
      'inboxkitten.com',
      'minuteinbox.com',
      'e4ward.com',
    ];

    // Check for disposable email domains
    final domain = email.split('@').last;
    if (disposableDomains.contains(domain)) {
      return 'Disposable emails are not allowed. Use a valid email.';
    }

    return null;
  }

  static String? validateEmail(
    String? email,
    String? phone, {
    bool isOptional = false,
  }) {
    // If both email and phone are empty
    if ((email == null || email.isEmpty) && (phone == null || phone.isEmpty)) {
      return 'Either email or phone number is required.';
    }

    // If email is empty but phone is provided, validation passes
    if ((email == null || email.isEmpty) && phone != null && phone.isNotEmpty) {
      return null; // Changed from returning empty string to null
    }

    // If email is provided, validate its format
    if (email != null && email.isNotEmpty) {
      final emailRegExp =
          RegExp(r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$');
      if (!emailRegExp.hasMatch(email)) {
        return 'Please enter a valid email address';
      }

      const disposableDomains = [
        'tempmail.com',
        '10minutemail.com',
        'mailinator.com',
        'guerrillamail.com',
        'yopmail.com',
        'temp-mail.org',
        'emailondeck.com',
        ' throwawaymail.com',
        'getnada.com',
        'maildrop.cc',
        'mohmal.com',
        'fakemail.net',
        'trashmail.com',
        'mytemp.email',
        'easytrashmail.com',
        'dispostable.com',
        'spamgourmet.com',
        'burnermail.io',
        'inboxkitten.com',
        'minuteinbox.com',
        'e4ward.com',
      ];

      // Check for disposable email domains
      final domain = email.split('@').last;
      if (disposableDomains.contains(domain)) {
        return 'Disposable emails are not allowed. Use a valid email.';
      }
    }

    return null;
  }

  /// TODO 🏆 Needs re-addressed - coz - validation of phone and email concurrently checked over
  static String validatePhoneNumber(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return 'Phone number is required';
    }
    return '';
  }

  static String? validateCity(String? name) {
    if (name == null || name.isEmpty) return 'City is required.';
    if (name.length < 3) return 'City name is too short.';
    if (!RegExp(r'^[a-zA-Z ]+$').hasMatch(name)) return 'Invalid city name.';
    return null;
  }

  static String? validateCountry(String? name) {
    if (name == null || name.isEmpty) return 'Country is required.';

    return null;
  }

  static String? validatePin(String? PIN, String fieldName) {
    if (PIN == null || PIN.isEmpty) return '$fieldName is required.';
    if (PIN.length != 6) {
      return '$fieldName must be exactly 6 digits long';
    }

    if (PIN.contains(RegExp(r'\D')))
      return '$fieldName must contain only numbers';

    // Validation for repeating digits (e.g., 111111)
    final repeatingPattern = RegExp(r'^(\d)\1{5}$');
    if (repeatingPattern.hasMatch(PIN)) {
      return 'Your $fieldName cannot contain identical digits';
    }

    final sequentialPattern = RegExp(
      '(123456|234567|345678|456789|567890|012345)',
    );
    // final sequentialPattern = RegExp(
    //     r'(012|123|234|345|456|567|678|789|890|987|876|765|654|543|432|321|210)');
    if (sequentialPattern.hasMatch(PIN)) {
      return 'Your $fieldName cannot be a sequential series';
    }

    if (_isAlternatingPattern(PIN)) {
      return '$fieldName cannot contain alternating patterns';
    }

    return null; // PIN is valid
  }

  static bool _isAlternatingPattern(String pin) {
    if (pin.length % 2 == 0) {
      final part1 = pin.substring(0, 2);
      final part2 = pin.substring(2, 4);
      final part3 = pin.substring(4);

      debugPrint('Part one: $part1');
      debugPrint('Part one: $part2');

      if (part1 == part2 || part2 == part3) {
        return true;
      }
    }
    return false;
  }

  static String? validateConfirmPIN(String? confirmPIN, String PIN) {
    final pinValidationMessage = validatePin(confirmPIN, 'Confirm Pin');
    if (pinValidationMessage != null) return pinValidationMessage;
    if (confirmPIN != PIN) {
      return 'The confirmation PIN does not match the new PIN.';
    }
    return null;
  }

static String? validateNewPin(String? newPIN, String oldPIN) {
  final pinError = validatePin(newPIN, 'New Pin');
  if (pinError != null) return pinError;

  if (oldPIN == newPIN) {
    return 'You cannot reuse your old PIN. Please choose a new one.';
  }

  return null;
}


  static String? validateOTP(String? otp, int triedTimes, int maxAttempts) {
    if (otp == null || otp.isEmpty) {
      return 'OTP is required';
    }
    if (otp.contains(RegExp(r'\D'))) return 'OTP must contain only numbers';

    if (triedTimes == maxAttempts)
      return 'You have exceeded the maximum number of attempts. Please try again later.';
    return null;
  }
}
