part of 'transaction_bloc.dart';

abstract class TransactionState extends Equatable {
  const TransactionState();

  @override
  List<Object?> get props => [];
}

class TransactionInitial extends TransactionState {}

class TransactionLoading extends TransactionState {
  const TransactionLoading({this.keepCache = true});
  final bool keepCache;

  @override
  List<Object?> get props => [keepCache];
}

class TransactionLoaded extends TransactionState {
  const TransactionLoaded({
    required this.allTransactions,
    required this.groupedTransactions,
    required this.bloc,
    this.currentFilter = 'All',
    this.startDate,
    this.endDate,
    this.isRefreshing = false,
  });
  final List<Transaction> allTransactions;
  final List<TransactionGroup> groupedTransactions;
  final String currentFilter;
  final DateTime? startDate;
  final DateTime? endDate;
  final TransactionBloc bloc;
  final bool isRefreshing;

  TransactionLoaded copyWith({
    List<Transaction>? allTransactions,
    List<TransactionGroup>? groupedTransactions,
    String? currentFilter,
    DateTime? startDate,
    DateTime? endDate,
    bool? isRefreshing,
  }) {
    return TransactionLoaded(
      allTransactions: allTransactions ?? this.allTransactions,
      groupedTransactions: groupedTransactions ?? this.groupedTransactions,
      bloc: bloc,
      currentFilter: currentFilter ?? this.currentFilter,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  List<Transaction> getRecentTransactions() {
    return allTransactions.take(5).toList();
  }

  Future<List<TransactionWithAvatar>> getRecentWalletTransferUsers() async {
    final currentUser = await bloc.authLocalDataSource.getUserId();
    if (currentUser == null) {
      return [];
    }

    debugPrint(
      '🔍 Starting to process wallet transfers for user: $currentUser',
    );
    debugPrint('📊 Total transactions in state: ${allTransactions.length}');

    // Filter wallet transfers and sort by date
    final walletTransfers = allTransactions
        .where(
          (t) =>
              t.transactionType.toLowerCase() == 'wallet_transfer' &&
              t.senderId != t.beneficiaryId,
        ) // Exclude self-transfers
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    // Create a map of unique users with their most recent transaction
    final uniqueUsers = <String, Transaction>{};
    for (final transaction in walletTransfers) {
      if (transaction.senderId == currentUser) {
        if (!uniqueUsers.containsKey(transaction.beneficiaryId)) {
          uniqueUsers[transaction.beneficiaryId ?? ''] = transaction;
        }
      } else {
        if (!uniqueUsers.containsKey(transaction.senderId)) {
          uniqueUsers[transaction.senderId] = transaction;
        }
      }
    }

    final uniqueTransfers = uniqueUsers.values.take(5).toList();
    debugPrint('💳 Found ${uniqueTransfers.length} unique wallet transfers');

    // Fetch avatars for unique users
    final userIds = uniqueTransfers
        .map((t) => t.senderId == currentUser ? t.beneficiaryId : t.senderId)
        .where((id) => id != null)
        .toSet()
        .toList();

    debugPrint('🆔 Unique user IDs to fetch: ${userIds.length}');
    final avatars = await bloc.fetchUserAvatars(uniqueTransfers);
    debugPrint('🖼️ Fetched ${avatars.length} avatars');

    return uniqueTransfers.map((t) {
      final otherPartyId =
          t.senderId == currentUser ? t.beneficiaryId : t.senderId;
      final avatar = otherPartyId != null ? avatars[otherPartyId] : null;
      final displayName = t.beneficiaryName;

      return TransactionWithAvatar(
        transaction: t,
        avatar: avatar,
        displayName: displayName,
      );
    }).toList();
  }

  @override
  List<Object?> get props =>
      [allTransactions, groupedTransactions, currentFilter, startDate, endDate];
}

class TransactionError extends TransactionState {
  const TransactionError({
    required this.message,
    this.statusCode,
  });
  final String message;
  final int? statusCode;

  @override
  List<Object?> get props => [message, statusCode];
}

class RecentWalletTransfersLoaded extends TransactionState {
  const RecentWalletTransfersLoaded({
    required this.transfers,
    required this.fetchedAt,
  });
  final List<TransactionWithAvatar> transfers;
  final DateTime fetchedAt;

  @override
  List<Object?> get props => [transfers, fetchedAt];
}

class TransferLimitLoaded extends TransactionState {
  const TransferLimitLoaded({
    required this.transferLimit,
    required this.fetchedAt,
  });
  final TransferLimit transferLimit;
  final DateTime fetchedAt;

  @override
  List<Object?> get props => [transferLimit, fetchedAt];
}

class LoanInvoiceLoaded extends TransactionState {
  const LoanInvoiceLoaded({
    required this.invoiceUrl,
  });
  final String invoiceUrl;

  @override
  List<Object?> get props => [invoiceUrl];
}

class ConfirmTransferLoading extends TransactionState {}

class ConfirmTransferSuccess extends TransactionState {

  const ConfirmTransferSuccess(this.transaction);
  final ConfirmTransferResponse transaction;

  @override
  List<Object?> get props => [transaction];
}

class ConfirmTransferError extends TransactionState {

  const ConfirmTransferError({
    required this.message,
    this.statusCode,
    this.billRefNo,
  });
  final String message;
  final int? statusCode;
  final String? billRefNo;

  @override
  List<Object?> get props => [message, statusCode, billRefNo];
}

class PinErrorState extends TransactionState {

  const PinErrorState({
    required this.message,
    this.statusCode,
    this.billRefNo,
  });
  final String message;
  final int? statusCode;
  final String? billRefNo;

  @override
  List<Object?> get props => [message, statusCode, billRefNo];
}

class OtpVerificationLoading extends TransactionState {}

class OtpVerificationSuccess extends TransactionState {
  const OtpVerificationSuccess(isSucess);
  bool get isSucess => true;

  @override
  List<Object?> get props => [isSucess];
}

class OtpVerificationError extends TransactionState {

  const OtpVerificationError({
    required this.message,
    this.statusCode,
  });
  final String message;
  final int? statusCode;

  @override
  List<Object?> get props => [message, statusCode];
}

class OtpResendLoading extends TransactionState {}

class OtpResendSuccess extends TransactionState {

  const OtpResendSuccess({
    required this.response,
    required this.timestamp,
  });
  final dynamic response;
  final DateTime timestamp;

  @override
  List<Object?> get props => [response, timestamp];
}

class OtpResendError extends TransactionState {

  const OtpResendError({
    required this.message,
    this.statusCode,
  });
  final String message;
  final int? statusCode;

  @override
  List<Object?> get props => [message, statusCode];
}

class ReceiptDownloadLoading extends TransactionState {}

class ReceiptDownloadSuccess extends TransactionState {

  const ReceiptDownloadSuccess({
    required this.receiptUrl,
    required this.billRefNo,
  });
  final String receiptUrl;
  final String billRefNo;

  @override
  List<Object?> get props => [receiptUrl, billRefNo];
}

class ReceiptDownloadError extends TransactionState {

  const ReceiptDownloadError({
    required this.message,
    this.statusCode,
  });
  final String message;
  final int? statusCode;

  @override
  List<Object?> get props => [message, statusCode];
}
