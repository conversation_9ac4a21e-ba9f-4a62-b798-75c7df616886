// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

import 'package:cbrs/features/auth/data/models/user_dto.dart';
import 'package:cbrs/features/home/<USER>/models/home_state_model.dart';

abstract class HomeState extends Equatable {
  const HomeState();

  @override
  List<Object?> get props => [];
}

class HomeInitial extends HomeState {}

class HomeLoading extends HomeState {}

class HomeLoaded extends HomeState {
  const HomeLoaded(this.data);
  final HomeStateModel data;

  @override
  List<Object?> get props => [data];
}

class HomeError extends HomeState {
  const HomeError(this.message);
  final String message;

  @override
  List<Object?> get props => [message];
}

class WalletLoadingState extends HomeState {
  @override
  List<Object?> get props => [];
}


class ProfileLoadingState extends HomeState {
  @override
  List<Object?> get props => [];
}

class HomeProfileLoadedState extends HomeState {
  final LocalUserDTO localUser;
  const HomeProfileLoadedState({
    required this.localUser,
  });
}

class WalletLoadedState extends HomeState {
  const WalletLoadedState({
    required this.etbBalance,
    required this.usdBalance,
    this.isUsdWallet = true,
  });
  final double etbBalance;
  final double usdBalance;
  final bool isUsdWallet;

  @override
  List<Object?> get props => [etbBalance, usdBalance, isUsdWallet];
}
