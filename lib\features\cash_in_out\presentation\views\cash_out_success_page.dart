import 'package:cbrs/core/common/widgets/success/custom_transaction_success_screen.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class CashOutSuccessPage extends StatelessWidget {
  final Map<String, dynamic> data;

  const CashOutSuccessPage({
    super.key,
    required this.data,
  });

  String _getDisplayValue(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'N/A';
    }
    return value;
  }

  double _getTotalAmount() {
    try {
      return double.parse(data['total'].toString());
    } catch (e) {
      return 0.0;
    }
  }

  Widget _buildTransactionDetails(BuildContext context) {
    String senderName = '';
    final userState = context.read<HomeBloc>().state;
    if (userState is HomeProfileLoadedState) {
      senderName = userState.localUser.fullName;
    }

    return Column(
      children: [
        _buildTransactionDetail('Sender Name', senderName),
        _buildTransactionDetail('Agent Name', data['agentName'].toString()),
        _buildTransactionDetail('Agent ID', data['agentCode'].toString()),
        _buildTransactionDetail(
            'Payment Reference', data['paymentReference'].toString()),
        _buildTransactionDetail('Payment Date', data['paidDate'].toString()),
        _buildTransactionDetail('Amount in ETB', data['amount'].toString()),
        _buildTransactionDetail(
            'Service Fee', data['serviceCharge'].toString()),
        _buildTransactionDetail('VAT(15%)', data['vat'].toString()),
      ],
    );
  }

  Widget _buildTransactionDetail(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 4,
            child: Text(
              label,
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                color: const Color(0xFF757575),
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Text(
              _getDisplayValue(value),
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  void _handleBackPress(BuildContext context) {
    context.goNamed(AppRouteName.home);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _handleBackPress(context);
        return false;
      },
      child: CustomTransactionSuccessScreen(
        alwaysShowETBColor: true,
        pageDescription:
            'Your cash out transaction was completed successfully.',
        totalAmount: _getTotalAmount(),
        isBirrTransfer: true,
        hasPageTitle: true,
        child: _buildTransactionDetails(context),
        onQrTap: () {
          // Implement QR functionality
        },
        onShareTap: () {
          // Implement share functionality
        },
        onGetRecieptTap: () {
          // Implement receipt functionality
        },
      ),
    );
  }
}
