import 'dart:ui';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:cbrs/core/services/routes/route_name.dart';

void showGuestModeBottomSheet(BuildContext context) {
  debugPrint('showGuestModeBottomSheet called');

  showModalBottomSheet<void>(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (BuildContext context) {
      debugPrint('Building guest mode bottom sheet');
      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 2,
            sigmaY: 2,
          ),
          child: SafeArea(
            child: Container(
              padding: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 24.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 16.h),
                  Center(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Theme.of(context).primaryColor,
                          width: 3,
                        ),
                        shape: BoxShape.circle,
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          'assets/icons/Profile.png',
                          fit: BoxFit.cover,
                          width: 100.h,
                          height: 100.h,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Guest Mode',
                    style: GoogleFonts.outfit(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'You are in guest mode. Sign up or log in to your account to access your wallet and enjoy all features.',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      color: Colors.black.withOpacity(0.6),
                    ),
                  ),
                  SizedBox(height: 36.h),
                  CustomRoundedBtn(
                    isLoading: false,
                    btnText: 'Sign Up',
                    onTap: () => context.go(AppRouteName.signUp),
                  ),
                  SizedBox(height: 12.h),
                  CustomRoundedBtn(
                    isLoading: false,
                    btnText: 'Login',
                    bgColor: Colors.white,
                    textColor: Theme.of(context).primaryColor,
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                    ),
                    onTap: () => context.go(AppRouteName.signIn),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}
