import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/add_money/data/datasources/add_money_remote_datasource.dart';
import 'package:cbrs/features/add_money/data/models/linked_account_response_model.dart';
import 'package:cbrs/features/add_money/data/models/add_money_response_model.dart';
import 'package:cbrs/features/send_money/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/send_money/data/models/otp_resend_response.dart';
import 'package:flutter/foundation.dart';

class AddMoneyRemoteDataSourceImpl implements AddMoneyRemoteDataSource {
  const AddMoneyRemoteDataSourceImpl({
    required ApiService apiService,
  }) : _apiService = apiService;

  final ApiService _apiService;

  @override
  Future<PaginatedLinkedAccountsResponseModel> getLinkedAccounts({
    required int page,
    required int limit,
    String status = 'LINKED',
  }) async {
    final result = await _apiService.get(
      ApiEndpoints.linkedAccountsPaginate,
      queryParameters: {
        'page': page,
        'limit': limit,
        'status': status,
      },
      parser: (data) => PaginatedLinkedAccountsResponseModel.fromJson(
        data as Map<String, dynamic>,
      ),
    );

    return result.fold(
      (data) {
        debugPrint('get linked account $data');
        return data;
      },
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<LinkedAccountResponseModel> checkAccountBalance({
    required String bankId,
    required String accountNumber,
  }) async {
    final result = await _apiService.post(
      ApiEndpoints.checkLinkedAccountBalance,
      data: {
        'bank': bankId,
        'accountNumber': accountNumber,
      },
      parser: (data) => LinkedAccountResponseModel.fromJson(
        (data as Map<String, dynamic>)['data'] as Map<String, dynamic>,
      ),
    );

    return result.fold(
      (data) => data,
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
    required String bankId,
    required String accountNumber,
  }) async {
    debugPrint(
      '🔍 Checking transfer rules for amount $amount currency $currency '
      'bank id $bankId account number $accountNumber',
    );

    final result = await _apiService.post(
      ApiEndpoints.validateTransferAmount,
      data: {
        'accountNumber': accountNumber,
        'bankID': bankId,
        'amount': amount,
        'currency': 'ETB',
        'productType': 'add_money',
      },
      parser: (data) {
        debugPrint('📡 Transfer rules API response: $data');
        final parsed = CheckTransferRulesResponse.fromJson(
          data as Map<String, dynamic>,
        );
        debugPrint('✨ Parsed transfer rules response: $parsed');
        return parsed;
      },
    );

    return result.fold(
      (data) => data,
      (error) {
        debugPrint('❌ Transfer rules check failed: ${error.message}');
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        );
      },
    );
  }

  @override
  Future<AddMoneyResponseModel> addMoney({
    required String accountNumber,
    required String bankId,
    required double amount,
    required String currency,
    required String senderName,
  }) async {
    final postData = {
      'beneficiaryAccountNo': accountNumber,
      'bankID': bankId,
      'amount': amount,
      'currency': currency,
      'transactionType': 'add_money',
    };

    final result = await _apiService.post(
      ApiEndpoints.generateBill,
      data: postData,
      parser: (data) => data as Map<String, dynamic>,
    );

    /*

    final result = await _apiService.post(
      ApiEndpoints.generateBill,
      data:postData
       final result = await _apiService.post(
      ApiEndpoints.generateBill,
      data: postData,
      parser: (data) => data as Map<String, dynamic> 
    );
      
   
      parser: (data) {
        debugPrint('📡 Add money API response: $data');

        if (data is String) {
          throw ApiException(
            message: data,
            statusCode: 500,
          );
        }

        final parsed = AddMoneyResponseModel.fromJson(
          data as Map<String, dynamic>,
        );
        debugPrint('✨ Parsed add money response: $parsed');
        return parsed;
      },


    );

*/
    return result.fold(
      (data) {
        final responseData = data['data'];
        final returnData = AddMoneyResponseModel.fromJson(
            responseData as Map<String, dynamic>);

        return returnData;
      },
      (error) {
        debugPrint('❌ Add money failed: ${error.message}');
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        );
      },
    );
  }

  @override
  Future<AddMoneyResponseModel> submitPin({
    required String pin,
    required String billRefNo,
    String? otp,
  }) async {
    final result = await _apiService.post(
      ApiEndpoints.transferConfirmPayment,
      data: {
        'PIN': pin,
        'billRefNo': billRefNo,
        'transactionType': 'add_money',
        'authType': otp != null ? 'PIN_AND_OTP' : 'PIN',
        'otpCode': otp ?? '',
      },
      parser: (data) {
        debugPrint('📡 Submit PIN response: $data');
        return AddMoneyResponseModel.fromJson(data as Map<String, dynamic>);
      },
    );

    return result.fold(
      (data) => data,
      (error) {
        if (error.statusCode == 401) {
          throw const ApiException(
            message: 'Session expired. Please login again.',
            statusCode: 401,
          );
        }

        if (error.statusCode == 400) {
          throw ApiException(
            message: error.message,
            statusCode: 400,
          );
        }

        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        );
      },
    );
  }

  @override
  Future<OtpResendResponse> resendOtp({
    required String billRefNo,
    required String otpFor,
  }) async {
    final result = await _apiService.post(
      '${ApiEndpoints.baseUrl}/member-transactions/transfer/resend-otp',
      data: {
        'billRefNo': billRefNo,
        'otpFor': otpFor,
      },
      parser: (data) => OtpResendResponse.fromJson(
        data as Map<String, dynamic>,
      ),
    );

    return result.fold(
      (data) => data,
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<AddMoneyResponseModel> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  }) async {
    debugPrint('OTP Code: $otpCode');

    final result = await _apiService.post(
      '${ApiEndpoints.baseUrl}/member-transactions/transfer/verify-otp',
      data: {
        'billRefNo': billRefNo,
        'otpFor': otpFor,
        'otpCode': otpCode,
      },
      parser: (data) => AddMoneyResponseModel.fromJson(
        data as Map<String, dynamic>,
      ),
    );

    return result.fold(
      (data) => data,
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }
}
