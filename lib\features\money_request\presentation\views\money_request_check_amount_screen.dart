import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_app_bar.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/money_request/domain/entities/my_requests_list_entity.dart';
import 'package:cbrs/features/money_request/presentation/bloc/money_request_detail/money_request_detail_bloc.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/authorization_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

class MoneyRequestCheckAmountScreen extends StatefulWidget {
  const MoneyRequestCheckAmountScreen({required this.moneyRequest, super.key});

  final MoneyRequestEntity moneyRequest;

  @override
  State<MoneyRequestCheckAmountScreen> createState() =>
      _MoneyRequestCheckAmountScreenState();
}

class _MoneyRequestCheckAmountScreenState
    extends State<MoneyRequestCheckAmountScreen> {
  late CurrencyInputController _currencyController;
  bool _isLoading = false;
  double walletBalance = 0;

  final TextEditingController _pinController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;

  @override
  void initState() {
    super.initState();

    context.read<MoneyRequestDetailBloc>().add(
          const GetWalletDetailEvent(),
        );

    context.read<WalletBalanceBloc>().add(
          FetchWalletEvent(
            isUsdWallet: widget.moneyRequest.currency == 'USD',
            forceTheme: false,
          ),
        );

    final state = context.read<WalletBalanceBloc>().state;

    if (state is WalletLoadedState) {
      setState(() {
        walletBalance = state.isUsdWallet ? state.usdBalance : state.etbBalance;
      });
    }

    _currencyController = CurrencyInputController(
      currencyType: widget.moneyRequest.currency == 'USD'
          ? CurrencyType.usd
          : CurrencyType.etb,
      maxBalance: walletBalance,
      defaultAmount: widget.moneyRequest.billAmount,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.moneyRequest,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        // Accept the money request with PIN
        context.read<MoneyRequestDetailBloc>().add(
              AcceptRequestActionEvent(
                transactionID: widget.moneyRequest.transactionId,
                amount: widget.moneyRequest.billAmount,
              ),
            );
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    try {
      final amount = _currencyController.numericAmount;

      // Validate amount is greater than 0
      if (amount <= 0) {
        CustomToastification(
          context,
          message: 'Amount must be greater than 0',
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      // Update the money request amount
      widget.moneyRequest.billAmount = amount;

      // First check transfer rules like wallet transfer does
      context.read<WalletTransferBloc>().add(
            CheckWalletTransferRulesRequested(
              amount: amount,
              currency: widget.moneyRequest.currency,
            ),
          );
    } catch (e) {
      CustomToastification(
        context,
        message: 'Invalid Amount',
      );
    }
  }

  void _showConfirmScreenBottomSheet(WalletTransferResponse response) {
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Money Request Payment',
        'Sender Name': widget.moneyRequest.senderName,
        'Sender Email': widget.moneyRequest.senderEmail,
        'Amount':
            '${response.data.billAmount} ${response.data.originalCurrency}',
        'Service Charge':
            '${response.data.serviceCharge} ${response.data.originalCurrency}',
        'VAT': '${response.data.vat} ${response.data.originalCurrency}',
        'Date': AppMapper.safeFormattedDate(response.data.createdAt),
      },
      status: response.data.status,
      originalCurrency: response.data.originalCurrency,
      totalAmount: response.data.totalAmount,
      billAmount: response.data.billAmount,
      requiresOtp: response.data.authorization_type == 'PIN_AND_OTP',
      billRefNo: response.data.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
      totalAmount: transaction.totalAmount,
      billAmount: transaction.billAmount,
      originalCurrency: transaction.originalCurrency,
      transactionId: transaction.id,
      billRefNo: transaction.billRefNo,
      status: 'Paid',
      title: 'Money request payment completed successfully',
      {
        'Transaction Type': 'Money Request Payment',
        'Sender Name': widget.moneyRequest.senderName,
        'Sender Email': widget.moneyRequest.senderEmail,
        'Amount': '${transaction.billAmount} ${transaction.originalCurrency}',
        'Service Charge':
            '${transaction.serviceCharge} ${transaction.originalCurrency}',
        'VAT': '${transaction.vat} ${transaction.originalCurrency}',
        'Date': AppMapper.safeFormattedDate(transaction.createdAt),
        'BillRefNo': transaction.billRefNo,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        context: context,
        title: 'Accept Money Request',
      ),
      body: SafeArea(
        child: MultiBlocListener(
          listeners: [
            BlocListener<TransactionBloc, TransactionState>(
              listenWhen: (previous, current) =>
                  current is ConfirmTransferSuccess ||
                  current is ConfirmTransferError,
              listener: (context, state) {
                if (state is ConfirmTransferError) {
                  CustomToastification(context, message: state.message);
                }
              },
            ),
            BlocListener<WalletTransferBloc, WalletTransferState>(
              listenWhen: (previous, current) =>
                  current is CheckingWalletTransferRules ||
                  current is WalletTransferRulesChecked ||
                  current is WalletTransferError,
              listener: (context, state) {
                if (state is CheckingWalletTransferRules) {
                  setState(() => _isLoading = true);
                } else if (state is WalletTransferRulesChecked) {
                  setState(() => _isLoading = false);
                  // Create a WalletTransferResponse from the rules data
                  final rulesData = state.rulesResponse.data;
                  final transferResponse = WalletTransferResponse(
                    statusCode: state.rulesResponse.statusCode,
                    message: 'Transfer rules checked successfully',
                    data: WalletTransferData(
                      id: widget.moneyRequest.transactionId,
                      senderId: widget.moneyRequest.beneficiaryId,
                      senderName: widget.moneyRequest.senderName,
                      senderPhone: widget.moneyRequest.beneficiaryPhone,
                      senderEmail: widget.moneyRequest.senderEmail,
                      transactionOwner: widget.moneyRequest.senderName,
                      beneficiaryId: widget.moneyRequest.beneficiaryId,
                      beneficiaryName: widget.moneyRequest.beneficiaryName,
                      beneficiaryPhone: widget.moneyRequest.beneficiaryPhone,
                      beneficiaryEmail: widget.moneyRequest.beneficiaryEmail,
                      transactionType: 'money_request',
                      billAmount: rulesData.amount,
                      originalCurrency: widget.moneyRequest.currency,
                      serviceCharge: rulesData.serviceCharge,
                      vat: rulesData.vat,
                      totalAmount: rulesData.amount +
                          rulesData.serviceCharge +
                          rulesData.vat,
                      paidAmount: rulesData.amount +
                          rulesData.serviceCharge +
                          rulesData.vat,
                      billRefNo: widget.moneyRequest.billRefNo,
                      paidDate: DateTime.now(),
                      authorization_type: rulesData.authorizationType,
                      status: 'PENDING',
                      enabled: true,
                      isDeleted: false,
                      isExpired: false,
                      createdAt: DateTime.now(),
                      lastModified: DateTime.now(),
                    ),
                  );
                  _showConfirmScreenBottomSheet(transferResponse);
                } else if (state is WalletTransferError) {
                  setState(() => _isLoading = false);
                  CustomToastification(
                    context,
                    message: state.message,
                  );
                }
              },
            ),
            BlocListener<MoneyRequestDetailBloc, MoneyRequestDetailState>(
              listenWhen: (previous, current) =>
                  current is GetWalletDetailState ||
                  current is MoneyRequestDetailErrorState ||
                  current is MoneyRequestDetailActionState,
              listener: (context, state) {
                if (state is MoneyRequestDetailErrorState) {
                  setState(() => _isLoading = false);
                  CustomToastification(
                    context,
                    message: state.message,
                  );
                } else if (state is MoneyRequestDetailActionState) {
                  setState(() => _isLoading = false);
                  if (state.isSuccess) {
                    // Money request accepted successfully, show success
                    _showSuccessScreenBottomSheet(
                      ConfirmTransferResponse(
                        success: true,
                        statusCode: 200,
                        message: state.message,
                        transaction: ConfirmTransferDetail(
                          id: widget.moneyRequest.transactionId,
                          transactionType: tx_type.TransactionType.moneyRequest,
                          billAmount: widget.moneyRequest.billAmount,
                          totalAmount: widget.moneyRequest.totalAmount,
                          originalCurrency: widget.moneyRequest.currency,
                          serviceCharge: widget.moneyRequest.serviceFee,
                          vat: widget.moneyRequest.vat,
                          paidAmount: widget.moneyRequest.totalAmount,
                          billRefNo: widget.moneyRequest.billRefNo,
                          authorizationType: 'PIN',
                          status: 'Paid',
                          createdAt: widget.moneyRequest.createdAt,
                          lastModified: DateTime.now(),
                        ),
                      ),
                    );
                  } else {
                    CustomToastification(
                      context,
                      message: state.message,
                    );
                  }
                } else if (state is GetWalletDetailState) {
                  setState(() {
                    walletBalance = state
                            .getWalletByCurrency(widget.moneyRequest.currency)
                            ?.balance ??
                        0;
                    _currencyController = CurrencyInputController(
                      currencyType: widget.moneyRequest.currency == 'USD'
                          ? CurrencyType.usd
                          : CurrencyType.etb,
                      maxBalance: walletBalance,
                      defaultAmount: widget.moneyRequest.billAmount,
                    );
                  });
                }
              },
            ),
          ],
          child: BlocListener<WalletBalanceBloc, HomeState>(
            listener: (context, state) {
              if (state is WalletLoadedState) {
                setState(() {
                  walletBalance =
                      state.isUsdWallet ? state.usdBalance : state.etbBalance;
                  _currencyController = CurrencyInputController(
                    currencyType: widget.moneyRequest.currency == 'USD'
                        ? CurrencyType.usd
                        : CurrencyType.etb,
                    maxBalance: walletBalance,
                    defaultAmount: widget.moneyRequest.billAmount,
                  );
                });
              }
            },
            child: BlocProvider(
              create: (context) => GetIt.I<TransactionBloc>(),
              child: CurrencyInputWidget(
                controller: _currencyController,
                title: 'Accept Money Request',
                subtitle: 'Check the requested amount and proceed to '
                    'confirmation. You can adjust the amount if needed.',
                transactionType: 'money_request',
                balanceLabel: CurrencyFormatter.formatWalletBalance(
                  walletBalance,
                  widget.moneyRequest.currency,
                ),
                onContinue: _onContinuePressed,
                isLoading: _isLoading,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
