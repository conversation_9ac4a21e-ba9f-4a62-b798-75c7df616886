import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:flutter/material.dart';

class ConnectionAnimationDialog extends StatefulWidget {
  const ConnectionAnimationDialog({
    required this.senderInitial,
    required this.receiverInitial,
    super.key,
  });
  final String senderInitial;
  final String receiverInitial;

  @override
  State<ConnectionAnimationDialog> createState() =>
      ConnectionAnimationDialogState();
}

class ConnectionAnimationDialogState extends State<ConnectionAnimationDialog>
    with TickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<Offset> _leftSlide;
  late final Animation<Offset> _rightSlide;
  late final Animation<double> _fadeOut;
  late final Animation<double> _fadeInCombined;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );

    _leftSlide = Tween<Offset>(
      begin: const Offset(-1.5, 0),
      end: const Offset(-0.2, 0),
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0, 0.5, curve: Curves.easeOut),
      ),
    );

    _rightSlide = Tween<Offset>(
      begin: const Offset(1.5, 0),
      end: const Offset(0.2, 0),
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0, 0.5, curve: Curves.easeOut),
      ),
    );

    _fadeOut = Tween<double>(
      begin: 1,
      end: 0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 0.7, curve: Curves.easeIn),
      ),
    );

    _fadeInCombined = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.7, 1, curve: Curves.easeOut),
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _buildInitialAvatar(String initial, Color color) {
    return CircleAvatar(
      radius: 32,
      backgroundColor: color,
      child: Text(
        initial,
        style: const TextStyle(fontSize: 28, color: Colors.white),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      backgroundColor: Colors.white,
      child: CustomPagePadding(
        child: Padding(
          padding: EdgeInsets.all(10),
          child: Column(
                mainAxisSize: MainAxisSize.min,
          
            children: [
              SizedBox(
                height: 100,
                width: double.infinity,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    SlideTransition(
                      position: _leftSlide,
                      child: FadeTransition(
                        opacity: _fadeOut,
                        child:
                            _buildInitialAvatar(widget.senderInitial,LightModeTheme().themeData.primaryColor),
                      ),
                    ),
                    SlideTransition(
                      position: _rightSlide,
                      child: FadeTransition(
                        opacity: _fadeOut,
                        child: _buildInitialAvatar(
                            widget.receiverInitial, LightModeTheme().dollarThemeData.primaryColor),
                      ),
                    ),
                    FadeTransition(
                      opacity: _fadeInCombined,
                      child: CircleAvatar(
                        radius: 32,
                        backgroundColor: Theme.of(context).primaryColor,
                        child: Text(
                          '${widget.senderInitial}${widget.receiverInitial}',
                          style: const TextStyle(fontSize: 30, color: Colors.white),
                        ),
                      ),
                    ),
                    // Optional text
                  ],
                ),
              ),
              const CustomBuildText(
                text: 'Connection Request Sent',
                fontSize: 20,
                fontWeight: FontWeight.w700,
              ),
              const SizedBox(height: 8),
              CustomBuildText(
                text:
                    "Your connection request to this person has been sent. Once accepted, you'll be able to send money easily.",
                color: Colors.black.withOpacity(0.5),
                fontSize: 12,
                caseType: '',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 26),
            ],
          ),
        ),
      ),
    );
  }
}
