import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class ConnectionAnimationDialog extends StatefulWidget {
  const ConnectionAnimationDialog({
    required this.senderInitial,
    required this.receiverInitial,
    this.senderAvatar,
    this.receiverAvatar,
    super.key,
  });
  final String senderInitial;
  final String receiverInitial;
  final String? senderAvatar;
  final String? receiverAvatar;

  @override
  State<ConnectionAnimationDialog> createState() =>
      ConnectionAnimationDialogState();
}

class ConnectionAnimationDialogState extends State<ConnectionAnimationDialog>
    with TickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<Offset> _leftSlide;
  late final Animation<Offset> _rightSlide;
  late final Animation<double> _fadeOut;
  late final Animation<double> _fadeInCombined;
  late final Animation<double> _fadeInGif;

  VideoPlayerController? _videoController;
  bool _showGif = false;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );

    _leftSlide = Tween<Offset>(
      begin: const Offset(-1.5, 0),
      end: const Offset(-0.2, 0),
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0, 0.5, curve: Curves.easeOut),
      ),
    );

    _rightSlide = Tween<Offset>(
      begin: const Offset(1.5, 0),
      end: const Offset(0.2, 0),
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0, 0.5, curve: Curves.easeOut),
      ),
    );

    _fadeOut = Tween<double>(
      begin: 1,
      end: 0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 0.7, curve: Curves.easeIn),
      ),
    );

    _fadeInCombined = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.7, 1, curve: Curves.easeOut),
      ),
    );

    _fadeInGif = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.9, 1, curve: Curves.easeOut),
      ),
    );

    _controller.forward();

    // Initialize video controller
    _videoController = VideoPlayerController.asset(
      'assets/animations/Lottie.gif.mp4',
    )..initialize().then((_) {
        // After the animation completes, show the GIF
        _controller.addStatusListener((status) {
          if (status == AnimationStatus.completed) {
            setState(() {
              _showGif = true;
            });
            _videoController?.play();
          }
        });
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  Widget _buildInitialAvatar(String initial, Color color, String? avatarUrl) {
    return CircleAvatar(
      radius: 32,
      backgroundColor: color,
      backgroundImage: avatarUrl != null && avatarUrl.isNotEmpty
          ? CachedNetworkImageProvider(avatarUrl)
          : null,
      child: (avatarUrl == null || avatarUrl.isEmpty)
          ? Text(
              initial,
              style: const TextStyle(fontSize: 28, color: Colors.white),
            )
          : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      backgroundColor: Colors.white,
      child: CustomPagePadding(
        child: Padding(
          padding: EdgeInsets.all(10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 100,
                width: double.infinity,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Initial avatars sliding in
                    SlideTransition(
                      position: _leftSlide,
                      child: FadeTransition(
                        opacity: _fadeOut,
                        child: _buildInitialAvatar(
                          widget.senderInitial,
                          LightModeTheme().themeData.primaryColor,
                          widget.senderAvatar,
                        ),
                      ),
                    ),
                    SlideTransition(
                      position: _rightSlide,
                      child: FadeTransition(
                        opacity: _fadeOut,
                        child: _buildInitialAvatar(
                          widget.receiverInitial,
                          LightModeTheme().dollarThemeData.primaryColor,
                          widget.receiverAvatar,
                        ),
                      ),
                    ),

                    // Combined avatar
                    FadeTransition(
                      opacity: _fadeInCombined,
                      child: !_showGif
                          ? CircleAvatar(
                              radius: 32,
                              backgroundColor: Theme.of(context).primaryColor,
                              child: Text(
                                '${widget.senderInitial}${widget.receiverInitial}',
                                style: const TextStyle(
                                    fontSize: 30, color: Colors.white),
                              ),
                            )
                          : const SizedBox(),
                    ),

                    // GIF animation
                    if (_showGif &&
                        _videoController != null &&
                        _videoController!.value.isInitialized)
                      FadeTransition(
                        opacity: _fadeInGif,
                        child: SizedBox(
                          height: 80,
                          width: 80,
                          child: AspectRatio(
                            aspectRatio: _videoController!.value.aspectRatio,
                            child: VideoPlayer(_videoController!),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              const CustomBuildText(
                text: 'Connection Request Sent',
                fontSize: 20,
                fontWeight: FontWeight.w700,
              ),
              const SizedBox(height: 8),
              CustomBuildText(
                text:
                    "Your connection request to this person has been sent. Once accepted, you'll be able to send money easily.",
                color: Colors.black.withOpacity(0.5),
                fontSize: 12,
                caseType: '',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 26),
            ],
          ),
        ),
      ),
    );
  }
}
