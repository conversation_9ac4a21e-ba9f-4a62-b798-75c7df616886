import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/transactions/data/models/confirm_transfer_response_model.dart';
import 'package:cbrs/features/transactions/data/models/invoice-response_model.dart';
import 'package:cbrs/features/transactions/data/models/transaction_with_avatar.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:cbrs/features/transactions/domain/entities/transfer_limit.dart';
import 'package:dartz/dartz.dart';

abstract class TransactionRepository {
  Future<Either<Failure, List<Transaction>>> getTransactions({
    required int page,
    required int perPage,
    String? transactionType,
    String? startDate,
    String? endDate,
  });
  Future<Either<Failure, InvoiceResponseModel>> getInvoice(String billRefNo);

  Future<Either<Failure, Transaction>> getTransactionDetails(String id);
  ResultFuture<Transaction> validateTransactions(String id);

  Future<Map<String, String>> fetchUserAvatars(List<String> userIds);

  Future<Either<Failure, List<TransactionWithAvatar>>>
      getRecentWalletTransfers({
    required int limit,
    required String currentUserId,
  });

  Future<Either<Failure, TransferLimit>> getTransferLimit({
    required String transactionType,
    required String currency,
  });

  ResultFuture<String> getLoanInvoice({
    required String billRefNo,
    required LoanReceipt loanReceipt,
  });

  Future<Either<Failure, ConfirmTransferResponseModel>> confirmTransfer({
    required String pin,
    required String billRefNo,
    required String transactionType,
    String? otp,
  });

  Future<Either<Failure, bool>> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  });

  Future<Either<Failure, dynamic>> resendOtp({
    required String billRefNo,
    required String otpFor,
  });
}
