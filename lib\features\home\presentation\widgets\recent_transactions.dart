import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/build_view_all.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/home/<USER>/widgets/home_section_headers.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:cbrs/features/transactions/presentation/widgets/transaction_card.dart';
import 'package:cbrs/features/user/presentation/bloc/user_bloc.dart';
import 'package:cbrs/features/user/presentation/bloc/user_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';

class RecentTransactions extends StatefulWidget {
  const RecentTransactions({super.key});

  @override
  State<RecentTransactions> createState() => _RecentTransactionsState();
}

class _RecentTransactionsState extends State<RecentTransactions> {
  List<Transaction>? _cachedTransactions;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TransactionBloc, TransactionState>(
      listener: (context, state) {
        if (state is TransactionLoaded) {
          setState(() {
            _cachedTransactions = state.allTransactions;
          });
        }
      },
      builder: (context, state) {
        if (state is TransactionLoading && _cachedTransactions == null) {
          return _buildShimmerEffect();
        }

        final transactions = state is TransactionLoaded
            ? state.allTransactions
            : _cachedTransactions;

        if (transactions == null || transactions.isEmpty) {
          return const SizedBox.shrink();
        }

        return _buildContent(context, transactions);
      },
    );
  }

  Widget _buildShimmerEffect() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title shimmer
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: 150.w,
              height: 20.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
          SizedBox(height: 8.h),
          // Subtitle shimmer
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: 250.w,
              height: 16.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
          SizedBox(height: 24.h),
          // Transaction list container shimmer
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 24,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: List.generate(
                3,
                _buildTransactionShimmerItem,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionShimmerItem(int index) {
    return Column(
      children: [
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Row(
            children: [
              Container(
                width: 40.w,
                height: 40.w,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 120.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      width: 80.w,
                      height: 14.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 80.w,
                height: 16.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ],
          ),
        ),
        if (index < 2)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: CustomPaint(
              painter: DottedLinePainter(
                color: Colors.grey.withOpacity(0.3),
              ),
              size: Size(double.infinity, 1.h),
            ),
          ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, List<Transaction> transactions) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HomeSectionHeaders(
            title: 'Recent Transactions',
            description:
                'All your transaction records with detailed information.',
          ),
          SizedBox(height: 16.h),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              children: [
                _buildTransactionList(transactions),
                SizedBox(height: 4.h),
                BuildViewAll(
                  onTap: () {
                    GlobalVariable.currentTabIndex = 3;
                  },
                ),
                SizedBox(height: 26.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionList(List<Transaction> transactions) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        var currentUserId = '';
        if (userState is UserLoaded) {
          currentUserId = userState.user.id;
        }

        final takeRecentTransaction =
            transactions.length > 3 ? transactions.take(3) : transactions;

        return Column(
          children: takeRecentTransaction.map((transaction) {
            return Column(
              children: [
                TransactionCard(
                  title: transaction.transactionType.toLowerCase() ==
                          'wallet_transfer'
                      ? (transaction.senderId == currentUserId
                          ? transaction.beneficiaryName ?? 'Unknown'
                          : transaction.senderName)
                      : transaction.senderName,
                  transactionType: transaction.transactionType,
                  transaction: transaction,
                  onTap: () {
                    context.pushNamed(
                      AppRouteName.transactionDetails,
                      pathParameters: {'id': transaction.id},
                    );
                  },
                  amount:
                      '${transaction.originalCurrency} ${transaction.billAmount}',
                  date: transaction.createdAt.toString(),
                  currentUserId: currentUserId,
                  senderId: transaction.senderId,
                  beneficiaryId: transaction.beneficiaryId ?? '',
                ),
                if (transactions.indexOf(transaction) !=
                    transactions.length - 1)
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: CustomPaint(
                      painter: DottedLinePainter(
                        color: Colors.grey.withOpacity(0.3),
                      ),
                      size: Size(double.infinity, 1.h),
                    ),
                  ),
              ],
            );
          }).toList(),
        );
      },
    );
  }

  // Widget _buildViewAllButton(BuildContext context) {
  //   return BlocBuilder<HomeBloc, HomeState>(
  //     builder: (context, state) {
  //       final isUSD = state is HomeLoaded && state.data.selectedWallet == 'USD';
  //       final color =
  //           isUSD ? const Color(0xFF0D3C89) : Theme.of(context).primaryColor;

  //       return Material(
  //         child: InkWell(
  //           borderRadius: BorderRadius.circular(20.r),
  //           onTap: () {
  //             context.pushNamed(AppRouteName.home, extra: {'initialTab': 3});
  //           },
  //           child: Container(
  //             padding: EdgeInsets.symmetric(
  //               horizontal: 12.w,
  //             ),
  //             child: Row(
  //               mainAxisSize: MainAxisSize.min,
  //               children: [
  //                 Text(
  //                   'View All',
  //                   style: GoogleFonts.plusJakartaSans(
  //                     fontSize: 14.sp,
  //                     fontWeight: FontWeight.w600,
  //                     letterSpacing: 0.2,
  //                     color: color,
  //                   ),
  //                 ),
  //                 SizedBox(width: 4.w),
  //                 Icon(
  //                   Icons.arrow_forward_ios_rounded,
  //                   size: 12.h,
  //                   color: color,
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }
}
