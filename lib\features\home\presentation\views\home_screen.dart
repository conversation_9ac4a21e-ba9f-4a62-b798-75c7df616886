import 'dart:async';

import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/firebase_notification_service/fcm_service.dart';
import 'package:cbrs/core/services/firebase_notification_service/get_service_key.dart';
import 'package:cbrs/core/services/firebase_notification_service/notification_service.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/contact_service.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/exchange_rate/presentation/widgets/exchange_rate.dart';
import 'package:cbrs/features/gift_packages/domain/entities/banner_item.dart';
import 'package:cbrs/features/gift_packages/domain/repositories/gift_package_repository.dart';
import 'package:cbrs/features/gift_packages/presentation/widgets/gift_banners_carousal.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/home/<USER>/widgets/additional_services.dart';
import 'package:cbrs/features/home/<USER>/widgets/bank_accounts_section.dart';
import 'package:cbrs/features/home/<USER>/widgets/bank_services_section.dart';
import 'package:cbrs/features/home/<USER>/widgets/financial_services_section.dart';
import 'package:cbrs/features/home/<USER>/widgets/mini_apps_section.dart';
import 'package:cbrs/features/home/<USER>/widgets/payment_options.dart';
import 'package:cbrs/features/home/<USER>/widgets/quick_wallet/quick_wallet_transfer_card.dart';
import 'package:cbrs/features/home/<USER>/widgets/recent_transactions.dart';
import 'package:cbrs/features/home/<USER>/widgets/usd_payment_options.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/my_connect/presentation/views/my_connect_page.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_bloc.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_event.dart';
import 'package:cbrs/features/orders/presentation/views/orders_screen.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/recent_wallet_transfer_hive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:leak_tracker/leak_tracker.dart';
import 'package:cbrs/features/home/<USER>/widgets/identity_verification_card.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/services/connectivity/connectivity_service.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/usecases/get_paginated_loan_items.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_bloc.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_event.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_state.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_categories.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_item_card.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_item_shimmer.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/my_connect_home_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  List<BannerItem> _bannerItems = [];
  NotificationService notificationService = NotificationService();
  String selectedWallet = 'USD';
  bool isAmountHidden = false;

  late ScrollController _scrollController;
  bool _hasLoadedTransactions = false;
  bool _shouldRebuildTransactions = true;

  bool isLevelOne = true;

  List<RecentWalletTransferHive>? recentWalletTransactions;

  @override
  void initState() {
    super.initState();

    _scrollController = ScrollController()..addListener(_onScroll);
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _initializeData();
  }

  Future<void> _initializeData() async {
    if (GlobalVariable.isAppOpenFromNotification) {
      handleNotificationRedirect(context);
    }

    context.read<WalletBalanceBloc>().add(
          FetchWalletEvent(
            isUsdWallet: GlobalVariable.currentlySelectedWallet == 'USD',
          ),
        );

    await _initializeEssentialData();
    context.read<RecentWalletTransferBloc>().add(
          const GetRecentWalletTransferEvent(limit: 10),
        );
    await _fetchExchangeRate();

    if (mounted) {
      setState(() {});
      await Future.microtask(() {
        context.read<TransactionBloc>().add(
              const FetchTransactionsEvent(page: 1, perPage: 5),
            );

        // Recent wallet transfer to check
        context.read<TransactionBloc>().add(
              const FetchRecentWalletTransfersEvent(limit: 20),
            );

        _fetchBanners();
      });

      await _initializeSecondaryData();
    }
  }

  Future<void> _initializeEssentialData() async {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..forward();

    context.read<HomeBloc>().add(const HomeProfileFetchingEvent());

    context.read<HomeBloc>().add(
          LoadEssentialDataEvent(
            walletType: GlobalVariable.currentlySelectedWallet ?? 'USD',
          ),
        );
  }

  Future<void> _initializeSecondaryData() async {
    try {
      await notificationService.requestNotificationPermission();

      await Future.wait([
        GetServerKey.getServerKey(),
        GetServerKey.getServerKey(),
        notificationService.initLocalNotifications(),
        notificationService.setUpFirebaseMessaging(context),
      ]);

      await sl<FcmService>().storeFcmToken();

      await Future.delayed(const Duration(seconds: 4), () async {
        await Get.find<ContactSyncService>().syncContacts();
      });
    } catch (e) {
      debugPrint('Error initializing secondary data: $e');
    }
  }

  void _handleWalletSelection(
    BuildContext context,
    String wallet,
    HomeState state,
  ) {
    debugPrint('HomeScreen - Wallet Selection Requested: $wallet');
    debugPrint('HomeScreen - Current State: ${state.runtimeType}');

    if (state is HomeLoaded) {
      context.read<HomeBloc>().add(UpdateWalletSelection(wallet));
    }
  }

  void _handleToggleVisibility(BuildContext context, HomeState state) {
    if (state is HomeLoaded) {
      context.read<HomeBloc>().add(ToggleAmountVisibility());
    }
  }

  void _onScroll() {
    final position = _scrollController.position;

    if (!_hasLoadedTransactions &&
        position.pixels > 300 &&
        _shouldRebuildTransactions) {
      setState(() {
        _hasLoadedTransactions = true;
        _shouldRebuildTransactions = false;
      });
      context.read<TransactionBloc>().add(
            const FetchTransactionsEvent(page: 1, perPage: 5),
          );
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  bool isUSD = true;
  // final isUSD = state is HomeLoaded && state.data.selectedWallet == 'USD';

  @override
  Widget build(BuildContext context) {
    // getLocalData();
    return BlocConsumer<WalletBalanceBloc, HomeState>(
      listener: (context, state) {
        if (state is WalletLoadedState) {
          setState(() {
            isUSD = state.isUsdWallet;
          });
        }
      },
      builder: (context, state) {
        return BlocConsumer<HomeBloc, HomeState>(
          listener: (context, state) {
            // Theme toggling is now handled exclusively in the HomeBloc
          },
          builder: (context, state) {
            return Scaffold(
              backgroundColor: const Color(0xFFF9F9F9),
              extendBodyBehindAppBar: true,
              body: RefreshIndicator(
                color: Theme.of(context).primaryColor,
                onRefresh: _handleRefresh,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return CustomScrollView(
                      controller: _scrollController,
                      physics: const ClampingScrollPhysics(),
                      slivers: [
                        //
                        SliverToBoxAdapter(
                          child: BankAccountsSection(
                            key: const ValueKey('bank_accounts'),
                            userName: state is HomeLoaded
                                ? '${state.data.profile.firstName} ${state.data.profile.lastName}'
                                : 'Loading...',
                            avatarUrl: state is HomeLoaded
                                ? state.data.profile.avatar
                                : null,

                            onNotificationTap: () =>
                                context.goNamed(AppRouteName.notifications),
                            onOrdersTap: () => _handleOrdersTap(context),
                            onProfileTap: () => _navigateToProfile(context),
                            // selectedWallet: _getSelectedWallet(state),
                            // onWalletSelected: (wallet) {
                            //   //UpdateWalletSelectionIsLoading
                            //     _handleWalletSelection(context, wallet, state);},

                            onRefreshBalances: () =>
                                _handleToggleVisibility(context, state),
                          ),
                        ),
                        _buildContentSections(),
                      ],
                    );
                  },
                ),
              ),
              floatingActionButton: Material(
                color: Colors.transparent,
                shape: const CircleBorder(),
                clipBehavior: Clip.hardEdge,
                child: InkWell(
                  splashColor: isUSD
                      ? const Color(0xFF1A6B2F).withOpacity(0.5)
                      : const Color(0xFF69AC5C).withOpacity(0.5),
                  highlightColor: Colors.transparent,
                  onTap: () => context.pushNamed(
                    AppRouteName.qrOptions,
                    extra: {'isUSD': isUSD},
                  ),
                  child: Ink(
                    width: 56.w,
                    height: 56.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: isUSD
                            ? [
                                const Color(0xFF0D451B),
                                const Color(0xFF1A6B2F),
                              ]
                            : [
                                const Color(0xFF69AC5C),
                                const Color(0xFF085905),
                              ],
                      ),
                    ),
                    child: Center(
                      child: Image.asset(
                        MediaRes.qrIcon,
                        width: 27.w,
                        height: 27.h,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.endFloat,
            );
          },
        );
      },
    );
  }

  double _getWalletBalance(HomeState state) {
    if (state is! HomeLoaded) return 0;
    final balance = state.data.selectedWallet == 'USD'
        ? state.data.usdBalance
        : state.data.etbBalance;
    debugPrint(
      'HomeScreen - Getting wallet balance: $balance for wallet type: ${state.data.selectedWallet}',
    );
    return balance;
  }

  bool _isAmountHidden(HomeState state) {
    return state is HomeLoaded ? state.data.isAmountHidden : true;
  }

  Widget _buildContentSections() {
    return SliverList(
      delegate: SliverChildListDelegate([
        SizedBox(
          height: 16.h,
        ),
        BlocBuilder<WalletBalanceBloc, HomeState>(
          builder: (context, state) {
            // final isUSD = state is WalletLoadedState && state.isUsdWallet;

            if (isUSD) {
              return Column(
                children: [
                  const USDPaymentOptions(),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                    child: MyConnectHomeCard(
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => MyConnectPage(),
                          ),
                        );
                      },
                    ),
                  ),
                  const QuickWalletTransferCard(
                    assetImage: MediaRes.birrQuickPayIcon,
                  ),
                  // Identity Verification Card
                  if (_bannerItems.isNotEmpty) ...[
                    SizedBox(height: 26.h),
                    GiftBannersCarousal(
                      imageUrls: _bannerItems,
                      isAutoPlay: true,
                      bannerHeight: 100.h,
                      dotsPosition: 2.h,
                      hasDots: true,
                      dotsBackgroundColor: Colors.transparent,
                      activeDotColor: Theme.of(context).primaryColor,
                      inActiveDotColor: Theme.of(context).secondaryHeaderColor,
                    ),
                  ],
                  SizedBox(height: 24.h),
                  const FinancialServicesSection(),
                  SizedBox(height: 26.h),
                  const RecentTransactions(),
                  SizedBox(height: 26.h),

                  if (isLevelOne)
                    BlocBuilder<HomeBloc, HomeState>(
                      builder: (context, state) {
                        if (state is HomeProfileLoadedState) {
                          final level =
                              state.localUser.memberLevel.level.toLowerCase() ??
                                  '';
                          final status = state.localUser.memberLevel.levelStatus
                                  .toLowerCase() ??
                              '';

                          if (level == 'level_one' &&
                              (status == 'pending' || status == 'rejected'))
                            return const IdentityVerificationCard();
                        }

                        return const SizedBox.shrink();
                      },
                    ),
                  SizedBox(height: 26.h),
                  const ExchangeRateWidget(),
                  SizedBox(height: 26.h),
                ],
              );
            }
            return Column(
              children: [
                PaymentOptions(isUSD: isUSD),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: MyConnectHomeCard(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => MyConnectPage(),
                        ),
                      );
                    },
                  ),
                ),
                const QuickWalletTransferCard(
                  assetImage: MediaRes.birrQuickPayIcon,
                ),
                if (_bannerItems.isNotEmpty) ...[
                  SizedBox(height: 26.h),
                  GiftBannersCarousal(
                    imageUrls: _bannerItems,
                    isAutoPlay: true,
                    bannerHeight: 100.h,
                    dotsPosition: 2.h,
                    hasDots: true,
                    dotsBackgroundColor: Colors.transparent,
                    activeDotColor: const Color(0xFF065234),
                    inActiveDotColor: const Color(0xFFC4DDD3),
                  ),
                ],
                SizedBox(height: 16.h),
                const BankServicesSection(),
                SizedBox(height: 26.h),
                const MiniAppsSection(),
                SizedBox(height: 26.h),
                const AdditionalServices(),
                SizedBox(height: 26.h),
                const RecentTransactions(),
                if (isLevelOne) const IdentityVerificationCard(),
                SizedBox(height: 26.h),
              ],
            );
          },
        ),
      ]),
    );
  }

  Future<void> _handleOrdersTap(BuildContext context) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const OrdersScreen(),
      ),
    );
  }

  Future<void> _fetchBanners() async {
    try {
      final response = await context
          .read<GiftPackageRepository>()
          .getBanners(status: 'ongoing');
      setState(() {
        _bannerItems = response.data;
      });
      // if (mounted) {
      //   setState(() {
      //     _bannerItems = response.data;
      //   });
      // }
    } catch (e) {
      if (mounted) {
        setState(() {
          _bannerItems = [];
        });
      }
    }
  }

  Future<void> _fetchExchangeRate() async {
    try {
      context.read<HomeBloc>().add(FetchExchangeRate());

      await Future.delayed(const Duration(milliseconds: 300));

      if (mounted) {
        final currentState = context.read<HomeBloc>().state;
        if (currentState is HomeLoaded) {
          setState(() {});
        }
      }
    } catch (e) {
      debugPrint('Error fetching exchange rate: $e');
    }
  }

  Future<void> _handleRefresh() async {
    final futures = <Future>[
      Future.microtask(() {
        context.read<WalletBalanceBloc>().add(
              FetchWalletEvent(
                isUsdWallet: GlobalVariable.currentlySelectedWallet == 'USD',
              ),
            );
      }),
      Future.microtask(() {
        context.read<TransactionBloc>().add(
              const FetchTransactionsEvent(page: 1, perPage: 5),
            );
      }),
      Future.microtask(() {
        context.read<TransactionBloc>().add(
              const FetchRecentWalletTransfersEvent(),
            );
      }),
      Future.microtask(() {
        context.read<HomeBloc>().add(RefreshHomeData());
      }),
      Future.microtask(() {
        context.read<NotificationBloc>().add(FetchUnseenCount());
      }),
    ];

    await Future.wait(futures);
  }

  void _navigateToProfile(BuildContext context) {
    GlobalVariable.currentTabIndex = 4;
  }
}
