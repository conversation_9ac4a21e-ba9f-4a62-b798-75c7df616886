import 'dart:convert';

import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/core/utils/app_mapper.dart';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'package:equatable/equatable.dart';
import 'package:fast_contacts/fast_contacts.dart'; 
/// I use this class coz
/// 1. member lookup to change to dart
/// 2. I want to be more freedom in changing or whatever since i use
/// so that i wont break the existing code
/// 3. i want to apply some customization or whatever

class SyncedContactData extends Equatable {
  final String id;
  final String memberCode;
  final String firstName;
  final String middleName;
  final String lastName;
  final String address;
  final String city;
  final String country;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final String phoneNumber;
  final String email;
  final bool enabled;
  final bool isVerified;
  final String avatar;

  const SyncedContactData({
    required this.id,
    required this.memberCode,
    required this.firstName,
    required this.middleName,
    required this.lastName,
    required this.address,
    required this.city,
    required this.country,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.phoneNumber,
    required this.email,
    required this.enabled,
    required this.isVerified,
    required this.avatar,
  });

  factory SyncedContactData.fromJson(Map<String, dynamic> json) {
    return SyncedContactData(
      id: AppMapper.safeString(json['_id']),
      memberCode: AppMapper.safeString(json['memberCode']),
      firstName: AppMapper.safeName(json[
          'firstName']), // sometimes name format is like abe asella so I use safeName to change names to Abe Asella [literally it wchange the first letter of the name]
      middleName: AppMapper.safeName(json['middleName']),
      lastName: AppMapper.safeName(json['lastName']),
      address: AppMapper.safeString(json['address']),
      city: AppMapper.safeString(json['city']),
      country: AppMapper.safeString(json['country']),
      isEmailVerified: AppMapper.safeBool(json['isEmailVerified']),
      isPhoneVerified: AppMapper.safeBool(json['isPhoneVerified']),
      phoneNumber: AppMapper.safeString(json['phoneNumber']),
      email: AppMapper.safeString(json['email']),
      enabled: AppMapper.safeBool(json['enabled']),
      isVerified: AppMapper.safeBool(json['isVerified']),
      avatar: AppMapper.safeString(json['avatar']),
    );
  }

  Map<String, dynamic> toJson() => {
        '_id': id,
        'memberCode': memberCode,
        'firstName': firstName,
        'middleName': middleName,
        'lastName': lastName,
        'address': address,
        'city': city,
        'country': country,
        'isEmailVerified': isEmailVerified,
        'isPhoneVerified': isPhoneVerified,
        'phoneNumber': phoneNumber,
        'email': email,
        'enabled': enabled,
        'isVerified': isVerified,
      };

  @override
  List<Object?> get props => [
        id,
        memberCode,
        firstName,
        middleName,
        lastName,
        address,
        city,
        country,
        isEmailVerified,
        isPhoneVerified,
        phoneNumber,
        email,
        enabled,
        isVerified,
      ];
}

class ContactSyncService extends GetxService {
  final Dio _dio; // for api request
  final Box _contactsBox; // for saving to the box

  ContactSyncService({
    required Dio dio,
    required Box contactsBox,
  })  : _dio = dio,
        _contactsBox = contactsBox;

  var isSyncing = false.obs;

  var _isPhoneSynced = false
      .obs; // check if the phone synced coz sometime beacuase of network failure happen it allows sync when we visit again

  bool get isSynced => _isPhoneSynced.value;
  final members = <SyncedContactData>[].obs; // for storing stored memebers
  final searchContacts = <SyncedContactData>[].obs; // for searching

  @override
  void onInit() {
    super.onInit();
    // _loadMembersFromStorage(); // Load on startup
  }

// this method filter synced contacts. that means after every query it fetches new contacts based on user input(First name, last name or middle name. i use member.valu so that i wont miss out when user clear his query)
  void filterSearch(String query) {
    debugPrint('query: $query');
    if (query.isEmpty) {
      resetSearch();
    } else {
      final normalizedQuery = query
          .trim()
          .toLowerCase(); // if user sent empty query it will return all contacts
      var filtered = members.where((member) {
        final firstName = member.firstName?.trim().toLowerCase() ?? '';
        final lastName = member.lastName?.trim().toLowerCase() ?? '';
        final middleName = member.middleName?.trim().toLowerCase() ?? '';

        return firstName.contains(normalizedQuery) ||
            lastName.contains(normalizedQuery) ||
            middleName.contains(normalizedQuery);
      }).toList();

      filtered.sort((a, b) => a.firstName.compareTo(b.firstName));
      searchContacts.value = filtered;
    }
  }

// check user with phone
  void filterWithPhone(String query) {
    debugPrint('query: $query');
    if (query.isEmpty) {
      resetSearch();
    } else {
      final normalizedQuery = query
          .trim()
          .toLowerCase(); // if user sent empty query it will return all contacts
      var filtered = members.where((member) {
        final phoneNumber = member.phoneNumber?.trim() ?? '';

        return phoneNumber.contains(normalizedQuery);
      }).toList();

      filtered.sort((a, b) => a.firstName.compareTo(b.firstName));
      searchContacts.value = filtered;
    }
  }
  void filterWithEmail(String query) {
    debugPrint('query: $query');
    if (query.isEmpty) {
      resetSearch();
    } else {
      final normalizedQuery = query
          .trim()
          .toLowerCase(); // if user sent empty query it will return all contacts
      var filtered = members.where((member) {
        final email = member.email?.trim() ?? '';

        return email.contains(normalizedQuery);
      }).toList();

      filtered.sort((a, b) => a.firstName.compareTo(b.firstName));
      searchContacts.value = filtered;
    }
  }

  /// it reset search
  /// 1. when user clears the query
  /// 2. when user clicks on close button
  /// 3. when user press on listed contact
  /// unless it persist the search
  void resetSearch() {
    members.sort((a, b) => a.firstName.compareTo(b.firstName));
    searchContacts.value = members;
  }

  /// this method sync contact from the backednd
  Future<void> syncContacts() async {
    debugPrint("🇧🇲lsync from storage");

    if (isSyncing.value || _isPhoneSynced.value) return;

    isSyncing.value = true;
    try {
      // if permission is given. it work unleess it ask.
      /// I recommed whenever we use this sync service to check permission. coz we can create our own bottom sheet to ask the user by providing necessary permission why we need such permissions.
      /// so before we use this sync we have to check if permsision is granted or not.
      /// i will create custom bottom sheet for this service in common/widgets instead of default one.

      if (await FlutterContacts.requestPermission()) {
        final deviceContacts = await FastContacts.getAllContacts();
            
            //await FlutterContacts.getContacts(withProperties: true);

        final contactsData = deviceContacts.expand((contact) {
          return contact.phones.map((phone) {
            var cleanedPhone = '';
            if (phone.number.isNotEmpty) {
              if (phone.number.startsWith('09'))
                cleanedPhone = '+251${phone.number.substring(1)}';
              else
                cleanedPhone = phone.number;
            }

            return cleanedPhone.replaceAll(' ', '');
          });
        }).toList();




        final response = await _dio.post<Map<String, dynamic>>(
          ApiEndpoints.contactLookup,
          data: {
            "phoneNumber": contactsData,
          },
        );

        if (response.data != null && response.data!['data'] != null) {
          _isPhoneSynced.value = true;
          final membersJson = response.data!['data'] as List<dynamic>;

          // Convert JSON to SyncedContactData objects
          // we have to conver json in the format we use it next time.
          final newMembers = membersJson
              .map((json) =>
                  SyncedContactData.fromJson(json as Map<String, dynamic>))
              .toList();

          _contactsBox.put('members', membersJson);
          newMembers.sort((a, b) => a.firstName.compareTo(b.firstName));
          members.value = newMembers;
          searchContacts.value = newMembers;
          isSyncing.value = false;
          print('Synced and stored ${membersJson.length} members.');
        } else {
          print('No members returned from backend.');
        }
      } else {
        print('Contacts permission denied.');
      }
    } catch (e) {
      print('Error during contact sync: $e');
    } finally {
      isSyncing.value = false;
    }
  }

  void _loadMembersFromStorage() {
    debugPrint(
        "🇧🇲load members from storage is syncncing value  ${isSyncing.value}  members lenegth ${members.length}");
    if (members.length != 0) {
      final membersJson = _contactsBox.get('members', defaultValue: []);
      if (membersJson is List && membersJson.isNotEmpty) {
        members.value = membersJson.map((json) {
          // Ensure each map is correctly typed as Map<String, dynamic>
          final Map<String, dynamic> typedJson =
              Map<String, dynamic>.from(json as Map<dynamic, dynamic>);
          return SyncedContactData.fromJson(typedJson);
        }).toList();

        searchContacts.value = members.value;
      } else {
        syncContacts();
      }
    } else {
      syncContacts();
    }
  }
}
