import 'package:cbrs/features/mini_apps/domain/entities/payment_request.dart';

class MiniappPaymentModel extends MiniappPaymentRequest {
  const MiniappPaymentModel({
    required String stage,
    required String timestamp,
    required String nonceStr,
    String? method,
    required BizContent bizContent,
    required String sign,
    required String confirmPayload,
    String? dataToken,
    String? deviceUuid,
    String? sourceApp,
    String? accessToken,
    String? apiKey,
  }) : super(
    stage: stage,
    timestamp: timestamp,
    nonceStr: nonceStr,
    method: method,
    bizContent: bizContent,
    sign: sign,
    confirmPayload: confirmPayload,
    dataToken: dataToken,
    deviceUuid: deviceUuid,
    sourceApp: sourceApp,
    accessToken: accessToken,
    apiKey: apiKey,
  );

  factory MiniappPaymentModel.fromJson(Map<String, dynamic> json) {
    return MiniappPaymentModel(
      stage: json['stage'] as String,
      timestamp: json['timestamp'] as String,
      nonceStr: json['nonce_str'] as String,
      method: json['method'] as String?,
      bizContent:
      BizContentModel.fromJson(json['biz_content'] as Map<String, dynamic>),
      sign: json['sign'] as String,
      confirmPayload: json['confirmpayload'] as String,
      dataToken: json['datatoken'] as String?,
      deviceUuid: json['deviceuuid'] as String?,
      sourceApp: json['sourceapp'] as String?,
      accessToken: json['a-access-token'] as String?,
      apiKey: json['x-api-key'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'stage': stage,
      'timestamp': timestamp,
      'nonce_str': nonceStr,
      'method': method,
      'biz_content': (bizContent as BizContentModel).toJson(),
      'sign': sign,
      'confirmpayload': confirmPayload,
    };
  }
}

class BizContentModel extends BizContent {
  const BizContentModel({
    String? notifyUrl,
    String? tradeType,
    String? appId,
    dynamic merchCode,
    dynamic merchOrderId,
    String? title,
    required double totalAmount,
    String? transCurrency,
    String? timeoutExpress,
    dynamic payeeIdentifier,
    String? payeeIdentifierType,
    String? payeeType,
    String? utilityProvider,
    required MiniappData utilityData,
    String? additionalData,
  }) : super(
    notifyUrl: notifyUrl,
    tradeType: tradeType,
    appId: appId,
    merchCode: merchCode,
    merchOrderId: merchOrderId,
    title: title,
    totalAmount: totalAmount,
    transCurrency: transCurrency,
    timeoutExpress: timeoutExpress,
    payeeIdentifier: payeeIdentifier,
    payeeIdentifierType: payeeIdentifierType,
    payeeType: payeeType,
    utilityProvider: utilityProvider,
    utilityData: utilityData,
    additionalData: additionalData,
  );

  factory BizContentModel.fromJson(Map<String, dynamic> json) {
    return BizContentModel(
      notifyUrl: json['notify_url'] as String?,
      tradeType: json['trade_type'] as String?,
      appId: json['appid'] as String?,
      merchCode: json['merch_code'],
      merchOrderId: json['merch_order_id'],
      title: json['title'] as String?,
      totalAmount: (json['total_amount'] as num).toDouble(),
      transCurrency: json['trans_currency'] as String?,
      timeoutExpress: json['timeout_express'] as String?,
      payeeIdentifier: json['payee_identifier'],
      payeeIdentifierType: json['payee_identifier_type'] as String?,
      payeeType: json['payee_type'] as String?,
      utilityProvider: json['utility_provider'] as String?,
      utilityData: MiniappDataModel.fromJson(
          json['utility_data'] as Map<String, dynamic>),
      additionalData: json['additional_data'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notify_url': notifyUrl,
      'trade_type': tradeType,
      'appid': appId,
      'merch_code': merchCode,
      'merch_order_id': merchOrderId,
      'title': title,
      'total_amount': totalAmount,
      'trans_currency': transCurrency,
      'timeout_express': timeoutExpress,
      'payee_identifier': payeeIdentifier,
      'payee_identifier_type': payeeIdentifierType,
      'payee_type': payeeType,
      'utility_provider': utilityProvider,
      'utility_data': (utilityData as MiniappDataModel).toJson(),
      'additional_data': additionalData,
    };
  }
}

class MiniappDataModel extends MiniappData {
  const MiniappDataModel({
    String? id,
    String? payerFullName,
    String? reference,
    String? serviceType,
    String? cardNumber,
    int? packageTime,
    String? productCode,
    String? customerNumber,
    String? orderId,
    String? narrative,
  }) : super(
    id: id,
    payerFullName: payerFullName,
    reference: reference,
    serviceType: serviceType,
    cardNumber: cardNumber,
    packageTime: packageTime,
    productCode: productCode,
    customerNumber: customerNumber,
    orderId: orderId,
    narrative: narrative,
  );

  factory MiniappDataModel.fromJson(Map<String, dynamic> json) {
    return MiniappDataModel(
      id: json['id'] as String?,
      payerFullName: json['payer_full_name'] as String?,
      reference: json['reference'] as String?,
      serviceType: json['serviceType'] as String?,
      cardNumber: json['card_number'] as String?,
      packageTime: json['package_time'] as int?,
      productCode: json['product_code'] as String?,
      customerNumber: json['customer_number'] as String?,
      orderId: json['orderId'] as String?,
      narrative: json['narrative'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'payer_full_name': payerFullName,
      'reference': reference,
      'serviceType': serviceType,
      'card_number': cardNumber,
      'package_time': packageTime,
      'product_code': productCode,
      'customer_number': customerNumber,
      'orderId': orderId,
      'narrative': narrative,
    };
  }
}
