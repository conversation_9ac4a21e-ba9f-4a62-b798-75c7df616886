import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';

class TransactionModel extends Transaction {
  const TransactionModel({
    required super.id,
    required super.senderId,
    required super.senderName,
    required super.transactionOwner,
    required super.transactionType,
    required super.billAmount,
    required super.paidAmount,
    required super.billRefNo,
    required super.status,
    required super.createdAt,
    required super.lastModified,
    required super.serviceCharge,
    required super.vat,
    required super.totalAmount,
    super.senderPhone,
    super.senderEmail,
    super.beneficiaryId,
    super.beneficiaryName,
    super.beneficiaryEmail,
    super.beneficiaryAccountNo,
    super.beneficiaryPhone,
    super.bankName,
    super.bankCode,
    super.bankLogo,
    super.orderId,
    super.giftPackageId,
    super.totalGiftPackageQty,
    super.originalCurrency,
    super.changedCurrency,
    super.exchangeRate,
    super.billReason,
    super.paidDate,
    super.walletFTNumber,
    super.beneficiaryConnectCode,
    super.cardNumber,
    super.connectRefNo,
    super.mpgsReference,
    super.FTNumber,
    super.senderConnectCode,
    
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>? ?? json;
    return TransactionModel(
      id: data['id']?.toString() ?? '',
      senderId: data['senderId'] as String? ?? '',
      senderName: data['senderName'] as String? ?? '',
      senderPhone: data['senderPhone'] as String?,
      senderEmail: data['senderEmail'] as String?,
      transactionOwner: data['transactionOwner'] as String? ?? '',
      beneficiaryId: data['beneficiaryId'] as String?,
      beneficiaryName: data['beneficiaryName'] as String?,
      beneficiaryEmail: data['beneficiaryEmail'] as String?,
      beneficiaryAccountNo: data['beneficiaryAccountNo'] as String?,
      beneficiaryPhone: data['beneficiaryPhone'] as String?,
      bankName: data['bankName'] as String?,
      bankCode: data['bankCode'] as String?,
      bankLogo: data['bankLogo'] as String?,
      transactionType: data['transactionType'] as String? ?? '',
      orderId: data['orderId'] as String?,
      giftPackageId: data['giftPackageId'] as String?,
      totalGiftPackageQty: data['totalGiftPackageQty'] as int?,
      billAmount: (data['billAmount'] as num?)?.toDouble() ?? 0.0,
      originalCurrency: data['originalCurrency'] as String?,
      changedCurrency: data['changedCurrency'] as String?,
      exchangeRate: (data['exchangeRate'] as num?)?.toDouble(),
      paidAmount: (data['paidAmount'] as num?)?.toDouble() ?? 0.0,
      billRefNo: data['billRefNo'] as String? ?? '',
      billReason: data['billReason'] as String?,
      paidDate: data['paidDate'] as String?,
      walletFTNumber: data['walletFTNumber'] as String?,
      status: data['status'] as String? ?? '',
      createdAt: DateTime.parse(
        data['createdAt'] as String? ?? DateTime.now().toIso8601String(),
      ),
      lastModified: DateTime.parse(
        data['lastModified'] as String? ?? DateTime.now().toIso8601String(),
      ),
      totalAmount: AppMapper.safeDouble(data['totalAmount']),
      vat: AppMapper.safeDouble(data['VAT']),
      serviceCharge: AppMapper.safeDouble(data['serviceCharge']),
      cardNumber: AppMapper.safeString(data['cardNumber']),
      beneficiaryConnectCode:
          AppMapper.safeString(data['beneficiaryConnectCode']),
      connectRefNo: AppMapper.safeString(data['connectRefNo']),
      mpgsReference: AppMapper.safeString(data['mpgsReference']),
      FTNumber: AppMapper.safeString(data['FTNumber']),
      senderConnectCode: AppMapper.safeString(data['senderConnectCode']),

/*
this.connectRefNo,
    this.beneficiaryConnectCode,
    this.mpgsRefNo,*/
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderPhone': senderPhone,
      'senderEmail': senderEmail,
      'transactionOwner': transactionOwner,
      'beneficiaryId': beneficiaryId,
      'beneficiaryName': beneficiaryName,
      'beneficiaryEmail': beneficiaryEmail,
      'beneficiaryAccountNo': beneficiaryAccountNo,
      'beneficiaryPhone': beneficiaryPhone,
      'bankName': bankName,
      'bankCode': bankCode,
      'transactionType': transactionType,
      'orderId': orderId,
      'giftPackageId': giftPackageId,
      'totalGiftPackageQty': totalGiftPackageQty,
      'billAmount': billAmount,
      'originalCurrency': originalCurrency,
      'changedCurrency': changedCurrency,
      'exchangeRate': exchangeRate,
      'paidAmount': paidAmount,
      'billRefNo': billRefNo,
      'billReason': billReason,
      'paidDate': paidDate,
      'walletFTNumber': walletFTNumber,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
    };
  }
}
