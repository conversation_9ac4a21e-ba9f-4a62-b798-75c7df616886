import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomTextInput extends StatefulWidget {
  const CustomTextInput({
    required this.hintText,
    required this.inputLabel,
    super.key,
    this.controller,
    this.isPassword = false,
    this.keyboardType = TextInputType.text,
    this.prefixIcon,
    this.onChanged,
    this.labelSize,
    this.inputFormatters,
    this.validator,
    this.autovalidateMode
  });
  final String? Function(String?)? validator;

  final TextEditingController? controller;
  final String hintText;
  final String inputLabel;
  final bool isPassword;
  final TextInputType keyboardType;
  final Widget? prefixIcon;
  final double? labelSize;
  final void Function(String)? onChanged;
  final AutovalidateMode? autovalidateMode;
  final List<TextInputFormatter>? inputFormatters;

  @override
  _CustomTextInputState createState() => _CustomTextInputState();
}

class _CustomTextInputState extends State<CustomTextInput> {
  bool _isPasswordVisible = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.inputLabel.isNotEmpty) ...[
          Text(
            widget.inputLabel.substring(0, 1).toUpperCase() +
                widget.inputLabel.substring(1),
            style: GoogleFonts.outfit(
              color: const Color(0xFFAAAAAA),
              fontSize: widget.labelSize?.sp ?? 16.sp,
              fontWeight: FontWeight.normal,
            ),
          ),
          SizedBox(height: 8.h),
        ],
        TextFormField(
          controller: widget.controller,
          obscureText: widget.isPassword && !_isPasswordVisible,
          keyboardType: widget.keyboardType,
          onTapOutside: (value) {
            FocusScope.of(context).unfocus();
          },
          autovalidateMode: widget.autovalidateMode,
          onChanged: widget.onChanged,
          inputFormatters: widget.inputFormatters,
          obscuringCharacter: '*',
          validator: widget.validator,
          decoration: InputDecoration(
            hintText: widget.hintText,
            hintStyle: GoogleFonts.outfit(
              color: const Color(0xFF7c7c7c),
              fontSize: 14.sp,
              fontWeight: FontWeight.normal,
            ),
            prefixIcon: widget.prefixIcon,
            suffixIcon: widget.isPassword
                ? IconButton(
                    icon: Icon(
                      _isPasswordVisible
                          ? Icons.visibility_off_outlined
                          : Icons.visibility_outlined,
                      color: Colors.grey.shade500,
                    ),
                    onPressed: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                  )
                : null,
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: Colors.transparent,
                width: 0,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: Colors.transparent,
                width: 0,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: Colors.transparent,
                width: 0,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: Colors.transparent,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: const Color(0xFFF8F8F8),
            contentPadding:
                EdgeInsets.symmetric(vertical: 8.h, horizontal: 20.w),
          ),
        ),
      ],
    );
  }
}
