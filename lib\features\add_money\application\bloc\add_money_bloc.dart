import 'package:bloc/bloc.dart';
import 'package:cbrs/features/add_money/data/models/add_money_response_model.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/add_money/domain/usecases/add_money_usecase.dart';
import 'package:cbrs/features/add_money/domain/usecases/check_account_balance_usecase.dart';
import 'package:cbrs/features/add_money/domain/usecases/check_transfer_rules_usecase.dart';
import 'package:cbrs/features/add_money/domain/usecases/get_linked_accounts_usecase.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import 'package:cbrs/features/send_money/data/models/check_transfer_rules_response.dart';

part 'add_money_event.dart';
part 'add_money_state.dart';

class AddMoneyBloc extends Bloc<AddMoneyEvent, AddMoneyState> {
  AddMoneyBloc({
    required GetLinkedAccounts getLinkedAccounts,
    required CheckAccountBalance checkAccountBalance,
    required CheckTransferRules checkTransferRules,
    required AddMoney addMoney,
  })  : _getLinkedAccounts = getLinkedAccounts,
        _checkAccountBalance = checkAccountBalance,
        _checkTransferRules = checkTransferRules,
        _addMoney = addMoney,
        super(const AddMoneyInitial()) {
    on<GetLinkedAccountsEvent>(_handleGetLinkedAccounts);
    on<CheckAccountBalanceEvent>(_handleCheckAccountBalance);
    on<ValidateAddMoneyAmountEvent>(_handleValidateAmount);
    on<AddMoneyTransferEvent>(_handleAddMoneyTransfer);
  }
  final GetLinkedAccounts _getLinkedAccounts;
  final CheckAccountBalance _checkAccountBalance;
  final CheckTransferRules _checkTransferRules;
  final AddMoney _addMoney;

  Future<void> _handleGetLinkedAccounts(
    GetLinkedAccountsEvent event,
    Emitter<AddMoneyState> emit,
  ) async {
    emit(const AddMoneyLoading());
    final result = await _getLinkedAccounts(
      GetLinkedAccountsParams(
        page: event.page,
        limit: event.limit,
        status: event.status,
      ),
    );

    debugPrint('add money fetchhed');
    result.fold((failure) => emit(AddMoneyError(failure.message)), (accounts) {
      debugPrint('accountntn ${accounts.docs.length}');
      // emit(const EmptyLinkedAccountState());
      debugPrint('EmptyLinkedAccountState');
      if (accounts.docs.isEmpty) {
        emit(const EmptyLinkedAccountState());
      } else {
        emit(LinkedAccountsLoaded(accounts));
      }
    });
  }

  Future<void> _handleCheckAccountBalance(
    CheckAccountBalanceEvent event,
    Emitter<AddMoneyState> emit,
  ) async {
    emit(const AddMoneyLoading());
    final result = await _checkAccountBalance(
      CheckAccountBalanceParams(
        bankId: event.bankId,
        accountNumber: event.accountNumber,
      ),
    );

    result.fold(
      (failure) => emit(AddMoneyError(failure.message)),
      (account) => emit(AccountBalanceChecked(account)),
    );
  }

  Future<void> _handleValidateAmount(
    ValidateAddMoneyAmountEvent event,
    Emitter<AddMoneyState> emit,
  ) async {
    emit(const AddMoneyLoading());
    debugPrint('💰 Validating amount: ${event.amount} ${event.currency}');

    final result = await _checkTransferRules(
      CheckTransferRulesParams(
        amount: event.amount,
        currency: event.currency,
        accountNumber: event.accountNumber,
        bankId: event.bankId,
      ),
    );

    result.fold(
      (failure) {
        debugPrint('❌ Transfer rules validation failed: ${failure.message}');
        return emit(AddMoneyError(failure.message));
      },
      (rules) {
        debugPrint('✅ Transfer rules response: $rules');
        if (!rules.success) {
          debugPrint('⚠️ Transfer rules failed: ${rules.message}');
          return emit(AddMoneyError(rules.message));
        }
        debugPrint('✅ Amount validated successfully');
        emit(AddMoneyAmountValidated(rules));
      },
    );
  }

  Future<void> _handleAddMoneyTransfer(
    AddMoneyTransferEvent event,
    Emitter<AddMoneyState> emit,
  ) async {
    emit(const AddMoneyLoading());
    debugPrint('💰 Adding money with bankId: ${event.bankId}');

    final result = await _addMoney(
      AddMoneyParams(
        accountNumber: event.accountNumber,
        bankId: event.bankId,
        amount: event.amount,
        currency: event.currency,
        senderName: event.senderName,
      ),
    );

    result.fold(
      (failure) => emit(AddMoneyError(failure.message)),
      (response) {
      

        if (response?.status == 'COMPLETED') {
          emit(AddMoneySuccess(response));
        } else if (response?.authorizationType == 'PIN_AND_OTP') {
          emit(
            AddMoneyPinRequired(
              billRefNo: response!.billRefNo,
              requiresOtp: true,
            ),
          );
        } else if (response.authorizationType == 'PIN') {
          emit(
            AddMoneyPinRequired(
              billRefNo: response.billRefNo,
            ),
          );
        } else {
          emit(
            const AddMoneyError(
              'Transfer failed: Invalid authorization type',
            ),
          );
        }
      },
    );
  }
}
