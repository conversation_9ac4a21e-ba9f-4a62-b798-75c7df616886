import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/send_money/domain/entities/wallet_details.dart';

class WalletDetailsModel extends WalletDetails {
  const WalletDetailsModel({
    required super.usdBalance,
    required super.etbBalance,
    required super.walletId,
    required super.usdWalletCode,
    required super.etbWalletCode,
  });

  factory WalletDetailsModel.fromJson(Map<String, dynamic> json) {
    double usdBalance = 0;
    double etbBalance = 0;
    var walletId = '';
    var usdWalletCode = '';
    var etbWalletCode = '';

    try {
      // Extract member ID from the member object
      if (json['member'] != null && json['member'] is Map<String, dynamic>) {
        walletId = json['member']['_id'] as String? ?? '';
      }

      // Process wallet data
      if (json['wallets'] != null && json['wallets'] is List) {
        final wallets = json['wallets'] as List;

        for (final wallet in wallets) {
          final data = wallet as Map<String, dynamic>;
          final currency = data['currency'] as String? ?? '';

          if (currency == 'USD') {
            usdBalance = AppMapper.safeDouble(data['balance']);
            //((data['balance'] as num?) ?? 0).toDouble();
            usdWalletCode = data['walletID'] as String? ?? '';
          } else if (currency == 'ETB') {
            etbBalance = AppMapper.safeDouble(data['balance']);
            etbWalletCode = data['walletID'] as String? ?? '';
          }
        }
      }

      return WalletDetailsModel(
        usdBalance: usdBalance,
        etbBalance: etbBalance,
        walletId: walletId,
        usdWalletCode: usdWalletCode,
        etbWalletCode: etbWalletCode,
      );
    } catch (e) {
      return const WalletDetailsModel(
        usdBalance: 0,
        etbBalance: 0,
        walletId: '',
        usdWalletCode: '',
        etbWalletCode: '',
      );
    }
  }

  Map<String, dynamic> toJson() => {
        'usdBalance': usdBalance,
        'etbBalance': etbBalance,
        'walletId': walletId,
        'usdWalletCode': usdWalletCode,
        'etbWalletCode': etbWalletCode,
      };
}
