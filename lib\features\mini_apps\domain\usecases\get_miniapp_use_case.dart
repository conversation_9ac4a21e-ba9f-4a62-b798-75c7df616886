import 'package:cbrs/core/utils/typedef.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:cbrs/core/error/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:cbrs/features/mini_apps/domain/repositories/miniapp_repository.dart';

class GetMiniappsUseCase
    extends UsecaseWithParams<MiniappEntity, GetMiniappsParams> {
  final MiniappRepository _repository;

  const GetMiniappsUseCase(this._repository);

  @override
  ResultFuture<MiniappEntity> call(GetMiniappsParams params) async {
    return _repository.getMiniapps(
        page: params.page,
        perPage: params.perPage,
        stage: params.stage

    );
  }
}

class GetMiniappsParams extends Equatable {
  final int page;
  final int perPage;
  final String stage;

  const GetMiniappsParams({
    required this.page,
    required this.perPage,
    required this.stage,
  });

  @override
  List<Object> get props => [page, perPage, stage];
}
