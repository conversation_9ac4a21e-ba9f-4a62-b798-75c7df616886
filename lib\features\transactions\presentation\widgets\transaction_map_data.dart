// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_row_transaction.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TransactionMapData {
  final Transaction transaction;

  TransactionMapData(this.transaction);

  final _dateFormatter = DateFormat('MMM dd, yyyy - hh:mm a');

  String get billAmount =>
      getFormattedAmountDisplay(amount: transaction.billAmount);
  String get totalAmount =>
      getFormattedAmountDisplay(amount: transaction.totalAmount ?? 0.0);
  String get paidAmount => getFormattedAmountDisplay(
        amount: transaction.paidAmount,
        hasCurrency: true,
        customCurrency: 'ETB',
      );
  String get serviceCharge =>
      getFormattedAmountDisplay(amount: transaction.serviceCharge ?? 0.0);
  String get vat => getFormattedAmountDisplay(amount: transaction.vat ?? 0.0);

  String get formattedDate => _dateFormatter.format(transaction.createdAt);
  String get _label => getTransactionLabel();
  String get billReason => transaction.billReason ?? 'unknown';

  /// currency gormating
  String _getCurrencySymbol(String code) {
    switch (code.toUpperCase()) {
      case 'USD':
        return r'$';
      case 'ETB':
        return 'ETB';

      default:
        return code;
    }
  }

  String formatAmount(double amount, String? currencyCode) {
    final symbol = _getCurrencySymbol(currencyCode ?? 'ETB');
    final formatter =
        NumberFormat.currency(locale: 'en_US', symbol: '$symbol ');
    return formatter.format(amount);
  }

  String getFormattedAmountDisplay({
    double amount = 0.0,
    bool hasCurrency = false,
    String customCurrency = '',
  }) {
    final originalFormatted = formatAmount(
      amount,
      hasCurrency ? customCurrency : transaction.originalCurrency,
    );

    return originalFormatted;
  }

  // ---- Transaction label ----

  String getTransactionLabel() {
    switch (transaction.transactionType.toLowerCase()) {
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'wallet_transfer':
        return 'Wallet Transfer';
      case 'change_to_birr':
        return 'Change To Birr';
      case 'load_to_wallet':
        return 'Load To Wallet';
      case 'add_money':
        return 'Add Money';

      case 'topup':
        return 'Mobile Top-up';
      case 'merchant_payment':
        switch (billReason.toLowerCase()) {
          case 'airtime':
            return 'Mobile Top-up';
          case 'package':
            return 'Gift Package';
          case 'utility':
            return 'Utility';
          case 'miniapp':
            return 'Mini Apps';

          default:
            return 'Merchant Payment';
        }

      case 'money_request':
        return 'Money Request';
      default:
        return 'Unknown Transaction';
    }
  }

  // ---- Row builder ----

  Widget _buildContainer({required String label, required String value}) {
    return Container(
      padding: EdgeInsets.only(bottom: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomBuildText(
            text: label,
            fontSize: 14.sp,
            color: const Color(0xFF000000).withOpacity(0.5),
            caseType: 'default',
          ),
          SizedBox(width: 12.w),
          Flexible(
            child: CustomBuildText(
              text: value,
              caseType: '',
              fontSize: 13.sp,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isStatus = false}) {
    if (!isStatus) {
      return _buildContainer(label: label, value: value);
    }
    if (value.isEmpty) return const SizedBox.shrink();
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CustomBuildText(
          text: label,
          color: Colors.grey[600]!,
        ),
        const SizedBox(width: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFFD4FECB),
            borderRadius: BorderRadius.circular(22),
          ),
          child: CustomBuildText(
            text: value,
            color: const Color(0xFF3EA100),
          ),
        ),
      ],
    );
  }

  // ---- Widget List Generator ----

  List<Widget> toWidgetList() {
    switch (_label) {
      case 'Bank Transfer':
        return _bankTransferDetails();
      case 'Wallet Transfer':
        return _walletTransferDetails();
      case 'Change To Birr':
        return _changeToBirr();
      case 'Load To Wallet':
        return _loadToWallet();
      case 'Add Money':
        return _addMoney();

      case 'Mobile Top-up':
        return _mobileTopUpDetails();

      case 'Merchant Payment':
        return _merchantPayment();

      default:
        return _defaultDetails(_label);
    }
  }

  List<Widget> _bankTransferDetails() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.senderEmail ?? transaction.senderPhone ?? 'N/A',
        ),
        _buildDetailRow('Bank Name', transaction.bankName ?? 'Unknown Bank'),
        _buildDetailRow('Recipent Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipent Account',
          transaction.beneficiaryAccountNo ?? 'N/A',
        ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in USD', billAmount),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow(
            'Excange Rate',
            '1 USD = ETB${transaction.exchangeRate}',
          ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in ETB', paidAmount),
        if (transaction.originalCurrency == 'ETB')
          _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('FT Reference', transaction.FTNumber ?? 'N/A'),
        _buildDetailRow('Transaction Date', formattedDate),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _walletTransferDetails() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.senderEmail ?? transaction.senderPhone ?? 'N/A',
        ),
        _buildDetailRow('Recipent Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipent Account',
          transaction.beneficiaryEmail ?? transaction.beneficiaryPhone ?? 'N/A',
        ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in USD', billAmount),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow(
            'Excange Rate',
            '1 USD = ETB${transaction.exchangeRate}',
          ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in ETB', paidAmount),
        if (transaction.originalCurrency == 'ETB')
          _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('Transaction Date', formattedDate),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _changeToBirr() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Amount in USD', billAmount ?? 'N/A'),
        _buildDetailRow(
          'Excange Rate',
          '1 USD = ETB${transaction.exchangeRate}',
        ),
        _buildDetailRow('Amount in ETB', paidAmount ?? 'N/A'),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _loadToWallet() => [
        _buildDetailRow('Transaction Type', _label),

        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.cardNumber ?? 'N/A',
        ), // masked - TODO
        _buildDetailRow('Recipient Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipient Account',
          transaction.beneficiaryPhone ?? transaction.beneficiaryEmail ?? 'N/A',
        ),
        _buildDetailRow('Amount in USD', billAmount),

        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('MPGS Ref No', transaction.mpgsReference ?? 'N/A'),

        _buildDetailRow('Transaction Date', formattedDate),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _addMoney() => [
        _buildDetailRow('Transaction Type', _label),

        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account Number',
          transaction.beneficiaryAccountNo ?? 'N/A',
        ),
        _buildDetailRow('Recipient Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipient Account',
          transaction.beneficiaryEmail ?? transaction.beneficiaryPhone ?? 'N/A',
        ),

        _buildDetailRow('Amount in ETB', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow(
          'FT  Reference',
          transaction.FTNumber ?? 'N/A',
        ), // todo

        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];
/*
  List<Widget> _giftPackageDetails() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Recipent Name', transaction.beneficiaryName ?? 'N/A'),
        _buildDetailRow(
          'Recipent Phone',
          transaction.beneficiaryPhone ?? 'N/A',
        ),


        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in USD', billAmount),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow(
            'Excange Rate',
            '1 USD = ETB${transaction.exchangeRate}',
          ),
        if (transaction.originalCurrency == 'USD')
          _buildDetailRow('Amount in ETB', paidAmount),
        if (transaction.originalCurrency == 'ETB')
          _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];
*/
  List<Widget> _mobileTopUpDetails() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.senderEmail ?? transaction.senderPhone ?? 'N/A',
        ),

        _buildDetailRow(
          'Recipent Name',
          transaction.beneficiaryName ?? 'N/A',
        ), // merchnat name - safaricom or ethiteleocm
        // _buildDetailRow(
        //   'Recipient Acccount',
        //   transaction.billRefNo ?? 'N/A',
        // ), // merchant aacccount or code
        _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),

        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  /// There are different merchant payments.
  ///
  List<Widget> _merchantPayment() => [
        _buildDetailRow('Transaction Type', _label),
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.senderEmail ?? transaction.senderPhone ?? 'N/A',
        ),

        _buildDetailRow(
          'Recipent Name',
          transaction.beneficiaryName ?? 'N/A',
        ), // merchnat name
        _buildDetailRow(
          'Recipient Acccount',
          transaction.billRefNo ?? 'N/A',
        ), // merchant aacccount or code
        _buildDetailRow('Amount', billAmount),
        _buildDetailRow('Service Fee', serviceCharge),
        _buildDetailRow('VAT', vat),
        _buildDetailRow('Date', formattedDate),
        _buildDetailRow('Connect Ref No', transaction.walletFTNumber ?? 'N/A'),

        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];

  List<Widget> _defaultDetails(String label) => [
        _buildDetailRow('Transaction ID', transaction.id),
        _buildDetailRow('Type', label),
        _buildDetailRow('Status', transaction.status, isStatus: true),
      ];
}
