import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/mini_apps/domain/entities/create_order_miniapp_entity.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:cbrs/core/error/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:cbrs/features/mini_apps/domain/repositories/miniapp_repository.dart';

class MiniappCreateOrderUseCase
    extends UsecaseWithParams<CreateOrderMiniappEntity, CreateOrderParams> {
  const MiniappCreateOrderUseCase(this._repository);
  final MiniappRepository _repository;

  @override
  ResultFuture<CreateOrderMiniappEntity> call(CreateOrderParams params) async {
    return _repository.createOrder(
      data: params.data,
    );
  }
}

class CreateOrderParams extends Equatable {
  const CreateOrderParams({
    required this.data,
  });
  final dynamic data;

  @override
  List<Object> get props => [];
}
