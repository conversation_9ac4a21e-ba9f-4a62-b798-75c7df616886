import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';
import 'package:cbrs/features/my_connect/domain/repositories/my_connect_repository.dart';
import 'package:equatable/equatable.dart';

class MyConnectSendRequestUseCase
    implements UsecaseWithParams<ConnectionRequestEntity, SendRequestParams> {
  final MyConnectRepository repository;

  const MyConnectSendRequestUseCase(this.repository);

  @override
  ResultFuture<ConnectionRequestEntity> call(SendRequestParams params) async {
    return await repository.sendConnectionRequest(
      recipientId: params.recipientId,
      recipientName: params.recipientName,
      recipientEmail: params.recipientEmail,
      recipientPhone: params.recipientPhone,
      recipientAvatar: params.recipientAvatar,
    );
  }
}

class SendRequestParams extends Equatable {
  final String recipientId;
  final String recipientName;
  final String? recipientEmail;
  final String recipientPhone;
  final String? recipientAvatar;

  const SendRequestParams({
    required this.recipientId,
    required this.recipientName,
    this.recipientEmail,
    required this.recipientPhone,
    this.recipientAvatar,
  });

  @override
  List<Object?> get props => [
        recipientId,
        recipientName,
        recipientEmail,
        recipientPhone,
        recipientAvatar,
      ];
}
