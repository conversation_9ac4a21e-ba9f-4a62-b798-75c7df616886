import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_request.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_response.dart';
import 'package:cbrs/features/load_wallet/domain/repositories/load_wallet_repository.dart';

class LoadToWalletUseCase {

  LoadToWalletUseCase(this.repository);
  final LoadWalletRepository repository;

  ResultFuture<LoadWalletResponse> execute(double amount) async {
    return await repository.loadToWallet(LoadWalletRequest(amount: amount));
  }

  ResultFuture<LoadWalletResponse> getRedirectURL(String billRefNo) async {
    return await repository.getLoadWalletDetails(billRefNo);
  }
}
