import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:flutter/foundation.dart';

class MiniappModel extends MiniappEntity {
  const MiniappModel({
    required super.miniappData,
    super.miniappPaginate,
  });

  factory MiniappModel.fromJson(Map<String, dynamic> json) {
    /// TODO
    // final ç = json['merchantId'] as List?;

    debugPrint("jsosn fo data  ${json}");

    return MiniappModel(
      miniappData:
      // todo - json['data'] =. CHNAGE
      AppMapper.safeList<dynamic>(json['docs'])
          .map((item) => MiniappDataModel.fromJson(AppMapper.safeMap(item)))
          .toList(),
      miniappPaginate: MiniappPaginateModel.fromJson(json),
    );
  }
}

class MiniappDataModel extends MiniappDataEntity {
  const MiniappDataModel({
    required super.merchantType,
    required super.miniAppIcon,
    required super.miniAppName,
    required super.url,
  });

  factory MiniappDataModel.fromJson(Map<String, dynamic> json) {
    final merchantData = json['merchantId'] as Map<String, dynamic>?;
    debugPrint("${merchantData?['merchantType']} ☺️ ");
    return MiniappDataModel(
      merchantType: merchantData?['merchantType']?.toString() ?? '',
      miniAppIcon: json['miniAppIcon']?.toString() ?? '',
      miniAppName: json['miniAppName']?.toString() ?? '',
      url: json['url']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'merchantType': merchantType,
      'miniAppIcon': miniAppIcon,
      'miniAppName': miniAppName,
      'url': url,
    };
  }
}

class MiniappPaginateModel extends MiniappPaginateEntity {
  const MiniappPaginateModel({
    required super.currentPage,
    required super.totalPages,
    required super.perPage,
    required super.totalItems,
  });

  factory MiniappPaginateModel.fromJson(Map<String, dynamic> json) {
    debugPrint(
        " totlitems ${json['totalDocs']} total page ${json["totalPages"]} perPage ${json['limit']}");
    return MiniappPaginateModel(
      currentPage: 0,
      totalItems: int.tryParse(json['totalDocs'].toString()) ?? 0,
      totalPages: int.tryParse(json['totalPages'].toString()) ?? 0,
      perPage: int.tryParse(json['limit'].toString()) ?? 0,
    );
  }
}
