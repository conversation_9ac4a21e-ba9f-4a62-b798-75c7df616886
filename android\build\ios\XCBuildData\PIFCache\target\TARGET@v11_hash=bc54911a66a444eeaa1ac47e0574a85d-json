{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847cd4e319875f6dfc3106a8783aa78b7", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1cbefd34c3ef86432f9c96704f21a8e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1cbefd34c3ef86432f9c96704f21a8e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9855b174ee8b7bcfaed914f3e68eb91730", "guid": "bfdfe7dc352907fc980b868725387e98a27a49b00be0ac981e5db9f07832563d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98436d8567c324c7446b1ffcc80d1c4651", "guid": "bfdfe7dc352907fc980b868725387e989878394bd4334bc5b75f69e19db62bf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989500d83026ed5a5ce95e5e884652d323", "guid": "bfdfe7dc352907fc980b868725387e98e1897e3d8aed17671192779f85b1c248", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c347b0a233aee2121a20e6bbe2613ec", "guid": "bfdfe7dc352907fc980b868725387e981450c482f5c290824d437888a184ae8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986901dde35324bc3a7f442b2f99af352f", "guid": "bfdfe7dc352907fc980b868725387e98e4fc9878a7a9815c87ead588b617e13f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be9bb98c1c6c0f500883f51ac80c8dd4", "guid": "bfdfe7dc352907fc980b868725387e98931f24651cd163bcb560cc6bf0293fcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c2fa52c829bef1f6ccaea0efc1badc", "guid": "bfdfe7dc352907fc980b868725387e98d4cfb9e1cbc63fdd6505f85f9a29df9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98336fa0897ddb5a3523ccaef6c9f60c91", "guid": "bfdfe7dc352907fc980b868725387e98603cbbc70b8dca9ffe9ae016055178e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec0faa74cab4a7c98d77d4164a54ec83", "guid": "bfdfe7dc352907fc980b868725387e98de1a9153e9195626d2e18082bddd5583", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c91a6a4b6641c3f51c2ac1b4fdd05dcd", "guid": "bfdfe7dc352907fc980b868725387e984a1d400fa247fcd1999957a8682fc831", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c732a16d9cdc27ea09010dbe3691e9bf", "guid": "bfdfe7dc352907fc980b868725387e98d32709d3a2dbac2c57e7e408179d0866", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98090d4d6bbf7fecce219b5af44c83b7a3", "guid": "bfdfe7dc352907fc980b868725387e9852b51035de7e7268120a09bd61060c70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b8f651afae1fa3ad0546811ff40c2b4", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2b79b715b17ca3d7b1bd41236e37400", "guid": "bfdfe7dc352907fc980b868725387e9851124daa4e024f512c6435eeca4166ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1818f0446b9ee0a2a9226d3bd916947", "guid": "bfdfe7dc352907fc980b868725387e98d1ab4894e5f8a787a882b5c8c35da9b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845b9d530ebee59d5132d20bc30db1f19", "guid": "bfdfe7dc352907fc980b868725387e980ef321af2f9f2c834e49de13f3d55bc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b32a325a848453596c83e0062473596", "guid": "bfdfe7dc352907fc980b868725387e98f0d31a429c82f17b0991aeffa8fa048c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ac50626a79eb1065240d86b283bb9c7", "guid": "bfdfe7dc352907fc980b868725387e98a42a0c118dedfd60efdbb4d16492c7b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bbfe9ef1dadd842b5f64837a36eb6a4", "guid": "bfdfe7dc352907fc980b868725387e98b3c686ca16e5bce7ed24eabc68c27a7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863bfac0115d86b0514d93e52f47b9cff", "guid": "bfdfe7dc352907fc980b868725387e9828accb1b5b23f919db719dddafe64428", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98539565939a9ee82c896e5610b21ba67d", "guid": "bfdfe7dc352907fc980b868725387e981a70531bb3ca84e549cc6ba35f299fa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98033e694662a53fefaf3340692f33a3c8", "guid": "bfdfe7dc352907fc980b868725387e98d126da608ed212ace70c11a62a0c1637", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be2610bcece50b88c5eb36dee9e5ac87", "guid": "bfdfe7dc352907fc980b868725387e98482d9659cbec4dc229fbd14d4097dbb0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c464dadffe00f6103318b7944230050", "guid": "bfdfe7dc352907fc980b868725387e989514a07acecd425069c301ac83629a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893835a63d0e36fce890dfe73b027678c", "guid": "bfdfe7dc352907fc980b868725387e98c5aeecdf68b8bd150764901c66af09a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985edd5c2d13dafffd172272077bd051b8", "guid": "bfdfe7dc352907fc980b868725387e98845832e3ddc9b9efa82804181189873f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c99e3e793580697baf1b38b8f5f0eab5", "guid": "bfdfe7dc352907fc980b868725387e988422c8d41a531ab0e5feda22b2729450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecdfe175576027ace702758bbb74277e", "guid": "bfdfe7dc352907fc980b868725387e98032b5432a99c409a15c23484d253896f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0d054a32c59aa44ed676d1869009d18", "guid": "bfdfe7dc352907fc980b868725387e988ec79e04b31e29a98b9058ffe01c7fe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fa3528ffaf2f474f9f3ce01936612cb", "guid": "bfdfe7dc352907fc980b868725387e980e70a7a6c573b1b7ccd3e1d5ab5d07e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eb653468a8d4b01fe86b65b22e7acbc", "guid": "bfdfe7dc352907fc980b868725387e983e056fb9529e4b65a3f33b7e5569c272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9b731ca0c4ccffcedab443456f21a2a", "guid": "bfdfe7dc352907fc980b868725387e980341c01be7632895d55fd1dac2b54ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e685f0a2b83b12f71a22a52bc75cd96", "guid": "bfdfe7dc352907fc980b868725387e9844b9c045f1529acb68ed027e0a48b614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362823edb9cae699cca3bb9882f6c323", "guid": "bfdfe7dc352907fc980b868725387e98b1b6e64bf6811487ed4c6e1384077226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab15ec53c56372bd161ae790acd9fe2", "guid": "bfdfe7dc352907fc980b868725387e981f2b6f47d148085767ead35147c92b0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819c3e0f2cb311fcec8504cd786f95bd5", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ae6160d7065cc26d697f4443d0d1886", "guid": "bfdfe7dc352907fc980b868725387e98ff4558f6e06cbb7fbd15dd015ca18854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98276ccf7ca63d3a2344aeeb4ed16c6f63", "guid": "bfdfe7dc352907fc980b868725387e983caf79b54a68f27872c153b09b7289cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da9a35eadb44cb685e36fd08852e4cd0", "guid": "bfdfe7dc352907fc980b868725387e983550715f5dc47c6e779aeb12290a7c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822c53ac4ec091545944305620552139d", "guid": "bfdfe7dc352907fc980b868725387e985800f051739a623bb35f7cc22cbdb0d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b23d02c432768511c02f16ea804996d8", "guid": "bfdfe7dc352907fc980b868725387e98f2203b5d78f4ac52bb8f2618d0202e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671862c4fd710a33aa0fbf9b9082bcfb", "guid": "bfdfe7dc352907fc980b868725387e983616275298339a71f8ce5405edcae646"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f86ef1075aa5d5c2f31d22220a08d6ed", "guid": "bfdfe7dc352907fc980b868725387e9859b2f7d951b31f73b36500eee5613569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d3ebca32cba8eb2c221e1e58b9873f8", "guid": "bfdfe7dc352907fc980b868725387e9800fc7c6d98b1d598e7f0946391c24f5b"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}