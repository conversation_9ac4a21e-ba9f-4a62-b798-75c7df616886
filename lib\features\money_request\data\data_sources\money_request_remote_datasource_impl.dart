import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/money_request/data/data_sources/money_request_remote_datasource.dart';
import 'package:cbrs/features/money_request/data/models/get_wallet_detail_response.dart';
import 'package:cbrs/features/money_request/data/models/member_lookup_response.dart';
import 'package:cbrs/features/money_request/data/models/my_requests_list_response.dart';
import 'package:flutter/material.dart';

class MoneyRequestRemoteDataSourceImpl implements MoneyRequestRemoteDataSource {
  const MoneyRequestRemoteDataSourceImpl({
    required ApiService apiService,
    required AuthLocalDataSource authLocalDataSource,
  })  : _apiService = apiService,
        _authLocalDataSource = authLocalDataSource;

  final ApiService _apiService;
  final AuthLocalDataSource _authLocalDataSource;

  Future<T> _handleApiResponse<T>(
    Future<T> Function(Map<String, dynamic>) parser,
    Future<Result<Map<String, dynamic>>> resultFuture,
  ) async {
    try {
      final result = await resultFuture;

      return result.fold(
        (data) async {
          if (data['success'] == false) {
            throw ApiException(
              message: data['message'] as String? ?? 'Operation failed',
              statusCode: 400,
            );
          }
          return parser(data);
        },
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'An unexpected error occurred: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<MemberLookupResponse> memberLookUp({
    String? emailAddress,
    String? phoneNumber,
  }) async {
    return _handleApiResponse<MemberLookupResponse>(
      (data) async {
        if (data['data'] == null) {
          throw const ApiException(
            message: 'Invalid response format',
            statusCode: 500,
          );
        }

        return MemberLookupResponse.fromJson(
          data['data'] as Map<String, dynamic>,
        );
      },
      _apiService.post(
        ApiEndpoints.memberLookup,
        data: {
          if (emailAddress != null) 'email': emailAddress,
          if (phoneNumber != null) 'phoneNumber': phoneNumber,
        },
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<GetWalletDetailResponse> getWalletDetail() async {
    final userId = await _authLocalDataSource.getUserId() ?? '';

    return _handleApiResponse<GetWalletDetailResponse>(
      (data) async {
        return GetWalletDetailResponse.fromJson(data);
      },
      _apiService.get(
        ApiEndpoints.getWalletDetailsEndpoint(userId),
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<MyRequestResponse> sendMoneyRequest({
    required String memberId,
    required double amount,
    required String transactionType,
    required String currency,
    required String? reason,
  }) async {
    final result = await _apiService.post(
      ApiEndpoints.sendMoneyRequest,
      data: {
        'memberID': memberId,
        'amount': amount,
        'transactionType': transactionType,
        'currency': currency,
        'billReason': reason,
      },
      parser: (data) => MyRequestResponse.fromJson(
        data as Map<String, dynamic>,
      ),
    );

    debugPrint("'ressssoutl");
    return result.fold(
      (data) => data,
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );


  }

  @override
  Future<MyRequestsResponse> getMoneyRequestList({
    required int page,
    String? currency,
  }) async {
    return _handleApiResponse<MyRequestsResponse>(
      (data) async {
        return MyRequestsResponse.fromJson(data);
      },
      _apiService.get(
        ApiEndpoints.getMoneyRequestList(page: page, currency: currency),
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<MyRequestResponse> acceptOrRejectOrCancelRequest({
    required String? transactionId,
    required String? status,
    required double? amount,
    String? reason,
  }) async {
    return _handleApiResponse<MyRequestResponse>(
      (data) async {
        return MyRequestResponse.fromJson(data);
      },
      _apiService.post(
        ApiEndpoints.acceptOrRejectOrCancelRequest,
        data: {
          'transactionID': transactionId,
          'status': status,
          'reason': reason,
          if (status == 'ACCEPTED') 'amount': amount,
        },
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<MyRequestResponse> confirmRequest({
    required String? billRefNo,
    required String? pin,
  }) async {
    return _handleApiResponse<MyRequestResponse>(
      (data) async {
        return MyRequestResponse.fromJson(data);
      },
      _apiService.post(
        ApiEndpoints.confirmRequest,
        data: {
          'billRefNo': billRefNo,
          'transactionType': 'money_request',
          'PIN': pin,
        },
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<MyRequestResponse> getRequestDetail({
    required String transactionId,
  }) async {
    return _handleApiResponse<MyRequestResponse>(
      (data) async {
        return MyRequestResponse.fromJson(data);
      },
      _apiService.get(
        ApiEndpoints.requestTransactionsDetail(transactionId),
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }
}
