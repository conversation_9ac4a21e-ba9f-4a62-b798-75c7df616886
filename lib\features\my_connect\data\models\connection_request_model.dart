import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';

class ConnectionRequestModel extends ConnectionRequestEntity {
  const ConnectionRequestModel({
    required super.requester,
    required super.recipient,
    required super.status,
    required super.sentAt,
    super.connectedAt,
    super.declinedAt,
    required super.id,
    required super.createdAt,
    required super.updatedAt,
  });

  factory ConnectionRequestModel.fromJson(Map<String, dynamic> json) {
    return ConnectionRequestModel(
      requester: json['requester'] as String,
      recipient: json['recipient'] as String,
      status: json['status'] as String,
      sentAt: DateTime.parse(json['sentAt'] as String),
      connectedAt: json['connectedAt'] != null
          ? DateTime.parse(json['connectedAt'] as String)
          : null,
      declinedAt: json['declinedAt'] != null
          ? DateTime.parse(json['declinedAt'] as String)
          : null,
      id: json['_id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'requester': requester,
      'recipient': recipient,
      'status': status,
      'sentAt': sentAt.toIso8601String(),
      'connectedAt': connectedAt?.toIso8601String(),
      'declinedAt': declinedAt?.toIso8601String(),
      '_id': id,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
