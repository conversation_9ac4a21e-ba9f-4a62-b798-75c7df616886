import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';
import 'package:cbrs/features/utility/domain/entities/create_order_utility_entity.dart';

class ConnectionRequestModel extends ConnectionRequestEntity {
  const ConnectionRequestModel({
    required super.requester,
    required super.recipient,
    required super.status,
    required super.sentAt,
    super.connectedAt,
    super.declinedAt,
    required super.id,
    required super.createdAt,
    required super.updatedAt,
  });

  factory ConnectionRequestModel.fromJson(Map<String, dynamic> json) {
    return ConnectionRequestModel(
      requester: RequesterModel.fromJson(AppMapper.safeMap(json['requester'])),
      recipient: RecipientModel.fromJson(AppMapper.safeMap(json['recipient'])),
      status: json['status'] as String,
      sentAt: DateTime.parse(json['sentAt'] as String),
      connectedAt: json['connectedAt'] != null
          ? DateTime.parse(json['connectedAt'] as String)
          : null,
      declinedAt: json['declinedAt'] != null
          ? DateTime.parse(json['declinedAt'] as String)
          : null,
      id: json['_id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'requester': requester,
      'recipient': recipient,
      'status': status,
      'sentAt': sentAt.toIso8601String(),
      'connectedAt': connectedAt?.toIso8601String(),
      'declinedAt': declinedAt?.toIso8601String(),
      '_id': id,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class RequesterModel extends RequesterEntity {
  const RequesterModel({
    super.id,
    super.firstName,
    super.lastName,
    super.phoneNumber,
  });

  factory RequesterModel.fromJson(Map<String, dynamic> json) {
    /// TODO
    // final ç = json['merchantId'] as List?;

    return RequesterModel(
      id: AppMapper.safeString(json['id']),
      firstName: AppMapper.safeString(json['firstName']),
      lastName: AppMapper.safeString(json['lastName']),
      phoneNumber: AppMapper.safeString(json['phoneNumber']),
      // request: RequestModel.fromJson(AppMapper.safeMap(json['request'])),
    );
  }
}

class RecipientModel extends RecipientEntity {
  const RecipientModel({
    super.id,
    super.firstName,
    super.lastName,
    super.phoneNumber,
  });

  factory RecipientModel.fromJson(Map<String, dynamic> json) {
    /// TODO
    // final ç = json['merchantId'] as List?;

    return RecipientModel(
      id: AppMapper.safeString(json['id']),
      firstName: AppMapper.safeString(json['firstName']),
      lastName: AppMapper.safeString(json['lastName']),
      phoneNumber: AppMapper.safeString(json['phoneNumber']),
      // request: RequestModel.fromJson(AppMapper.safeMap(json['request'])),
    );
  }
}
