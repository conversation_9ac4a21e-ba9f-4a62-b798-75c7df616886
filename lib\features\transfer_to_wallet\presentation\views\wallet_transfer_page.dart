import 'dart:math';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart'
    hide GetWalletDetailsEvent, WalletDetailsLoaded;
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_phone_field.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/home/<USER>/widgets/quick_wallet/quick_wallet_recipients_bottom_sheet.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/member_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/recent_wallet_transfer_hive.dart';
import 'package:cbrs/features/transfer_to_wallet/presentation/helpers/contact_helper.dart';
import 'package:cbrs/features/transfer_to_wallet/presentation/widgets/contact_selector.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:phone_form_field/phone_form_field.dart';
import 'package:shimmer/shimmer.dart';

class TransferToWalletPage extends StatefulWidget {
  const TransferToWalletPage({super.key});

  @override
  State<TransferToWalletPage> createState() => _TransferToWalletPageState();
}

class _TransferToWalletPageState extends State<TransferToWalletPage>
    with TickerProviderStateMixin {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  PhoneController phoneFieldController = PhoneController(
    initialValue: const PhoneNumber(isoCode: IsoCode.ET, nsn: ''),
  );

  final FocusNode focusNode = FocusNode();

  bool _isValidEmail = false;
  bool _showRecipientPreview = false;
  String _recipientName = '';
  String _recipientPhone = '';
  String _recipentAvatar = '';
  final String _currentUserPhone = '';

  bool canLookUp = true;

  bool isRecentLookedUp = false;
  bool _isValidPhone = false;
  bool _isLoading = false;
  MemberLookupResponse? _memberInfo;
  RecentRecipient? recipient;
  double _walletBalance = 0;
  bool _isKeyboardVisible = false;

  String selectedTab = 'Phone Number';
  final List<String> tabList = ['Phone Number', 'Email'];

  void onTap(String tabName) {
    if (tabName != selectedTab) {
      setState(() {
        selectedTab = tabName;
        _isValidEmail = false;
        _isValidPhone = false;
        _showRecipientPreview = false;
        _recipientName = '';
        _recipentAvatar = '';
        _memberInfo = null;
        if (selectedTab == 'Email') {
          _emailController.clear();
        } else {
          _phoneController.clear();
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _fetchRecentRecipients();

    context.read<BankTransferBloc>().add(const GetSupportedBanksEvent());

    _emailController.addListener(() {
      if (selectedTab == 'Email') {
        _validateEmail();
      }
    });

    phoneFieldController.addListener(_validatePhone);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  String userID = '';
  bool _isLoadingRecent = false;
  List<RecentWalletTransferHive>? recentWalletTransactions;

  double dashenExchangeAmount = 0;
  void _fetchRecentRecipients() {
    debugPrint('ffetching recent recipents');
    setState(() {
      _isLoadingRecent = true;
    });

    context.read<RecentWalletTransferBloc>().add(
          const GetRecentWalletTransferEvent(limit: 10),
        );
  }

  void _validateEmail() {
    if (!mounted) return;
    final email = _emailController.text.trim();
    final isValid = EmailValidator.validate(email);
    setState(() {
      _isValidEmail = isValid;
      if (isValid) {
        _showRecipientPreview = false;
      }
    });

    if (isValid) {
      context.read<WalletTransferBloc>().add(
            LookupMemberEvent(email: email),
          );
    } else {
      setState(() {
        _showRecipientPreview = false;
      });
    }
  }

  Future<void> _validatePhone() async {
    setState(() {
      _isValidPhone = phoneFieldController.value.isValid() ?? false;
      if (_isValidPhone) {
        setState(() {
          _showRecipientPreview = false;
        });
        final fullPhoneNumber =
            '+${phoneFieldController.value.countryCode}${phoneFieldController.value.nsn}';
        if (canLookUp) {
          if (_currentUserPhone == fullPhoneNumber) {
            CustomToastification(
              context,
              message: 'Phone number must be different from your wallet number',
            );
            return;
          } else if (ContactHelper.checkInContact(fullPhoneNumber,
              (memberInfo, name, phone, avatar, showPreview) {
            setState(() {
              canLookUp = false;
              _showRecipientPreview = showPreview;
              _recipientPhone = phone;
              _recipientName = name;
              _recipentAvatar = avatar;
              _memberInfo = memberInfo;
            });
          })) {
            return;
          }
          context.read<WalletTransferBloc>().add(
                LookupMemberEvent(phoneNumber: fullPhoneNumber),
              );
        }
      } else {
        setState(() {
          _showRecipientPreview = false;
          canLookUp = true;
        });
      }
    });
  }

  void _handleContactSelected(
    MemberLookupResponse memberInfo,
    String name,
    String phone,
    String avatar,
    bool showPreview,
  ) {
    setState(() {
      canLookUp = false;
      _memberInfo = memberInfo;
      if (phone.isNotEmpty) _isValidPhone = true;
      _recipientName = name;
      _recipientPhone = phone;
      _recipentAvatar = avatar;
      _showRecipientPreview = showPreview;
    });
  }

  String currency = '';
  @override
  Widget build(BuildContext context) {
    _isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;
    final theme = Theme.of(context);

    currency = GoRouterState.of(context).pathParameters['currency'] ?? 'usd';

    return BlocConsumer<BankTransferBloc, BankTransferState>(
      listener: (context, state) {
        if (state is SupportedBanksLoaded) {
          final banks = state.banks
              .where((bank) => bank.displayName.contains('Dashen'))
              .toList();

          setState(() {
            dashenExchangeAmount = banks[0].exchangeRate?.rate ?? 0.0;
          });
        }
      },
      builder: (context, state) {
        return BlocListener<WalletTransferBloc, WalletTransferState>(
          listener: (context, state) {
            if (state is MemberLookupSuccess) {
              setState(() {
                _recipientName = state.member.fullName;
                _showRecipientPreview = true;
                _memberInfo = state.member;
                _recipientPhone = state.member.phoneNumber;
              });
            } else if (state is WalletDetailsLoaded) {
              setState(() {
                _walletBalance = state.wallet.balance;
                _isLoading = false;
              });
              if (_memberInfo != null) {
                context.pushNamed(
                  AppRouteName.walletTransferAddAmount,
                  pathParameters: {
                    'currency': currency,
                  },
                  extra: {
                    'walletBalance': _walletBalance,
                    'memberInfo': _memberInfo,
                    'isFromQuick': false,
                  },
                );
              } else if (recipient != null) {
                context.pushNamed(
                  AppRouteName.walletTransferAddAmount,
                  pathParameters: {
                    'currency': currency,
                  },
                  extra: {
                    'walletBalance': _walletBalance,
                    'memberInfo': _memberInfo,
                    'isFromQuick': true,
                    'recipent': recipient,
                  },
                );
              }
            } else if (state is MemberLookupNotFound) {
              setState(() {
                _showRecipientPreview = false;
                if (selectedTab == 'Email') {
                  _isValidEmail = false;
                } else {
                  _isValidEmail = _isValidEmail;
                }
                if (selectedTab != 'Email') {
                  _isValidPhone = false;
                } else {
                  _isValidPhone = _isValidPhone;
                }
              });
              CustomToastification(
                context,
                message: state.message,
              );
            } else if (state is WalletTransferError) {
              setState(() {
                _isLoading = false;
              });
              CustomToastification(
                context,
                message: state.message,
              );
            }
          },
          child: Scaffold(
            appBar: AppBar(
              title: const Text(
                'Transfer to Wallet',
              ),
            ),
            body: SafeArea(
              bottom: true,
              child: GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const CustomPageHeader(
                                pageTitle: 'Recipient Address',
                                description:
                                    'Enter the recipient account number on the selected  bank and send money.',
                              ),
                              SizedBox(height: 24.h),
                              CustomRoundedTabs(
                                onTap: onTap,
                                selectedTab: selectedTab,
                                tabList: tabList,
                              ),
                              SizedBox(height: 24.h),
                              if (selectedTab == 'Email')
                                _buildEmailForm()
                              else
                                _buildPhoneForm(),
                              if (_showRecipientPreview) ...[
                                SizedBox(height: 16.h),
                                RecipientCard(
                                  avatar: _recipentAvatar,
                                  name: _recipientName,
                                  accountNumber: selectedTab != 'Email'
                                      ? _recipientPhone
                                      : _emailController.text,
                                  isBirrTransfer:
                                      currency.toLowerCase() == 'etb',
                                  // recipientEmail: _emailController.text,
                                  onTap: _handleContinue,
                                ),
                              ],
                              // if (_isLoadingRecent &&
                              //     recentWalletTransactions == null)

                              BlocConsumer<RecentWalletTransferBloc,
                                  WalletTransferState>(
                                listener: (context, state) {
                                  if (state
                                      is LoadedRecentWalletTransferState) {
                                    setState(() {
                                      recentWalletTransactions =
                                          state.recentWalletTransactions;
                                    });
                                  }
                                },
                                builder: (context, state) {
                                  if (state is QuickWalletLoading &&
                                      recentWalletTransactions == null) {
                                    return _recentShimmer();
                                  }
                                  if (state
                                      is LoadedRecentWalletTransferState) {
                                    return _buildRecipientsList(
                                      recentWalletTransactions!,
                                    );
                                  } else {
                                    return const SizedBox.shrink();
                                  }
                                },
                              ),
                              SizedBox(height: 24.h),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 5),
                      child: CustomRoundedBtn(
                        btnText: _isLoading ? 'Loading...' : 'Continue',
                        onTap: _showRecipientPreview
                            ? _handleContinue
                            : (selectedTab == 'Email'
                                        ? _isValidEmail
                                        : _isValidPhone) &&
                                    !_isLoading
                                ? _handleContinue
                                : null,
                        isLoading: _isLoading,
                        isBtnActive: _showRecipientPreview ||
                            (selectedTab == 'Email'
                                ? _isValidEmail
                                : _isValidPhone && !_isLoading),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmailForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Email Address',
          style: GoogleFonts.outfit(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: TextField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    color: Colors.black87,
                  ),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: 'Enter Email Address',
                    hintStyle: GoogleFonts.outfit(
                      color: Colors.grey[400],
                      fontSize: 16.sp,
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 16.h,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPhoneForm() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Phone Number',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: TransactionPhoneField(
                    controller: phoneFieldController,
                    focusNode: FocusNode(),
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              ContactSelector(
                phoneController: phoneFieldController,
                onContactSelected: _handleContactSelected,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleContinue() {
    FocusScope.of(context).unfocus();
    if (_memberInfo != null) {
      context.pushNamed(
        AppRouteName.walletTransferAddAmount,
        pathParameters: {
          'currency': currency,
        },
        extra: {
          'dashenExchangeAmount': dashenExchangeAmount,
          'memberInfo': _memberInfo,
          'isFromQuick': false,
        },
      );
    } else if (recipient != null) {
      context.pushNamed(
        AppRouteName.walletTransferAddAmount,
        pathParameters: {
          'currency': currency,
        },
        extra: {
          'dashenExchangeAmount': dashenExchangeAmount,
          'memberInfo': _memberInfo,
          'isFromQuick': true,
          'recipent': recipient,
        },
      );
    }
  }

  Widget _recentShimmer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 24.h),
        const CustomBuildText(
          text: 'Quick Wallet Transfer',
          color: Color(0xFFAAAAAA),
          fontSize: 13,
        ),
        const SizedBox(
          height: 8,
        ),
        SizedBox(
          height: 90,
          child: Row(
            children: [
              Expanded(
                child: SizedBox(
                  height: 100,
                  // color: Colors.grey,
                  child: ListView.separated(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    itemCount: 10,
                    separatorBuilder: (context, index) => SizedBox(width: 12.h),
                    itemBuilder: (context, index) {
                      return _buildRecipientTileShimmer();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRecipientTileShimmer() {
    return Column(
      children: [
        Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: 42,
            height: 42,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: 40,
            height: 10,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildRecipientsList(List<RecentWalletTransferHive> recipients) {
    recipients.sort(
      (a, b) => b.createdAt.compareTo(a.createdAt),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 24.h),
        if (recipients.isNotEmpty)
          const CustomBuildText(
            text: 'Quick Wallet Transfer',
            color: Color(0xFFAAAAAA),
            fontSize: 13,
          ),
        const SizedBox(
          height: 8,
        ),
        SizedBox(
          height: 90,
          child: Row(
            children: [
              Expanded(
                child: SizedBox(
                  height: 100,
                  // color: Colors.grey,
                  child: ListView.separated(
                    // padding: EdgeInsets.symmetric(horizontal: 16.w),
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    itemCount: recipients.length,

                    separatorBuilder: (context, index) => SizedBox(width: 12.h),
                    itemBuilder: (context, index) {
                      final transfer = recipients[index];

                      final name = transfer.recipientName ?? '';
                      final phone = transfer.recipientPhone ?? '';
                      final email = transfer.recipientEmail ?? '';

                      final avatarUrl = transfer.avatar;
                      //transfer.avatar ?? '';

                      debugPrint('avataar $avatarUrl');
                      final date = transfer.createdAt;

                      return _buildRecipientTile(
                        RecentRecipient(
                          id: transfer.recipientId ?? '',
                          name: name,
                          phone: phone,
                          date: date,
                          email: email,
                          avatarUrl: avatarUrl,
                        ),
                        context,
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRecipientTile(RecentRecipient recipient, BuildContext context) {
    return GestureDetector(
      onTap: () => _navigateToQuickTransfer(context, recipient),
      child: Container(
        // padding: EdgeInsets.symmetric(horizontal: 12.w),

        child: Column(
          children: [
            Container(
              width: 42.w,
              height: 42.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).primaryColor,
              ),
              clipBehavior: Clip.antiAlias,
              child: recipient.avatarUrl.isNotEmpty
                  ? CustomCachedImage(
                      url: recipient.avatarUrl,
                    )
                  : _buildInitialsAvatar(recipient, context),
            ),
            SizedBox(height: 8.w),
            CustomBuildText(
              text: recipient.name.split(' ').first,
              style: GoogleFonts.outfit(
                fontSize: 11.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialsAvatar(RecentRecipient recipient, BuildContext context) {
    return Center(
      child: Text(
        recipient.name.isNotEmpty
            ? recipient.name
                .substring(0, min(2, recipient.name.length))
                .toUpperCase()
            : '??',
        style: GoogleFonts.outfit(
          color: Colors.white,
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _navigateToQuickTransfer(
    BuildContext context,
    RecentRecipient rec,
  ) {
    setState(() {
      recipient = rec;
      _memberInfo = null;
    });

    _handleContinue();
  }
}
