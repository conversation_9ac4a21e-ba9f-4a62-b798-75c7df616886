import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/mini_apps/domain/entities/create_order_miniapp_entity.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';

import 'package:flutter/foundation.dart';

class CreateOrderMiniappModel extends CreateOrderMiniappEntity {
  const CreateOrderMiniappModel({
    required super.statusCode,
    required super.success,
    required super.data,
  });

  factory CreateOrderMiniappModel.fromJson(Map<String, dynamic> json) {
    /// TODO
    // final ç = json['merchantId'] as List?;

    return CreateOrderMiniappModel(
      statusCode: AppMapper.safeInt(json['statusCode']),
      success: AppMapper.safeBool(json['success']),
      data: CreateOrderMiniappDataModel.fromJson(
        AppMapper.safeMap(json['data']),
      ),
    );
  }
}

class CreateOrderMiniappDataModel extends CreateOrderMiniappDataEntity {
  const CreateOrder<PERSON>iniappDataModel({
    super.senderId,
    super.senderName,
    super.senderPhone,
    super.transactionOwner,
    super.transactionType,
    super.merchantId,
    super.merchantType,
    super.billAmount,
    super.originalCurrency,
    super.serviceCharge,
    super.vat,
    super.paymentDetails,
    super.billRefNo,
    super.billReason,
    super.authorizationType,
    super.status,
    super.createdAt,
    super.lastModified,
    super.typeId,
    super.customerName,
    super.senderEmail,
    super.sessionID,
    super.cardNumber,
    super.redirectURL,
    super.mpgsReference,
    super.beneficiaryName,
    super.beneficiaryId,
    super.beneficiaryPhone,
    super.beneficiaryAccountNo,
    super.beneficiaryEmail,
    super.bankName,
    super.bankCode,
    super.bankID,
  });

  factory CreateOrderMiniappDataModel.fromJson(Map<String, dynamic> json) {
    /// TODO
    // final ç = json['merchantId'] as List?;

    return CreateOrderMiniappDataModel(
      senderId: AppMapper.safeString(json['senderId']),
      senderName: AppMapper.safeString(json['senderName']),
      senderPhone: AppMapper.safeString(json['senderPhone']),
      transactionOwner: AppMapper.safeString(json['transactionOwner']),
      transactionType: AppMapper.safeString(json['transactionType']),
      merchantId: AppMapper.safeString(json['merchantId']),
      merchantType: AppMapper.safeString(json['merchantType']),
      billAmount: AppMapper.safeFormattedNumberWithDecimal(json['billAmount']),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      serviceCharge:
      AppMapper.safeFormattedNumberWithDecimal(json['serviceCharge']),
      vat: AppMapper.safeString(json['vat']),
      paymentDetails:
      PaymentDetailsModel.fromJson(AppMapper.safeMap(json['data'])),

      // statusCode:  AppMapper.safeString(json['statusCode']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      billReason: AppMapper.safeString(json['billReason']),
      authorizationType: AppMapper.safeString(json['authorization_type']),
      status: AppMapper.safeString(json['status']),
      createdAt: AppMapper.safeFormattedDate(json['createdAt']),
      lastModified: AppMapper.safeFormattedDate(json['lastModified']),
      typeId: AppMapper.safeString(json['typeId']),
      customerName: AppMapper.safeString(json['customerName']),
      senderEmail: AppMapper.safeString(json['senderEmail']),
      sessionID: AppMapper.safeString(json['sessionID']),
      cardNumber: AppMapper.safeString(json['cardNumber']),
      redirectURL: AppMapper.safeString(json['redirectURL']),
      mpgsReference: AppMapper.safeString(json['mpgsReference']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      beneficiaryPhone: AppMapper.safeString(json['beneficiaryPhone']),
      beneficiaryAccountNo: AppMapper.safeString(json['beneficiaryAccountNo']),
      beneficiaryEmail: AppMapper.safeString(json['beneficiaryEmail']),
      bankName: AppMapper.safeString(json['bankName']),
    );
  }
}

class PaymentDetailsModel extends PaymentDetailsEntity {
  const PaymentDetailsModel({
    super.violationReference,
    super.ticketNo,
    super.driverFullName,
    super.issueDate,
    super.request,
  });

  factory PaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    /// TODO
    // final ç = json['merchantId'] as List?;

    return PaymentDetailsModel(
      violationReference: AppMapper.safeString(json['violationReference']),
      ticketNo: AppMapper.safeString(json['ticketNo']),
      driverFullName: AppMapper.safeString(json['driverFullName']),
      issueDate: AppMapper.safeString(json['issueDate']),
      request: RequestModel.fromJson(AppMapper.safeMap(json['request'])),
    );
  }
}

class RequestModel extends RequestEntity {
  const RequestModel({
    super.scope,
  });

  factory RequestModel.fromJson(Map<String, dynamic> json) {
    /// TODO
    // final ç = json['merchantId'] as List?;

    return RequestModel(
      scope: AppMapper.safeString(json['scope']),
    );
  }
}
