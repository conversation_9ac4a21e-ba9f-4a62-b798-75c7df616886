import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/features/my_connect/presentation/views/my_connect_detail_page.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/connect_request_card.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/empty_connection_card.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/list_of_connect_header.dart';
import 'package:cbrs/features/transactions/presentation/views/quick%20wallet%20transfer/quick_pay_recipent_card.dart';
import 'package:flutter/material.dart';

class Recipient {
  Recipient({required this.name, required this.email, required this.avatar});
  final String name;
  final String email;
  final String avatar;
}

  final List<Recipient> recipients = [
    Recipient(name: '<PERSON><PERSON>', email: '<EMAIL>', avatar: '...'),
    Recipient(name: '<PERSON><PERSON><PERSON><PERSON>', email: '<EMAIL>', avatar: '...'),
    Recipient(name: '<PERSON><PERSON>', email: '<EMAIL>', avatar: '...'),
    Recipient(name: 'Biniam', email: '<EMAIL>', avatar: '...'),
    Recipient(name: 'Bini', email: '<EMAIL>', avatar: '...'),
    Recipient(name: 'Mehreteu', email: '<EMAIL>', avatar: '...'),
    Recipient(name: 'Amanny', email: '<EMAIL>', avatar: '...'),
    Recipient(name: 'Misgana', email: '<EMAIL>', avatar: '...'),
    Recipient(name: 'Kal', email: '<EMAIL>', avatar: '...'),
    Recipient(name: 'KalKidan', email: '<EMAIL>', avatar: '...'),
    Recipient(name: 'Bessufekad', email: '<EMAIL>', avatar: '...'),
  ];

class MyConnectPage extends StatelessWidget {
  MyConnectPage({super.key}) {
    _groupRecipients();
  }



  final Map<String, List<Recipient>> groupedRecipients = {};
  List<String> sortedKeys = [];

  void _groupRecipients() {
    for (final r in recipients) {
      final letter = r.name[0].toUpperCase();
      groupedRecipients.putIfAbsent(letter, () => []).add(r);
    }
    sortedKeys = groupedRecipients.keys.toList()..sort();
  }

  final bool isEmptyConnect = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Connects'),
      ),
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          child: Column(
            children: [
              const ConnectRequestCard(requestCount: 12),
              if (isEmptyConnect)
                const Expanded(child: EmptyConnectionCard())
              else ...[
                const SizedBox(height: 16),
                const ListOfConnectHeader(),
                const SizedBox(height: 12),
                const CustomTextInput(
                  hintText: 'Search Connect',
                  inputLabel: '',
                  prefixIcon: Icon(Icons.search, color: Color(0xFF7C7C7C)),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: sortedKeys.length,
                    itemBuilder: (context, index) {
                      final key = sortedKeys[index];
                      final items = groupedRecipients[key]!;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 12,
                            ),
                            child: CustomBuildText(
                              text: key,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF7C7C7C),
                            ),
                          ),
                          ...items.map((recipient) {
                            return InkWell(
                              onTap: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const MyConnectDetailPage(),
                                  ),
                                );
                              },
                              child: Container(
                                margin: const EdgeInsets.symmetric(
                                  vertical: 6,
                                  horizontal: 6,
                                ),
                                child: QuickPayRecipentCard(
                                  recipientName: recipient.name,
                                  recipientEmail: recipient.email,
                                  recipientAvatar: recipient.avatar,
                                ),
                              ),
                            );
                          }),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
