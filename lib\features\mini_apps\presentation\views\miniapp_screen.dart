import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_webview.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/features/mini_apps/application/bloc/miniapp_bloc.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:go_router/go_router.dart';
import 'package:cbrs/core/services/routes/route_name.dart';

class MiniAppScreen extends StatefulWidget {
  const MiniAppScreen({super.key});

  @override
  State<MiniAppScreen> createState() => _MiniappScreenState();
}

class _MiniappScreenState extends State<MiniAppScreen> {
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  bool _hasMoreData = true;
  List<MiniappDataEntity> _miniapps = [];

  @override
  void initState() {
    super.initState();
    _loadMiniApps();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadMiniApps() {
    context.read<MiniappBloc>().add(
          GettingMiniappEvent(
            page: _currentPage,
            perPage: 10,
            stage: 'UAT',
          ),
        );
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
            _scrollController.position.maxScrollExtent &&
        _hasMoreData) {
      _currentPage++;
      _loadMiniApps();
    }
  }

  List<MiniappDataEntity> _filterMiniApps(List<MiniappDataEntity> apps) {
    return apps.where((app) => app.merchantType == "MiniApp").toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Mini Apps',
          style: GoogleFonts.outfit(fontSize: 18.sp),
        ),
        titleSpacing: 22.w,
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const CustomPageHeader(
                pageTitle: 'MiniApp Payment',
                description:
                    'Select a miniapp from the list and pay your bill easily',
              ),
              SizedBox(height: 16.h),
              BlocConsumer<MiniappBloc, MiniappState>(
                listener: (context, state) {
                  if (state is GetLoadedMiniappsState) {
                    final filteredApps =
                        _filterMiniApps(state.miniapp.miniappData);
                    setState(() {
                      if (_currentPage == 1) {
                        _miniapps = filteredApps;
                      } else {
                        _miniapps.addAll(filteredApps);
                      }
                      _hasMoreData =
                          state.miniapp.miniappPaginate?.currentPage !=
                              state.miniapp.miniappPaginate?.totalPages;
                    });
                  }
                },
                builder: (context, state) {
                  if (state is MiniappLoadingState && _currentPage == 1) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (state is MiniappErrorState && _miniapps.isEmpty) {
                    return Center(
                      child: Text(state.message ?? 'An error occurred'),
                    );
                  }

                  return Column(
                    children: [
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          childAspectRatio: 122.w / 128.h,
                          crossAxisSpacing: 12.w,
                          mainAxisSpacing: 12.h,
                        ),
                        itemCount: _miniapps.length +
                            (state is MiniappLoadingState ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _miniapps.length) {
                            return Container(
                              padding: EdgeInsets.all(16.h),
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            );
                          }
                          return _buildUtilityItem(context, _miniapps[index]);
                        },
                      ),
                      if (state is MiniappLoadingState && _currentPage > 1)
                        Padding(
                          padding: EdgeInsets.all(16.h),
                          child: const CircularProgressIndicator(),
                        ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUtilityItem(BuildContext context, MiniappDataEntity miniapp) {
    return GestureDetector(
      onTap: () {
        if (miniapp.url.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${miniapp.miniAppName} is coming soon!')),
          );
          return;
        }

        context.pushNamed(
          AppRouteName.miniappWebView,
          extra: {
            'url': miniapp.url,
            'appName': miniapp.miniAppName,
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 24,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 56.w,
              height: 56.h,
              padding: EdgeInsets.all(8.w),
              child: Image.network(
                miniapp.miniAppIcon,
                width: 44.w,
                height: 44.h,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.error_outline,
                    size: 44.w,
                    color: Colors.red,
                  );
                },
              ),
            ),
            SizedBox(height: 8.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Text(
                miniapp.miniAppName,
                textAlign: TextAlign.center,
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1A1A1A),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/*
  @override
  Widget build(BuildContext context) {
    return
     BlocProvider(
      create: (context) => sl<UtilityBloc>()
        ..add(
          const LoadUtilitiesEvent(
            page: 1,
            perPage: 10,
            stage: 'UAT',
          ),
        ),
      child:

       Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text(
            'Utility Payment',
          ),
        ),
        body:

        SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CustomPageHeader(
                  pageTitle: 'Utility Payment',
                  description:
                      'Select a utility from the list and pay your bill easily',
                ),
                SizedBox(height: 16.h),
                BlocBuilder<UtilityBloc, UtilityState>(
                  builder: (context, state) {
                    if (state.status == UtilityStatus.loading) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (state.status == UtilityStatus.error) {
                      return Center(
                          child:
                              Text(state.errorMessage ?? 'An error occurred'));
                    }

                    if (state.status == UtilityStatus.loaded) {
                      return _buildUtilityGrid(context, state.utilities);
                    }

                    return const SizedBox();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }




  Widget _buildUtilityGrid(BuildContext context, List<UtilityEntity> utilities) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 122.w / 128.h,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: utilities.length,
      itemBuilder: (context, index) {
        return _buildUtilityItem(context, utilities[index]);
      },
    );
  }

  Widget _buildUtilityItem(BuildContext context, UtilityEntity utility) {
    return GestureDetector(
      onTap: () {
        if (utility.url.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${utility.miniAppName} is coming soon!')),
          );
          return;
        }

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MiniAppsWebView(
              url: utility.url,
              appName: utility.miniAppName,
            ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 24,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 56.w,
              height: 56.h,
              padding: EdgeInsets.all(8.w),
              child: Image.network(
                utility.miniAppIcon,
                width: 44.w,
                height: 44.h,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.error_outline,
                    size: 44.w,
                    color: Colors.red,
                  );
                },
              ),
            ),
            SizedBox(height: 8.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Text(
                utility.miniAppName,
                textAlign: TextAlign.center,
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1A1A1A),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
*/
