import 'package:intl/intl.dart';

String formatDateInMonth(String dateString, {bool isAbbreviate = false}) {
  try {
    DateTime parsedDate = DateTime.parse(dateString);
    return isAbbreviate
        ? DateFormat('MMM d, yyyy').format(parsedDate)
        : DateFormat('MMMM d, yyyy').format(parsedDate);
  } catch (e) {
    return 'Invalid date';
  }
}

String formatFriendlyDate(int leftDates) {
  String stringDate;

  if (leftDates > 1) {
    stringDate = '$leftDates Days ';
  } else if (leftDates == 1) {
    stringDate = '$leftDates Day';
  } else {
    stringDate = 'Today';
  }

  return stringDate;
}

String formatDateWithTime(dynamic dateInput) {
  try {
    DateTime parsedDate;
    if (dateInput is DateTime) {
      parsedDate = dateInput;
    } else if (dateInput is String) {
      parsedDate = DateTime.parse(dateInput);
    } else {
      return 'Invalid date';
    }
    return DateFormat('MMM d,yyyy | hh:mm a').format(parsedDate);
  } catch (e) {
    return 'Invalid date';
  }
}

// Final Day
