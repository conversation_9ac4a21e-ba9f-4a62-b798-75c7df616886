import 'package:cbrs/features/utility/domain/entities/create_order_utility_entity.dart';
import 'package:equatable/equatable.dart';

class ConnectionRequestEntity extends Equatable {
  const ConnectionRequestEntity({
    required this.requester,
    required this.recipient,
    required this.status,
    required this.sentAt,
    this.connectedAt,
    this.declinedAt,
    required this.id,
    required this.createdAt,
    required this.updatedAt,
  });
  final RequesterEntity requester;
  final RecipientEntity recipient;
  final String status;
  final DateTime sentAt;
  final DateTime? connectedAt;
  final DateTime? declinedAt;
  final String id;
  final DateTime createdAt;
  final DateTime updatedAt;

  @override
  List<Object?> get props => [
        requester,
        recipient,
        status,
        sentAt,
        connectedAt,
        declinedAt,
        id,
        createdAt,
        updatedAt,
      ];
}

class RequesterEntity extends Equatable {
  const RequesterEntity(
      {this.id, this.firstName, this.lastName, this.phoneNumber});
  final String? id;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;

  @override
  List<Object?> get props => [id, firstName, lastName, phoneNumber];
}

class RecipientEntity extends Equatable {
  const RecipientEntity(
      {this.id, this.firstName, this.lastName, this.phoneNumber});
  final String? id;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;

  String get fullName => '$firstName $lastName';

  @override
  List<Object?> get props => [id, firstName, lastName, phoneNumber];
}
