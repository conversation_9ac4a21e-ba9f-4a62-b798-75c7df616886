import 'package:equatable/equatable.dart';

class ConnectionRequestEntity extends Equatable {
  const ConnectionRequestEntity({
    required this.requester,
    required this.recipient,
    required this.status,
    required this.sentAt,
    this.connectedAt,
    this.declinedAt,
    required this.id,
    required this.createdAt,
    required this.updatedAt,
  });
  final String requester;
  final String recipient;
  final String status;
  final DateTime sentAt;
  final DateTime? connectedAt;
  final DateTime? declinedAt;
  final String id;
  final DateTime createdAt;
  final DateTime updatedAt;

  @override
  List<Object?> get props => [
        requester,
        recipient,
        status,
        sentAt,
        connectedAt,
        declinedAt,
        id,
        createdAt,
        updatedAt,
      ];
}
