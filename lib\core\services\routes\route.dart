import 'dart:io';

import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/pages/error_screen.dart';
import 'package:cbrs/core/common/presentation/pages/coming_soon_page.dart';
import 'package:cbrs/core/common/presentation/pages/connection_lost_screen.dart';
import 'package:cbrs/core/common/presentation/pages/main_page.dart';
import 'package:cbrs/core/enum/shared_item_type.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/navigation/navigation_service.dart';
import 'package:cbrs/core/services/storage/hive_box_manager.dart';
import 'package:cbrs/features/add_money/application/bloc/add_money_bloc.dart';
// Add Money Imports
import 'package:cbrs/features/add_money/presentation/views/add_money_screen.dart';
import 'package:cbrs/features/agent_locator/presentation/pages/agent_coming_soon_page.dart';
import 'package:cbrs/features/agent_locator/presentation/pages/agent_locator_page.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
// Auth Imports
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/presentation/views/create_pin_screen.dart';
import 'package:cbrs/features/auth/presentation/views/email_verification_screen.dart';
import 'package:cbrs/features/auth/presentation/views/forgot_password_screen.dart';
import 'package:cbrs/features/auth/presentation/views/otp_verification_screen.dart';
import 'package:cbrs/features/auth/presentation/views/sign_in_screen.dart';
import 'package:cbrs/features/auth/presentation/views/sign_up_screen.dart';
import 'package:cbrs/features/auth/presentation/views/token_device_login_screen.dart';
import 'package:cbrs/features/balance_check/application/bloc/balance_check_bloc.dart';
import 'package:cbrs/features/balance_check/application/bloc/recent_linked_transaction_bloc.dart';
import 'package:cbrs/features/balance_check/presentation/views/balance_check_page.dart';
import 'package:cbrs/features/balance_check/presentation/views/balance_history_page.dart';
import 'package:cbrs/features/balance_check/presentation/views/transaction_detail_page.dart';

import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_bloc.dart';
import 'package:cbrs/features/cash_in_out/domain/entities/agent.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_in_add_amount_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_in_out_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_out_agent_add_amount_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_out_agent_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_out_confirmation_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_out_menu_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_out_otp_confirmation_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_out_qr_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_out_success_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/cash_out_voucher_add_amount_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/qr_success_page.dart';
import 'package:cbrs/features/cash_in_out/presentation/views/voucher_success_page.dart';
import 'package:cbrs/features/change_to_birr/application/bloc/change_to_birr_bloc.dart';
import 'package:cbrs/features/change_to_birr/presentation/views/change_to_birr_add_amount.dart';

import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_bloc.dart';
import 'package:cbrs/features/gift_packages/domain/entities/banner_item.dart';
import 'package:cbrs/features/gift_packages/domain/entities/package_item.dart';
import 'package:cbrs/features/gift_packages/presentation/views/confirm_purchase_screen.dart';
import 'package:cbrs/features/gift_packages/presentation/views/gift_packages_screen.dart';
import 'package:cbrs/features/gift_packages/presentation/views/merchant_packages_screen.dart';
import 'package:cbrs/features/gift_packages/presentation/views/package_detail_screen.dart';
import 'package:cbrs/features/gift_packages/presentation/views/package_search_screen.dart';
import 'package:cbrs/features/gift_packages/presentation/views/recipient_details_screen.dart';
import 'package:cbrs/features/guest/application/bloc/guest_bloc.dart';
import 'package:cbrs/features/guest/domain/entities/guest_transfer_status.dart';
import 'package:cbrs/features/guest/presentation/views/guest_add_amount_screen.dart';
import 'package:cbrs/features/guest/presentation/views/guest_confirmation_screen.dart';
import 'package:cbrs/features/guest/presentation/views/guest_recipient_account_number_screen.dart';
import 'package:cbrs/features/guest/presentation/views/guest_send_money_screen.dart';
import 'package:cbrs/features/guest/presentation/views/guest_transfer_failure_screen.dart';
import 'package:cbrs/features/guest/presentation/views/guest_transfer_success_screen.dart';
import 'package:cbrs/features/guest/presentation/views/home/<USER>';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/home/<USER>/widgets/quick_wallet/quick_wallet_recipients_bottom_sheet.dart';
// identity verification imports
import 'package:cbrs/features/identity_verification/presentation/bloc/identity_verification_bloc.dart';
import 'package:cbrs/features/identity_verification/presentation/views/identity_verification_page.dart';
import 'package:cbrs/features/identity_verification/presentation/views/face_scan_camera_page.dart';
import 'package:cbrs/features/identity_verification/presentation/views/document_scan_page.dart';
import 'package:cbrs/features/in_app_update/presentation/views/in_app_update.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/load_wallet/presentation/view/Load_to_wallet_web_view.dart';
import 'package:cbrs/features/load_wallet/presentation/view/failure_page.dart';
import 'package:cbrs/features/load_wallet/presentation/view/load_to_wallet_page.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_bloc.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_banks_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_categories_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_payment_info_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_terms_cubit.dart';
import 'package:cbrs/features/loans/presentation/views/document_for_loan_page.dart';
import 'package:cbrs/features/loans/presentation/views/loan_bank_info_page.dart';
import 'package:cbrs/features/loans/presentation/views/loan_item_detail_page.dart';
import 'package:cbrs/features/loans/presentation/views/loan_terms_and_condition_page.dart';
import 'package:cbrs/features/loans/presentation/views/loans_application_page.dart';
import 'package:cbrs/features/loans/presentation/views/virtual_tour_webview.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_payment_bloc.dart';
import 'package:cbrs/features/merchant_payment/presentation/views/merchant_payment_add_amount_page.dart';
import 'package:cbrs/features/merchant_payment/presentation/views/merchant_payment_by_qr.dart';
import 'package:cbrs/features/merchant_payment/presentation/views/merchant_payment_by_qr_add_amount.dart';
import 'package:cbrs/features/merchant_payment/presentation/views/merchant_payment_confirmation_page.dart';
import 'package:cbrs/features/merchant_payment/presentation/views/merchant_payment_otp_confirmation.dart';
import 'package:cbrs/features/merchant_payment/presentation/views/merchant_payment_screen.dart';
import 'package:cbrs/features/merchant_payment/presentation/views/merchant_payment_success_page.dart';
import 'package:cbrs/features/mini_apps/application/bloc/miniapp_bloc.dart';
import 'package:cbrs/features/mini_apps/domain/entities/create_order_miniapp_entity.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp_success_entity.dart';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_confirm_page.dart';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_screen.dart';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_success_screen.dart';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_webview.dart';
import 'package:cbrs/features/mini_statements/applications/mini_statement_bloc.dart';
import 'package:cbrs/features/mini_statements/presentations/views/mini_statements_menu_screen.dart';
import 'package:cbrs/features/money_request/domain/entities/member_lookup_entity.dart';
import 'package:cbrs/features/money_request/domain/entities/my_requests_list_entity.dart';
import 'package:cbrs/features/money_request/domain/usecases/send_money_request_usecase.dart';
import 'package:cbrs/features/money_request/presentation/bloc/money_request_detail/money_request_detail_bloc.dart';
import 'package:cbrs/features/money_request/presentation/bloc/money_request_list/money_request_list_bloc.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_money_request_add_money/send_money_request_add_money_bloc.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_money_request_member_lookup/send_money_request_member_lookup_bloc.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_request_money_confirm_request/send_request_money_confirm_request_bloc.dart';
import 'package:cbrs/features/money_request/presentation/views/money_request_check_amount_screen.dart';
import 'package:cbrs/features/money_request/presentation/views/money_request_confirm_request_screen.dart';
import 'package:cbrs/features/money_request/presentation/views/money_request_detail_screen.dart';
import 'package:cbrs/features/money_request/presentation/views/money_request_menu_screen.dart';
import 'package:cbrs/features/money_request/presentation/views/money_requested_list_screen.dart';
import 'package:cbrs/features/money_request/presentation/views/my_money_request_list_screen.dart';
import 'package:cbrs/features/money_request/presentation/views/send_money_request_add_money_screen.dart';
import 'package:cbrs/features/money_request/presentation/views/send_money_request_member_lookup_screen.dart';
import 'package:cbrs/features/money_request/presentation/views/send_money_request_success_screen.dart';

import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_bloc.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/upfront_payment_bloc.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:cbrs/features/my_loan/domain/entity/payment_history.dart';
import 'package:cbrs/features/my_loan/domain/entity/success_payment.dart';
import 'package:cbrs/features/my_loan/presentation/views/screen_my_loan_detail_page.dart';
import 'package:cbrs/features/my_loan/presentation/views/screen_payment_detail.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_bloc.dart';
import 'package:cbrs/features/notifications/presentation/views/notification_details_screen.dart';
import 'package:cbrs/features/notifications/presentation/views/notifications_screen.dart';
import 'package:cbrs/features/notifications/presentation/widgets/notification_group.dart';
import 'package:cbrs/features/onboarding/presentation/cubit/on_boarding_cubit.dart';
import 'package:cbrs/features/onboarding/presentation/views/onboarding_screen.dart';
import 'package:cbrs/features/pay_by_qr/application/bloc/parse_qr_bloc.dart';
import 'package:cbrs/features/pay_by_qr/presentation/views/qr_options_page.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
import 'package:cbrs/features/profile/presentation/views/add_email_screen.dart';
import 'package:cbrs/features/profile/presentation/views/add_phone_screen.dart';
import 'package:cbrs/features/profile/presentation/views/change_pin_screen.dart';
import 'package:cbrs/features/profile/presentation/views/customer_support_screen.dart';
import 'package:cbrs/features/profile/presentation/views/faq_screen.dart';
import 'package:cbrs/features/profile/presentation/views/profile_info_screen.dart';
import 'package:cbrs/features/profile/presentation/views/profile_photo_detail.dart';
import 'package:cbrs/features/profile/presentation/views/verify_email_phone_screen.dart';
import 'package:cbrs/features/profile/presentation/widgets/privacy_and_policy.dart';
import 'package:cbrs/features/profile/presentation/widgets/terms_and%20_services.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/send_money/data/models/bank_transfer_response_model.dart';
import 'package:cbrs/features/send_money/data/models/check_transfer_rules_response.dart'
    as bank_transfer_rules;
import 'package:cbrs/features/send_money/domain/entities/bank_transfer_request.dart';
import 'package:cbrs/features/send_money/presentation/views/other_bank_add_amount_screen.dart';
import 'package:cbrs/features/send_money/presentation/views/recipient_account_screen.dart';
import 'package:cbrs/features/send_money/presentation/views/send_money_screen.dart';
import 'package:cbrs/features/splash_screen/presentation/splash_loading_screen.dart';
import 'package:cbrs/features/top_up/applications/top_up_bloc.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_providers_entity.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_success_response_entities.dart';
import 'package:cbrs/features/top_up/presentations/views/top_up_add_amoun_screen.dart';
import 'package:cbrs/features/top_up/presentations/views/top_up_contact_screen.dart';
import 'package:cbrs/features/top_up/presentations/views/top_up_menu_screen.dart';
import 'package:cbrs/features/top_up/presentations/views/top_up_success_screen.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_details_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:cbrs/features/transactions/presentation/views/transaction_details_screen.dart';
import 'package:cbrs/features/transactions/presentation/views/transaction_validate_screen.dart';
import 'package:cbrs/features/transactions/presentation/views/transactions_screen.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/check_transfer_rules_response.dart'
    as wallet_transfer_rules;
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/member_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/presentation/views/wallet_transfer_add_amount.dart';
import 'package:cbrs/features/transfer_to_wallet/presentation/views/wallet_transfer_page.dart';
import 'package:cbrs/features/utility/application/bloc/utility_bloc.dart';
import 'package:cbrs/features/utility/domain/entities/utility_success_entity.dart';
import 'package:cbrs/features/utility/presentation/views/utility_screen.dart';
import 'package:cbrs/features/utility/presentation/views/utility_success_screen.dart';
import 'package:cbrs/features/utility/presentation/views/utility_webview.dart';
import 'package:cbrs/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'auth_routes.dart';
part 'loan_routes.dart';
part 'protected_routes.dart';
part 'route.main.dart';
part 'transfer_routes.dart';
