import 'package:cbrs/features/home/<USER>/widgets/home_action_icons.dart';
import 'package:cbrs/features/home/<USER>/widgets/home_section_headers.dart';
import 'package:cbrs/features/mini_statements/presentations/views/mini_statements_menu_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';

class USDPaymentOptions extends StatelessWidget {
  const USDPaymentOptions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 16.w, right: 16.w),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              HomeSectionHeaders(
                title: 'Payments',
                description: 'Make payments and manage your cash easily.',
              ),
            ],
          ),
        ),
        SizedBox(height: 8.h),
        Padding(
          padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildPaymentOption(
                context: context,
                image: MediaRes.cashInIcon,
                label: 'Cash\nIn',
                onTap: () {
                  context.pushNamed(
                    AppRouteName.cashIn,
                    extra: {'isUSD': true},
                  );
                },
              ),
              _buildPaymentOption(
                context: context,
                image: MediaRes.miniStatement,
                label: 'Mini\nStatement',
                onTap: () {
                  context.pushNamed(AppRouteName.miniStatements);
                },
              ),
              _buildPaymentOption(
                context: context,
                image: MediaRes.moneyRequest,
                label: 'Money\nRequest',
                onTap: () {
                  context.pushNamed(
                    AppRouteName.moneyRequestMenuScreen,
                  );
                },
              ),
              _buildPaymentOption(
                context: context,
                image: MediaRes.agentLocator,
                label: 'Agent\nLocator',
                onTap: () {
                  context.pushNamed(AppRouteName.agentLocatorComing);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentOption({
    required String image,
    required BuildContext context,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          HomeActionIcons(imageIcon: image),
          SizedBox(height: 6.h),
          Text(
            label,
            textAlign: TextAlign.center,
            style: GoogleFonts.outfit(
              fontSize: 13.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
