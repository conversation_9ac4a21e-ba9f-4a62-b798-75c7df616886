import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class IdentityVerificationCard extends StatelessWidget {
  const IdentityVerificationCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      child: Container(
        height: 200.h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(MediaRes.identityVerificationCard),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              left: 32.w,
              top: 32.h,
              child: SizedBox(
                width: 180.w,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Please verify your account',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    // Description text
                    Text(
                      'To ensure the security of your account and protect against fraud, we require you to complete our identity verification process.',
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                      softWrap: true,
                      maxLines: 3,
                    ),
                    SizedBox(height: 12.h),
                    // Button
                    SizedBox(
                      height: 50.h,
                      width: 130.w,
                      child: ElevatedButton(
                        onPressed: () {
                          context.pushNamed(AppRouteName.identityVerification);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          elevation: 0, // No shadow
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40.r),
                          ),
                          padding: EdgeInsets.symmetric(vertical: 10.h),
                        ),
                        child: Text(
                          'Get Verified',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
