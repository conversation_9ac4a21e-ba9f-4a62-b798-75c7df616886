import 'package:cbrs/core/utils/price_formatter.dart';
import 'package:cbrs/features/orders/application/blocs/order_bloc.dart';
import 'package:cbrs/features/orders/application/blocs/order_state.dart';
import 'package:cbrs/features/orders/presentation/widgets/received_order_details_shimmer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:go_router/go_router.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';

class ReceivedOrderDetailsScreen extends StatelessWidget {
  const ReceivedOrderDetailsScreen({super.key, required this.orderId});
  final String orderId;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        if (state is OrderDetailLoading) {
          return const ReceivedOrderDetailsShimmer();
        }

        if (state is OrderDetailLoaded) {
          final orderDetail = state.orderDetail;
          final giftPackage = orderDetail.giftPackage;

          return Scaffold(
            backgroundColor: Colors.grey[50],
            appBar: AppBar(
              title: const Text(
                'Order Details',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
              ),
            ),
            body: SafeArea(
              child: Column(
                children: [
                  const SizedBox(height: 12),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Gift Information',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Gift Card
// Gift Card
                            Container(
                              padding: EdgeInsets.all(
                                  MediaQuery.of(context).size.width *
                                      0.02), // 2% padding
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  final maxWidth = constraints.maxWidth;
                                  final imageSize = (maxWidth * 0.25).clamp(
                                      80.0,
                                      100.0); // 25% of width, clamped between 80-100
                                  final fontSize = (maxWidth * 0.04).clamp(
                                      14.0, 18.0); // Responsive font size

                                  return Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        padding:
                                            EdgeInsets.all(maxWidth * 0.01),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[50],
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: giftPackage?.featureImage != null
                                            ? ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: Image.network(
                                                  giftPackage!.featureImage,
                                                  width: imageSize,
                                                  height: imageSize,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error,
                                                      stackTrace) {
                                                    return Image.asset(
                                                      'assets/vectors/lamb.png',
                                                      width: imageSize,
                                                      height: imageSize,
                                                      fit: BoxFit.cover,
                                                    );
                                                  },
                                                  loadingBuilder: (context,
                                                      child, loadingProgress) {
                                                    if (loadingProgress == null)
                                                      return child;
                                                    return Shimmer.fromColors(
                                                      baseColor:
                                                          Colors.grey[300]!,
                                                      highlightColor:
                                                          Colors.grey[100]!,
                                                      child: Container(
                                                        width: imageSize,
                                                        height: imageSize,
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              )
                                            : Image.asset(
                                                'assets/vectors/lamb.png',
                                                width: imageSize,
                                                height: imageSize,
                                                fit: BoxFit.cover,
                                              ),
                                      ),
                                      SizedBox(
                                          width: maxWidth * 0.04), // 4% spacing
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              giftPackage!.name,
                                              style: TextStyle(
                                                fontSize: fontSize,
                                                fontWeight: FontWeight.w700,
                                              ),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            SizedBox(
                                                height: maxWidth *
                                                    0.01), // 1% spacing
                                            Text(
                                              giftPackage.category.name,
                                              style: TextStyle(
                                                color: Colors.grey,
                                                fontSize: fontSize *
                                                    0.8, // 80% of main font size
                                                fontWeight: FontWeight.w400,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            SizedBox(
                                                height: maxWidth *
                                                    0.03), // 3% spacing
                                            Wrap(
                                              crossAxisAlignment:
                                                  WrapCrossAlignment.center,
                                              children: [
                                                Text(
                                                  'Sent at : ',
                                                  style: TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: fontSize * 0.8,
                                                  ),
                                                ),
                                                Text(
                                                  DateFormat('MMM dd, yyyy')
                                                      .format(orderDetail
                                                          .createdAt),
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                    fontSize: fontSize * 0.8,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),

                            const SizedBox(height: 22),
                            // Pick up Address
                            const Text(
                              'Pick up Address',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const SizedBox(height: 10),
                            Container(
                              padding: EdgeInsets.all(
                                  MediaQuery.of(context).size.width * 0.02),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  final maxWidth = constraints.maxWidth;
                                  final logoSize = (maxWidth * 0.2).clamp(
                                      60.0, 80.0); // 20% of width, clamped
                                  final fontSize = (maxWidth * 0.04).clamp(
                                      14.0, 18.0); // Responsive font size

                                  return Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // ClipRRect(
                                      //   borderRadius: BorderRadius.circular(8),
                                      //   child: Image.network(
                                      //     giftPackage!.merchant.logo,
                                      //     width: logoSize,
                                      //     height: logoSize,
                                      //     fit: BoxFit.contain,
                                      //     errorBuilder:
                                      //         (context, error, stackTrace) {
                                      //       return Container(
                                      //         width: logoSize,
                                      //         height: logoSize,
                                      //         color: Colors.grey[200],
                                      //         child: Icon(Icons.error,
                                      //             size: logoSize * 0.5),
                                      //       );
                                      //     },
                                      //   ),
                                      // ),
                                      SizedBox(
                                          width: maxWidth * 0.03), // 3% spacing
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              giftPackage!.merchant.name,
                                              style: TextStyle(
                                                fontSize: fontSize *
                                                    1.2, // 20% larger than base fontSize
                                                fontWeight: FontWeight.w700,
                                              ),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  giftPackage
                                                      .merchant.phoneNumber,
                                                  style: TextStyle(
                                                    color: Colors.grey[600],
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: fontSize *
                                                        0.9, // 90% of base fontSize
                                                  ),
                                                ),
                                                Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal: maxWidth * 0.03,
                                                    vertical: maxWidth * 0.015,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        const Color(0xFFE8F5F1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                  ),
                                                  child: Text(
                                                    'Near By',
                                                    style: TextStyle(
                                                      color: LightModeTheme()
                                                          .primaryColorUSD,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: fontSize *
                                                          0.8, // 80% of base fontSize
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                                height: maxWidth *
                                                    0.02), // 2% spacing
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.info_outline,
                                                  color: Colors.orange[400],
                                                  size:
                                                      fontSize, // Icon size matches base fontSize
                                                ),
                                                SizedBox(
                                                    width: maxWidth * 0.02),
                                                if (orderDetail.status ==
                                                    'UNREDEEMED')
                                                  Expanded(
                                                    child: Text(
                                                      'You can pick your gift from any ${giftPackage.merchant.name} branch',
                                                      style: TextStyle(
                                                        color:
                                                            Colors.orange[400],
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize: fontSize *
                                                            0.7, // 70% of base fontSize
                                                      ),
                                                      maxLines: 2,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),

                            const SizedBox(height: 22),

                            // Sender Detail
                            const Text(
                              'Order Detail',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const SizedBox(height: 3),

                            _buildDetailRow(
                                'Order Code:',
                                orderDetail.orderId ??
                                    orderDetail.billRefNo ??
                                    'N/A'),
                            _buildDetailRow(
                                'Sender name:', orderDetail.senderName),
                            _buildDetailRow(
                                'Recipient name:', orderDetail.beneficiaryName),
                            _buildDetailRow('Recipient Phone:',
                                orderDetail.beneficiaryPhone ?? 'N/A'),
                            _buildDetailRow(
                                'Package name:', giftPackage?.name ?? 'N/A'),
                            if (giftPackage?.merchant != null)
                              _buildDetailRow(
                                  'Merchant', giftPackage!.merchant.name),
                            if (orderDetail.paidAmount != null)
                              _buildDetailRow(
                                  'Amount:',
                                  PriceFormatter.formatPrice(
                                      (orderDetail.paidAmount ??
                                              orderDetail.billAmount)
                                          .toStringAsFixed(2))),

                            const SizedBox(height: 16),

                            // Delivery Status
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Delivery Status:',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 16,
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: orderDetail.status == 'UNREDEEMED'
                                        ? const Color(0xFFFFF1C2)
                                        : LightModeTheme()
                                            .primaryColorUSD
                                            .withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    orderDetail.status == 'UNREDEEMED'
                                        ? 'Ready to Pickup'
                                        : orderDetail.status,
                                    style: TextStyle(
                                      color: orderDetail.status == 'UNREDEEMED'
                                          ? Color(0xFFDD9000)
                                          : LightModeTheme().primaryColorUSD,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Send Message Button
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: () {
                          context.pop();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: LightModeTheme().primaryColorUSD,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child: const Text(
                          'Back to Orders',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        if (state is OrderDetailError) {
          return Scaffold(
            body: Center(child: Text(state.message)),
          );
        }

        return const Scaffold(
          body: Center(child: CircularProgressIndicator()),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth = constraints.maxWidth;
        final fontSize =
            (maxWidth * 0.04).clamp(14.0, 16.0); // Responsive font size

        return Padding(
          padding: EdgeInsets.symmetric(
            vertical: maxWidth * 0.015, // 1.5% of width for vertical padding
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 4, // 40% of width
                child: Text(
                  label,
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: fontSize,
                    fontWeight: FontWeight.w400,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: maxWidth * 0.02), // 2% spacing
              Expanded(
                flex: 6, // 60% of width
                child: Text(
                  value,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: fontSize,
                  ),
                  textAlign: TextAlign.right,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
