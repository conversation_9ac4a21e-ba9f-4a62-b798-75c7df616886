import 'dart:async';
import 'dart:io';

import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:path_provider/path_provider.dart';
import 'package:cross_file/cross_file.dart';

class DocumentScanPage extends StatefulWidget {
  const DocumentScanPage({
    super.key,
    this.selfieImage,
  });

  final File? selfieImage;

  @override
  State<DocumentScanPage> createState() => _DocumentScanPageState();
}

class _DocumentScanPageState extends State<DocumentScanPage>
    with TickerProviderStateMixin {
  bool _isCapturing = false;
  bool _isScanningFront = true;
  bool _showPreview = false;
  bool _isTransitioning = false;
  File? _frontIdImage;
  File? _backIdImage;
  late AnimationController _transitionAnimationController;

  @override
  void initState() {
    super.initState();
    _transitionAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _transitionAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _showPreview ? Colors.white : Colors.black,
      body: SafeArea(
        child: _showPreview ? _buildPreviewView() : _buildCameraView(),
      ),
    );
  }

  Widget _buildCameraView() {
    return Column(
      children: [
        // Header with back button and title
        SafeArea(
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Text(
                  _isScanningFront ? 'Scan Front ID' : 'Scan Back ID',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                const SizedBox(width: 48), // Balance the back button
              ],
            ),
          ),
        ),
        SizedBox(height: 20.h),
        Expanded(
          child: Stack(
            children: [
              Positioned.fill(
                child: CameraAwesomeBuilder.awesome(
                  saveConfig: SaveConfig.photo(
                    pathBuilder: (sensors) async {
                      try {
                        final appDir = await getApplicationDocumentsDirectory();
                        final String dirPath = '${appDir.path}/document_photos';

                        // Ensure directory exists
                        final dir = Directory(dirPath);
                        if (!await dir.exists()) {
                          await dir.create(recursive: true);
                        }

                        final String filePath =
                            '$dirPath/document_${DateTime.now().millisecondsSinceEpoch}.jpg';
                        debugPrint('Document photo path: $filePath');
                        return SingleCaptureRequest(filePath, sensors.first);
                      } catch (e) {
                        debugPrint('Error creating file path: $e');
                        // Simple fallback
                        final appDir = await getApplicationDocumentsDirectory();
                        final String filePath =
                            '${appDir.path}/document_${DateTime.now().millisecondsSinceEpoch}.jpg';
                        return SingleCaptureRequest(filePath, sensors.first);
                      }
                    },
                    mirrorFrontCamera: false,
                  ),
                  sensorConfig: SensorConfig.single(
                    sensor: Sensor.position(SensorPosition.back),
                    flashMode: FlashMode.auto,
                    aspectRatio: CameraAspectRatios.ratio_4_3,
                  ),
                  enablePhysicalButton: false,
                  defaultFilter: AwesomeFilter.None,
                  availableFilters: const [],
                  onMediaCaptureEvent: (mediaCapture) {
                    // Set capturing state
                    if (mounted) {
                      setState(() => _isCapturing = true);
                    }

                    try {
                      mediaCapture.captureRequest.when(
                        single: (single) async {
                          try {
                            final file = await single.file;
                            if (file != null && mounted) {
                              final capturedFile = File(file.path);
                              // Verify file exists before setting state
                              if (await capturedFile.exists()) {
                                setState(() {
                                  if (_isScanningFront) {
                                    _frontIdImage = capturedFile;
                                    _isTransitioning = true;
                                  } else {
                                    _backIdImage = capturedFile;
                                    _showPreview = true;
                                  }
                                  _isCapturing = false;
                                });

                                if (_isScanningFront && mounted) {
                                  unawaited(
                                    _transitionAnimationController
                                        .forward()
                                        .then((_) {
                                      if (mounted) {
                                        setState(() {
                                          _isScanningFront = false;
                                          _isTransitioning = false;
                                        });
                                        _transitionAnimationController.reset();
                                      }
                                    }),
                                  );
                                }
                              } else {
                                debugPrint(
                                    'Captured file does not exist: ${file.path}');
                                if (mounted) {
                                  setState(() => _isCapturing = false);
                                }
                              }
                            } else if (mounted) {
                              setState(() => _isCapturing = false);
                              _showErrorMessage('Failed to capture photo');
                            }
                          } catch (e) {
                            debugPrint('Error processing single capture: $e');
                            if (mounted) {
                              setState(() => _isCapturing = false);
                              _showErrorMessage('Error capturing photo');
                            }
                          }
                        },
                        multiple: (multiple) async {
                          try {
                            final file =
                                await multiple.fileBySensor.values.first;
                            if (file != null && mounted) {
                              final capturedFile = File(file.path);
                              // Verify file exists before setting state
                              if (await capturedFile.exists()) {
                                setState(() {
                                  if (_isScanningFront) {
                                    _frontIdImage = capturedFile;
                                    _isTransitioning = true;
                                  } else {
                                    _backIdImage = capturedFile;
                                    _showPreview = true;
                                  }
                                  _isCapturing = false;
                                });

                                if (_isScanningFront && mounted) {
                                  unawaited(
                                    _transitionAnimationController
                                        .forward()
                                        .then((_) {
                                      if (mounted) {
                                        setState(() {
                                          _isScanningFront = false;
                                          _isTransitioning = false;
                                        });
                                        _transitionAnimationController.reset();
                                      }
                                    }),
                                  );
                                }
                              } else {
                                debugPrint(
                                    'Captured file does not exist: ${file.path}');
                                if (mounted) {
                                  setState(() => _isCapturing = false);
                                  _showErrorMessage('Failed to save photo');
                                }
                              }
                            } else if (mounted) {
                              setState(() => _isCapturing = false);
                              _showErrorMessage('Failed to capture photo');
                            }
                          } catch (e) {
                            debugPrint('Error processing multiple capture: $e');
                            if (mounted) {
                              setState(() => _isCapturing = false);
                              _showErrorMessage('Error capturing photo');
                            }
                          }
                        },
                      );
                    } catch (e) {
                      debugPrint('Error handling captured photo: $e');
                      if (mounted) {
                        setState(() => _isCapturing = false);
                        _showErrorMessage('Error processing photo');
                      }
                    }
                  },
                ),
              ),
              Positioned(
                top: 60.h,
                left: 24.w,
                right: 24.w,
                child: Text(
                  _frontIdImage == null
                      ? 'Take a front photo of your ID.'
                      : 'Take a back photo of your ID.',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.outfit(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewView() {
    return Column(
      children: [
        // Header with back button
        SafeArea(
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                IconButton(
                  onPressed: _retakePhoto,
                  icon: const Icon(Icons.arrow_back),
                ),
                const Spacer(),
                Text(
                  'Review ID Photos',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                const SizedBox(width: 48), // Balance the back button
              ],
            ),
          ),
        ),
        SizedBox(height: 20.h),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Column(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.r),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(16.r),
                            child: _frontIdImage != null
                                ? Image.file(
                                    _frontIdImage!,
                                    fit: BoxFit.cover,
                                  )
                                : Container(
                                    color: Colors.grey[200],
                                    child: Icon(
                                      Icons.credit_card,
                                      size: 40.sp,
                                      color: Colors.grey[400],
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20.h),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.r),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(16.r),
                            child: _backIdImage != null
                                ? Image.file(
                                    _backIdImage!,
                                    fit: BoxFit.cover,
                                  )
                                : Container(
                                    color: Colors.grey[200],
                                    child: Icon(
                                      Icons.credit_card,
                                      size: 40.sp,
                                      color: Colors.grey[400],
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: 20.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Text(
            'Please review both photos of your ID. If you are satisfied with '
            'the quality and clarity, you may proceed. Otherwise, you can '
            'retake the photos.',
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
        ),
        SizedBox(height: 30.h),
        Padding(
          padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 40.h),
          child: Column(
            children: [
              CustomRoundedBtn(
                btnText: 'Confirm & Continue',
                onTap: _confirmPhoto,
                isLoading: false,
              ),
              SizedBox(height: 16.h),
              CustomRoundedBtn(
                btnText: 'Retake Photos',
                onTap: _retakePhoto,
                isLoading: false,
                bgColor: Colors.white,
                textColor: Theme.of(context).primaryColor,
                borderSide: BorderSide(
                  color: Theme.of(context).primaryColor,
                  width: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _retakePhoto() {
    setState(() {
      _showPreview = false;
      _frontIdImage = null;
      _backIdImage = null;
      _isScanningFront = true;
      _isTransitioning = false;
    });
    _transitionAnimationController.reset();
  }

  void _confirmPhoto() {
    if (_frontIdImage != null && _backIdImage != null) {
      // Return the captured ID images to the identity verification page
      Navigator.pop(context, {
        'frontImage': _frontIdImage!,
        'backImage': _backIdImage!,
      });
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
