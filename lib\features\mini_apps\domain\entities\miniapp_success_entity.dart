import 'package:equatable/equatable.dart';

class MiniappSuccessEntity extends Equatable {
  const MiniappSuccessEntity(
      {required this.statusCode, required this.success, required this.data});
  final int statusCode;
  final bool success;
  final MiniappSuccessDataEntity data;

  @override
  // TODO: implement props
  List<Object?> get props => [statusCode, success, data];
}

class MiniappSuccessDataEntity {
  MiniappSuccessDataEntity({
    required this.id,
    required this.typeId,
    required this.senderId,
    required this.customerName,
    required this.senderName,
    required this.senderPhone,
    required this.senderEmail,
    required this.transactionOwner,
    required this.sessionID,
    required this.cardNumber,
    required this.redirectURL,
    required this.mpgsReference,
    required this.beneficiaryId,
    required this.beneficiaryName,
    required this.beneficiaryPhone,
    required this.beneficiaryAccountNo,
    required this.beneficiaryEmail,
    required this.bankName,
    required this.bankCode,
    required this.bankId,
    required this.bank<PERSON><PERSON>,
    required this.senderAvatar,
    required this.beneficiaryAvatar,
    required this.senderConnectCode,
    required this.beneficiaryConnectCode,
    required this.transactionType,
    required this.orderId,
    required this.giftPackageId,
    required this.merchantId,
    required this.merchantType,
    required this.paidUsing,
    required this.totalGiftPackageQty,
    required this.packageDiscountLevel,
    required this.packageDiscountId,
    required this.packageCampaignId,
    required this.billAmount,
    required this.originalCurrency,
    required this.serviceCharge,
    required this.vat,
    required this.totalAmount,
    required this.changedCurrency,
  });
  final String id;
  final String typeId;
  final String senderId;
  final String customerName;
  final String senderName;
  final String senderPhone;
  final String senderEmail;
  final String transactionOwner;
  final String sessionID;
  final String cardNumber;
  final String redirectURL;
  final String mpgsReference;
  final String beneficiaryId;
  final String beneficiaryName;
  final String beneficiaryPhone;
  final String beneficiaryAccountNo;
  final String beneficiaryEmail;
  final String bankName;
  final String bankCode;
  final String bankId;
  final String bankLogo;
  final String senderAvatar;
  final String beneficiaryAvatar;
  final String senderConnectCode;
  final String beneficiaryConnectCode;
  final String transactionType;
  final String orderId;
  final String giftPackageId;
  final String merchantId;
  final String merchantType;
  final String paidUsing;
  final String totalGiftPackageQty;
  final String packageDiscountLevel;
  final String packageDiscountId;
  final String packageCampaignId;
  final double billAmount;
  final String originalCurrency;
  final double serviceCharge;
  final double vat;
  final double totalAmount;
  final String changedCurrency;
}
