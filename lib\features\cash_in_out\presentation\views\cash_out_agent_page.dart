import 'dart:async';
import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_bloc.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_event.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_state.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';

class CashOutAgentPage extends StatefulWidget {
  const CashOutAgentPage({super.key});

  @override
  State<CashOutAgentPage> createState() => _CashOutAgentPageState();
}

class _CashOutAgentPageState extends State<CashOutAgentPage> {
  final TextEditingController _agentIdController = TextEditingController();
  Timer? _debounceTimer;
  String _previousText = '';

  @override
  void initState() {
    super.initState();
    _agentIdController.addListener(() {
      if (_agentIdController.text != _previousText) {
        _previousText = _agentIdController.text;
        setState(() {
          // Reset agent state when the text changes
          context.read<CashInCashOutBloc>().add(ResetAgentState());
        });
      }
    });
  }

  bool get _isButtonEnabled {
    return _agentIdController.text.length > 5;
  }

  String get _buttonText {
    final state = context.read<CashInCashOutBloc>().state;
    return state is AgentSearchSuccess ? 'Continue' : 'Check Account';
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _agentIdController.removeListener(() {
      setState(() {
        // Reset agent state when the text changes
        context.read<CashInCashOutBloc>().add(ResetAgentState());
      });
    });
    _agentIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CashInCashOutBloc, CashInCashOutState>(
      listener: (context, state) {
        // Force rebuild to update button state when agent state changes
        setState(() {});
        if (state is AgentSearchSuccess) {
          // Close keyboard when agent is found
          FocusScope.of(context).unfocus();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text(
            'Cash-Out',
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const CustomPageHeader(
                          pageTitle: 'Cash Out',
                          description:
                              'Please enter the agent ID or scan the agent QR code, then proceed with the money transfer and cash out.',
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          'Agent ID Number',
                          style: GoogleFonts.outfit(
                            fontSize: 13.sp,
                            color: const Color(0xFFAAAAAA),
                          ),
                        ),
                        SizedBox(height: 8.h),
                        Container(
                          padding: const EdgeInsets.only(
                            left: 8,
                            right: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _agentIdController,
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    setState(() {
                                      // Reset agent state when the text changes
                                      context
                                          .read<CashInCashOutBloc>()
                                          .add(ResetAgentState());
                                    });
                                  },
                                  style: GoogleFonts.outfit(
                                    fontSize: 16.sp,
                                    color: Colors.black,
                                  ),
                                  inputFormatters: const [],
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    hintText: 'Enter agent ID',
                                    hintStyle: GoogleFonts.outfit(
                                      fontSize: 16.sp,
                                      color: Colors.grey[400],
                                    ),
                                  ),
                                ),
                              ),
                              GestureDetector(
                                onTap: () =>
                                    context.pushNamed(AppRouteName.cashOutQr),
                                child: Container(
                                  padding: EdgeInsets.all(6.w),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8.r),
                                  ),
                                  child: Icon(
                                    CupertinoIcons.qrcode_viewfinder,
                                    size: 36.w,
                                    color: LightModeTheme().primaryColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 8.h),
                        if (_agentIdController.text.length >= 6) ...[
                          BlocBuilder<CashInCashOutBloc, CashInCashOutState>(
                            builder: (context, state) {
                              if (state is CashInCashOutLoading) {
                                return Container(
                                  margin: EdgeInsets.only(bottom: 24.h),
                                  child: Shimmer.fromColors(
                                    baseColor: Colors.grey[300]!,
                                    highlightColor: Colors.grey[100]!,
                                    child: Container(
                                      padding: EdgeInsets.all(14.w),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.4),
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(12.r),
                                      ),
                                      child: Row(
                                        children: [
                                          CircleAvatar(
                                            backgroundColor: Colors.white,
                                            radius: 24.r,
                                          ),
                                          SizedBox(width: 12.w),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  width: 150.w,
                                                  height: 16.h,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                      4,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(height: 8.h),
                                                Container(
                                                  width: 120.w,
                                                  height: 14.h,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                      4,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              } else if (state is AgentSearchSuccess) {
                                return GestureDetector(
                                  onTap: () {
                                    context.pushNamed(
                                      AppRouteName.cashOutAgentAddAmount,
                                      extra: {
                                        'agent': state.agent,
                                      },
                                    );
                                  },
                                  child: RecipientCard(
                                    name: state.agent.agentName,
                                    accountNumber: state.agent.agentCode,
                                    isBirrTransfer: true,
                                    isCashOut: true,
                                  ),
                                );
                              } else if (state is CashInCashOutFailure) {
                                return Text(
                                  'No agent found with this ID. Please check and try again.',
                                  style: GoogleFonts.outfit(
                                    fontSize: 14.sp,
                                    color: Colors.red,
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                CustomRoundedBtn(
                  btnText: _buttonText,
                  isLoading: false,
                  onTap: _isButtonEnabled
                      ? () {
                          final state = context.read<CashInCashOutBloc>().state;
                          if (state is AgentSearchSuccess) {
                            context.pushNamed(
                              AppRouteName.cashOutAgentAddAmount,
                              extra: {
                                'agent': state.agent,
                              },
                            );
                          } else {
                            context.read<CashInCashOutBloc>().add(
                                  SearchAgentRequested(
                                    _agentIdController.text,
                                  ),
                                );
                          }
                        }
                      : null,
                  isBtnActive: _isButtonEnabled,
                ),
                SizedBox(
                  height: Platform.isIOS ? 24 : 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
