import 'package:cbrs/core/utils/typedef.dart';

abstract class MyConnectRepository {
  ResultFuture<dynamic> memberLookup({
    required String phoneNumber,
    required String email,
  });

  ResultFuture<dynamic> fetchMyConnects();

  ResultFuture<dynamic> sendConnection({required String beneficiaryId});

  ResultFuture<dynamic> acceptOrDeclineRequest({
    required String beneficiaryId,
    required String status,
  });

  ResultFuture<dynamic> connectTransactions({required String beneficiaryId});
}
