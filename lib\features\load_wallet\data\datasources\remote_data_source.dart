import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_request.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_response.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_status_response.dart';

abstract class LoadTowalletRemoteDataSource {
  Future<LoadWalletResponse> loadToWallet(LoadWalletRequest request);
  Future<LoadWalletResponse> getLoadWalletDetails(String billRefNo);
  Future<LoadWalletStatusResponse> checkLoadWalletStatus(String billRefNo);
}

class LoadTowalletRemoteDataSourceImpl implements  LoadTowalletRemoteDataSource{
  const LoadTowalletRemoteDataSourceImpl({
    required ApiService apiService,
  }) : _apiService = apiService;
  final ApiService _apiService;


  @override
  Future<LoadWalletStatusResponse> checkLoadWalletStatus(String billRefNo) async{
 try {
      final result = await _apiService.post(
      ApiEndpoints.loadToWalletStatus,
        data: {'bill_refno': billRefNo},
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return LoadWalletStatusResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }
  }

  @override
  Future<LoadWalletResponse> getLoadWalletDetails(String billRefNo) async{
  try {
      final result = await _apiService.get(
         ApiEndpoints.getLoadWalletDetails(billRefNo),
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return LoadWalletResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }
  }

  @override
  Future<LoadWalletResponse> loadToWallet(LoadWalletRequest request) async {
   try {
      final result = await _apiService.post(
        ApiEndpoints.loadToWallet,
        data: request.toJson(),
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return LoadWalletResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }
  }

}