import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:cbrs/features/mini_apps/data/models/miniapp_model.dart';

class PaginatedMiniappResponseModel extends Equatable {
  const PaginatedMiniappResponseModel({
    required this.data,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.perPage,
  });

  factory PaginatedMiniappResponseModel.fromJson(Map<String, dynamic> json) {
    final dataList = json['docs'] as List<dynamic>;

    return PaginatedMiniappResponseModel(
      data:
      // [],

      dataList
          .map(
            (item) => MiniappModel.fromJson(item as Map<String, dynamic>),
      )
          .toList(),
      currentPage: json['currentPage'] as int? ?? 1,
      totalPages: json['totalPages'] as int? ?? 1,
      totalItems: json['totalItems'] as int? ?? 0,
      perPage: json['perPage'] as int? ?? 10,
    );
  }
  final List<MiniappModel> data;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int perPage;

  @override
  List<Object?> get props => [
    data,
    currentPage,
    totalPages,
    totalItems,
    perPage,
  ];
}
