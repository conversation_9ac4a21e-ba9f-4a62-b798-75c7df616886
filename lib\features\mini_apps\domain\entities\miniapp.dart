import 'package:equatable/equatable.dart';

class MiniappEntity extends Equatable {
  const MiniappEntity({
    required this.miniappData,
    required this.miniappPaginate,
  });
  final List<MiniappDataEntity> miniappData;
  final MiniappPaginateEntity? miniappPaginate;

  @override
  List<Object?> get props => [miniappData, miniappPaginate];
}

class MiniappDataEntity extends Equatable {
  const MiniappDataEntity({
    required this.merchantType,
    required this.miniAppIcon,
    required this.miniAppName,
    required this.url,
  });
  final String merchantType;
  final String miniAppIcon;
  final String miniAppName;
  final String url;

  @override
  List<Object?> get props => [merchantType, miniAppIcon, miniAppName, url];
}

class MiniappPaginateEntity extends Equatable {
  const MiniappPaginateEntity({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.perPage,
  });
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int perPage;

  @override
  List<Object?> get props => [currentPage, totalPages, totalItems, perPage];
}
