import 'dart:convert';
import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/mini_apps/data/models/miniapp_success_model.dart';
import 'package:dio/dio.dart';
import 'package:cbrs/features/mini_apps/data/models/miniapp_model.dart';
import 'package:cbrs/features/mini_apps/data/models/create_order_miniapp_model.dart';

import 'package:cbrs/features/mini_apps/data/models/paginated_miniapp_response_model.dart';
import 'package:cbrs/core/error/exceptions.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:flutter/foundation.dart';

abstract class MiniappRemoteDataSource {
  /// Fetches a paginated list of utilities
  ///
  Future<MiniappModel> getMiniapps({
    required int page,
    required int perPage,
    required String stage,
  });

  Future<CreateOrderMiniappModel> createOrder({
    required dynamic data,
  });

  Future<MiniappSuccessModel> submitPin({
    required String transactionType,
    required String billRefNo,
    required String pin,
  });

  Future<MiniappModel> submitOtp({
    required String transactionType,
    required String billRefNo,
    required String otp,
  });
}

class MiniappRemoteDataSourceImpl implements MiniappRemoteDataSource {
  MiniappRemoteDataSourceImpl({
    required this.dio,
    required this.authLocalDataSource,
  });
  final Dio dio;
  final AuthLocalDataSource authLocalDataSource;

  Future<Response<T>> _safeApiCall<T>(
      Future<Response<T>> Function() apiCall,
      ) async {
    try {
      final response = await apiCall();

      return response;
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Unknown error',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  @override
  Future<MiniappModel> getMiniapps({
    required int page,
    required int perPage,
    required String stage,
  }) async {
    try {
      debugPrint('GFet utility');
      final response = await _safeApiCall(
            () => dio.get(
          ApiEndpoints.utlilityPaginate,
          queryParameters: {
            'page': page,
            'perPage': perPage,
            'stage': stage,
          },
        ),
      );

      debugPrint('Get utility respsne $response');

      if (response.statusCode == 200) {
        return MiniappModel.fromJson(
          response.data as Map<String, dynamic>,
        );
      }

      throw ApiException(
        message:
        response.data['message'] as String? ?? 'Failed to load utilities',
        statusCode: response.statusCode ?? 400,
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.response?.data['message'] as String? ??
            'Failed to connect to server',
        statusCode: e.response?.statusCode ?? 400,
      );
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      debugPrint('Getting utitltis catch $e');
      throw const ApiException(
        message: 'Something went wrong during fetching utilities.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<CreateOrderMiniappModel> createOrder({
    required dynamic data,
  }) async {
    try {
      /*
           final token = await authLocalDataSource.getAuthToken();
      final encodedRequest = jsonEncode(request['orderPayload']);

      debugPrint("xapikey ${request['x-api-key']}");

      final response = await dio.post(
        ApiEndpoints.utilityPayment,
        data: encodedRequest,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
            if (request['datatoken']?.toString()?.isNotEmpty ?? false)
              'datatoken': request['datatoken'],
            if (request['deviceUuid']?.toString()?.isNotEmpty ?? false)
              'deviceuuid': request['deviceUuid'],
            if (request['memberapp']?.toString()?.isNotEmpty ?? false)
              'sourceapp': request['memberapp'],
            if (request['a-access-token']?.toString()?.isNotEmpty ?? false)
              'a-access-token': request['a-access-token']!,
            'appSecretKey': request['x-api-key']
          },
        ),
      );
      debugPrint("Response: $response");

*/

      final encodedRequest = jsonEncode(data['orderPayload']);
      debugPrint("xapikey ${data['x-api-key']}");
      final token = await authLocalDataSource.getAuthToken();

      final response = await _safeApiCall(
            () => dio.post(
          ApiEndpoints.utilityPayment,
          data: encodedRequest,
          options: Options(
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json',
              if (data['datatoken']?.toString().isNotEmpty ?? false)
                'datatoken': data['datatoken'],
              if (data['deviceUuid']?.toString().isNotEmpty ?? false)
                'deviceuuid': data['deviceUuid'],
              if (data['memberapp']?.toString().isNotEmpty ?? false)
                'sourceapp': data['memberapp'],
              if (data['a-access-token']?.toString().isNotEmpty ?? false)
                'a-access-token': data['a-access-token'],
              'appSecretKey': data['x-api-key'],
            },
          ),
        ),
      );

      debugPrint('Repsonse from creaoteing order  ${response}');

      if (response.statusCode == 200) {
        // throw ApiException(
        //   message: ' intentional eerrororoororor',
        //   statusCode: response.statusCode ?? 400,
        // );

        return CreateOrderMiniappModel.fromJson(
          response.data as Map<String, dynamic>,
        );
      }

      throw ApiException(
        message:
        response.data['message'] as String? ?? 'Failed to load utilities',
        statusCode: response.statusCode ?? 400,
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.response?.data['message'] as String? ??
            'Failed to connect to server',
        statusCode: e.response?.statusCode ?? 400,
      );
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong during fetching utilities.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<MiniappSuccessModel> submitPin({
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) async {
    try {
      final token = await authLocalDataSource.getAuthToken();

      final response_back = await _safeApiCall(
            () => dio.post(
          '${ApiConstants.baseUrl}/member-transactions/confirm-payment',
          data: {
            'transactionType': transactionType,
            'billRefNo': billRefNo,
            'PIN': pin,
          },
        ),
      );

      debugPrint("respsosne_successs ${response_back}");

      if (response_back.statusCode == 200) {
        return MiniappSuccessModel.fromJson(
          response_back.data as Map<String, dynamic>,
        );
      }

      throw ApiException(
        message: response_back.data['message'] as String? ??
            'Failed to load utilities',
        statusCode: response_back.statusCode ?? 400,
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.response?.data['message'] as String? ??
            'Failed to connect to server',
        statusCode: e.response?.statusCode ?? 400,
      );
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong during fetching utilities.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<MiniappModel> submitOtp({
    required String transactionType,
    required String billRefNo,
    required String otp,
  }) async {
    try {
      final token = await authLocalDataSource.getAuthToken();

      final response = await _safeApiCall(
            () => dio.get(
          ApiEndpoints.utlilityPaginate,
          queryParameters: {
            'transactionType': transactionType,
            'billRefNo': billRefNo,
            'otp': otp,
          },
        ),
      );

      if (response.statusCode == 200) {
        return MiniappModel.fromJson(
          response.data as Map<String, dynamic>,
        );
      }

      throw ApiException(
        message:
        response.data['message'] as String? ?? 'Failed to load utilities',
        statusCode: response.statusCode ?? 400,
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.response?.data['message'] as String? ??
            'Failed to connect to server',
        statusCode: e.response?.statusCode ?? 400,
      );
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong during fetching utilities.',
        statusCode: 500,
      );
    }
  }
}
