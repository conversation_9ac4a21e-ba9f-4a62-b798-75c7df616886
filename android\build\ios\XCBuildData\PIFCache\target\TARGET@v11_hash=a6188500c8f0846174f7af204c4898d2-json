{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b30b220151af82f7a98e7e09059bd78", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b107fef70d0c224b5b969d692454c05a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b107fef70d0c224b5b969d692454c05a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e17a546b7a5b0bbbcf8067a51dd497a4", "guid": "bfdfe7dc352907fc980b868725387e985f0085899a9f5aec5a0202605a8918e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa40bc526a64bb12e2c2690f6ced071c", "guid": "bfdfe7dc352907fc980b868725387e98611718009e578d911dfa24820f8938c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98855951c150c5c6fc33d4a8503237e167", "guid": "bfdfe7dc352907fc980b868725387e98a6682a128e26f73988588a0b9dc99c4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a4244255fa5fb93795151447f92ea2d", "guid": "bfdfe7dc352907fc980b868725387e98a3fcd6fa162c0d365d634616e789d6ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98493e26f5b8391e543594553ba1329290", "guid": "bfdfe7dc352907fc980b868725387e983e6167a4e115668fb3799c465a0cfc40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc02cb511a61b541938d7ee569529402", "guid": "bfdfe7dc352907fc980b868725387e98322b1742f5524a6d0e37f0b0497cc76c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828e256ae19d14cadd42b7deefd612fca", "guid": "bfdfe7dc352907fc980b868725387e9843ada8db56df3fa463ceac3d83ee854b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823de9df22da32cd0584dbf16c9a208bf", "guid": "bfdfe7dc352907fc980b868725387e980ac4bf4b39ebe460e5e8c74c9450f291", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb8da802693f46983aefafde7c6c436c", "guid": "bfdfe7dc352907fc980b868725387e98bbd8a28e44ee02016bb2b7154fb70861", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af665fa0aa5a73a2905f13a6b5f88b3", "guid": "bfdfe7dc352907fc980b868725387e9843fad2db3368a046c718d5256715c904", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812d9607c25d61d12a19d5b32be7bcd87", "guid": "bfdfe7dc352907fc980b868725387e9828d12d05508c1581ba2436be33f4de22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0cf85cbc4e5f94dd4009bbba2040c39", "guid": "bfdfe7dc352907fc980b868725387e982a76ee02c6f40170d9e26dcda3f47392", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf58fe615641db0eed8874b2f6969402", "guid": "bfdfe7dc352907fc980b868725387e982179a03359c5c1547754ab9df73dc6ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb68584ccd9e760fa21635bdd6c61d64", "guid": "bfdfe7dc352907fc980b868725387e98d678334a5d238374f2ad9c04b29d4364", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ead31aad35b288d044e2477179fcc4c", "guid": "bfdfe7dc352907fc980b868725387e98775d03fe3e3492d0099115e1bf707a2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890c8bd37370ebec5dcbc93aafd561011", "guid": "bfdfe7dc352907fc980b868725387e98079c00739040ad2f95375b026b07ccfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872fd0361a2381ec5cdf144612b2b65f4", "guid": "bfdfe7dc352907fc980b868725387e98e3932ebd3a8b73ab380edcbb9178253b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825ecc62b60762b9f1b021bac01290a55", "guid": "bfdfe7dc352907fc980b868725387e98a280d73544069688727cb901c0e4c23b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebae128f9e5078c532134a431f8c77db", "guid": "bfdfe7dc352907fc980b868725387e98b0f7618034ed189c15db88fd3fea3d89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98723bd3c148e0cee596d60a865fc623b7", "guid": "bfdfe7dc352907fc980b868725387e98c601d2214f03bd2cd74b3097ecac9d80", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989ef904fc5286bc0a41a6be98a3130cdf", "guid": "bfdfe7dc352907fc980b868725387e98adadec3b201175c8d0e30e0e0875c721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98597b8735475b1b91f17c35eb40f229c6", "guid": "bfdfe7dc352907fc980b868725387e9855bbf862dbacc561aea6d6c3b71beffd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dabcf2e813dec885e83bd34262ea90b7", "guid": "bfdfe7dc352907fc980b868725387e980933bd8b7809f3d220559f518c73495b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98882bf969e87117f72051eb66a59f1b48", "guid": "bfdfe7dc352907fc980b868725387e984f3927b5025fbab50f810894b2944645"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2a727626017efbe5f2e1e98174d3c06", "guid": "bfdfe7dc352907fc980b868725387e98a7337d83be2cab6dc31cc46cebb38081"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd4814779cb878d7c57da77001c7e926", "guid": "bfdfe7dc352907fc980b868725387e98c97a8ecbd5b2762aca0093db40716ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bcef75f45bf338fe1d0276a76052b71", "guid": "bfdfe7dc352907fc980b868725387e98ec11d162d18201d0db7fc2a6c6450791"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dbeb8ef7bc8f95b6f0f0ec77e10a973", "guid": "bfdfe7dc352907fc980b868725387e98b89d056539b9db589f8f435fb0ee22cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e731d084eb981d144b1d07915cf9cfff", "guid": "bfdfe7dc352907fc980b868725387e98e74e91e4c474192ff4f6c671250611a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c5ab6de786e230f4ed81b96040280bc", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894fc9c2fa5388ee4f07162c9d4db2867", "guid": "bfdfe7dc352907fc980b868725387e98f3c9db90a8372e8e2e2d76a4d3f5b63f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c3ddb44282da685d433756055e2beb", "guid": "bfdfe7dc352907fc980b868725387e98ac4a1bc695ff6aa06833c9cbb5c44312"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b06e6164bc5613025dbbc7ec0461800", "guid": "bfdfe7dc352907fc980b868725387e984219fb5f96a080c36f13a083e2af6774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f5f443b8883336f78934ac5a97b7851", "guid": "bfdfe7dc352907fc980b868725387e98ad13bb31d800eda1d0c0c6976c915b1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ebd9cf555181a753003c10a74607008", "guid": "bfdfe7dc352907fc980b868725387e98fdd592debdee1d55b93b79d74af0e92b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cd6a7eebb038734e2128594bada3ae4", "guid": "bfdfe7dc352907fc980b868725387e98cf4f6f22792b8db021e8e1c754326233"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}