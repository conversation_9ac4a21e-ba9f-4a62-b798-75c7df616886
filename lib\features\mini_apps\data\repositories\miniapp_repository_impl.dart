import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/add_money/data/datasources/add_money_remote_datasource.dart';
import 'package:cbrs/features/add_money/data/models/add_money_response_model.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/add_money/domain/repositories/add_money_repository.dart';
import 'package:cbrs/features/send_money/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/send_money/data/models/otp_resend_response.dart';
import 'package:cbrs/features/mini_apps/data/datasources/miniapp_remote_data_source.dart';
import 'package:cbrs/features/mini_apps/data/models/create_order_miniapp_model.dart';
import 'package:cbrs/features/mini_apps/data/models/miniapp_model.dart';
import 'package:cbrs/features/mini_apps/data/models/miniapp_success_model.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:cbrs/features/mini_apps/domain/repositories/miniapp_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';

class MiniappRepositoryImpl implements MiniappRepository {
  const MiniappRepositoryImpl(this._remoteDataSource);
  final MiniappRemoteDataSource _remoteDataSource;

  @override
  ResultFuture<MiniappModel> getMiniapps({
    required int page,
    required int perPage,
    required String stage,
  }) async {
    try {
      final result = await _remoteDataSource.getMiniapps(
        page: page,
        perPage: perPage,
        stage: stage,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<CreateOrderMiniappModel> createOrder({required data}) async {
    try {
      final result = await _remoteDataSource.createOrder(
        data: data,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<MiniappEntity> submitOtp({
    required String transactionType,
    required String billRefNo,
    required String otp,
  }) async {
    try {
      final result = await _remoteDataSource.submitOtp(
        transactionType: transactionType,
        otp: otp,
        billRefNo: billRefNo,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<MiniappSuccessModel> submitPin({
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) async {
    try {
      final result = await _remoteDataSource.submitPin(
        transactionType: transactionType,
        pin: pin,
        billRefNo: billRefNo,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }
}
