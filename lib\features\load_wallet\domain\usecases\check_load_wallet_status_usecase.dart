import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_status_response.dart';
import 'package:cbrs/features/load_wallet/domain/repositories/load_wallet_repository.dart';

class CheckLoadWalletStatusUseCase {
  final LoadWalletRepository repository;

  CheckLoadWalletStatusUseCase(this.repository);

  ResultFuture<LoadWalletStatusResponse> call(String billRefNo) async {
    return await repository.checkLoadWalletStatus(billRefNo);
  }
}