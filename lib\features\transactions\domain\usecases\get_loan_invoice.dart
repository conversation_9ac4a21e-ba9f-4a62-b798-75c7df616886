import 'package:cbrs/core/utils/typedef.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:cbrs/features/transactions/domain/entities/loan_invoice.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';

class GetLoanInvoice
    implements UsecaseWithParams<String, GetLoanInvoiceParams> {
  GetLoanInvoice(this.repository);
  final TransactionRepository repository;

  @override
  ResultFuture<String> call(GetLoanInvoiceParams params) {
    return repository.getLoanInvoice(
      billRefNo: params.billRefNo,
      loanReceipt: params.loanReceipt,
    );
  }
}

class GetLoanInvoiceParams extends Equatable {
  const GetLoanInvoiceParams({
    required this.billRefNo,
    required this.loanReceipt,
  });
  final String billRefNo;
  final LoanReceipt loanReceipt;

  @override
  List<Object?> get props => [billRefNo, loanReceipt];
}
