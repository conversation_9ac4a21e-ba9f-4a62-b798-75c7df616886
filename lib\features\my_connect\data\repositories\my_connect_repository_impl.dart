import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_connect/data/datasources/my_connect_remote_datasource.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_list_response_entity.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';
import 'package:cbrs/features/my_connect/domain/repositories/my_connect_repository.dart';
import 'package:dartz/dartz.dart';

class MyConnectRepositoryImpl implements MyConnectRepository {
  final MyConnectRemoteDataSource _remoteDataSource;

  MyConnectRepositoryImpl(this._remoteDataSource);

  @override
  ResultFuture<ConnectionRequestEntity> sendConnectionRequest({
    required String recipientId,
    required String recipientName,
    String? recipientEmail,
    required String recipientPhone,
    String? recipientAvatar,
  }) async {
    try {
      final result = await _remoteDataSource.sendConnectionRequest(
        recipientId: recipientId,
        recipientName: recipientName,
        recipientEmail: recipientEmail,
        recipientPhone: recipientPhone,
        recipientAvatar: recipientAvatar,
      );
      return Right(result);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure.fromException(e));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  ResultFuture<ConnectionListResponseEntity> getConnections({
    String? status,
    String? scope,
    int? page,
    int? limit,
  }) async {
    try {
      final result = await _remoteDataSource.getConnections(
        status: status,
        scope: scope,
        page: page,
        limit: limit,
      );
      return Right(result);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure.fromException(e));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  ResultFuture<void> acceptConnectionRequest(String requestId) async {
    try {
      await _remoteDataSource.acceptConnectionRequest(
        requestId: requestId,
        accept: true,
      );
      return const Right(null);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure.fromException(e));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  ResultFuture<void> rejectConnectionRequest(String requestId) async {
    try {
      await _remoteDataSource.acceptConnectionRequest(
        requestId: requestId,
        accept: false,
      );
      return const Right(null);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure.fromException(e));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
