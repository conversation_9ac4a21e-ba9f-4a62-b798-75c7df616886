import 'package:cbrs/core/common/widgets/success/custom_transaction_success_screen.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp_success_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class MiniappSuccessScreen extends StatelessWidget {
  const MiniappSuccessScreen({super.key, required this.data});

  final MiniappSuccessDataEntity data;

  String _formatAmount(double amount) {
    return NumberFormat.currency(
      symbol: 'ETB ',
      decimalDigits: 2,
      customPattern: '#,##0.00 ¤',
    ).format(amount);
  }

  // Widget _buildTransactionDetails(BuildContext context) {
  //   return Text("Please uncomment code below and fix them");
  // }

  Widget _buildTransactionDetails(BuildContext context) {
    return Column(
      children: [
        _buildTransactionDetail('Sender Name:', data.senderName),
        _buildTransactionDetail('Transaction Type:', data.merchantType),
        _buildTransactionDetail('Recipient Account:', data.beneficiaryAccountNo),
        _buildTransactionDetail('Reference No:', data.beneficiaryId),
        _buildTransactionDetail(
          'Date:',
          DateFormat('MMMM dd, yyyy').format(DateTime.now()),
        ),
        _buildTransactionDetail(
          'Service Fee:',
          _formatAmount(data.serviceCharge),
        ),
        _buildTransactionDetail(
          'VAT:',
          _formatAmount(data.vat),
        ),
        _buildTransactionDetail(
          'Total:',
          _formatAmount(data.billAmount + data.vat + data.serviceCharge),
        ),
      ],
    );
  }

  Widget _buildTransactionDetail(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 4,
            child: Text(
              label,
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                color: const Color(0xFF757575),
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Text(
              value,
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomTransactionSuccessScreen(
      pageDescription: 'MiniApp Payment Successful',
      totalAmount: data.billAmount,
      isBirrTransfer: true,
      hasPageTitle: true,
      billRefNo: '',
      child: _buildTransactionDetails(context),
      onQrTap: () {
        // Implement QR functionality
      },
      onShareTap: () {
        // Implement share functionality
      },
      onGetRecieptTap: () {
        // Implement receipt functionality
      },
    );
  }
}
