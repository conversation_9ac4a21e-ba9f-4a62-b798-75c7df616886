import 'dart:async';
import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_app_bar.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/utils/form_validation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CreatePinScreen extends StatefulWidget {
  const CreatePinScreen({
    required this.source,
    super.key,
  });
  final String source;

  @override
  State<CreatePinScreen> createState() => _CreatePinScreenState();
}

class _CreatePinScreenState extends State<CreatePinScreen> {
  final _formKey = GlobalKey<FormState>();
  final _newPinController = TextEditingController();
  final _confirmPinController = TextEditingController();
  bool _isNewPinVisible = false;
  bool _isConfirmPinVisible = false;

  void _createPin() {
    if (_formKey.currentState!.validate()) {
      if (_newPinController.text != _confirmPinController.text) {
        CustomToastification(context, message: 'PINs do not match');

        return;
      }
      _handleChangePin();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthError) {
          CustomToastification(context, message: state.message);
        } else if (state is LoggedInWithPinState) {
          context.go(AppRouteName.home);
        }
      },
      builder: (context, state) {
        final isLoading = state is AuthLoading;

        return Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: CustomAppBar(context: context, title: 'Create PIN'),
          body: SafeArea(
            bottom: false,
            child: GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus(); // Hide the keyboard
              },
              child: Stack(
                fit: StackFit.expand,
                children: [
                  SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: SizedBox(
                      height: MediaQuery.sizeOf(context).height -
                          kToolbarHeight -
                          (Platform.isIOS ? 64 : 30),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 22.h,
                            ),
                            Text(
                              'Create PIN',
                              style: GoogleFonts.outfit(
                                fontSize: 24.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Create a new secure PIN and confirm it. Make sure to use a strong PIN.',
                              style: GoogleFonts.outfit(
                                fontSize: 16.sp,
                                color: const Color(0xFFAAAAAA),
                              ),
                            ),
                            SizedBox(height: 24.h),
                            _buildPinInput(
                              theme,
                              'New PIN',
                              _newPinController,
                              _isNewPinVisible,
                              validator: (value) =>
                                  FormValidation.validatePin(value, 'New Pin'),
                              () => setState(
                                () => _isNewPinVisible = !_isNewPinVisible,
                              ),
                            ),
                            SizedBox(height: 20.h),
                            _buildPinInput(
                              theme,
                              'Confirm New PIN',
                              _confirmPinController,
                              _isConfirmPinVisible,
                              validator: (value) =>
                                  FormValidation.validateConfirmPIN(
                                value,
                                _newPinController.text,
                              ),
                              () => setState(
                                () => _isConfirmPinVisible =
                                    !_isConfirmPinVisible,
                              ),
                              shouldClose: true,
                            ),
                            // SizedBox(height: 390.h),
                            const Spacer(),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 32),
                              child: Container(
                                child: CustomRoundedBtn(
                                  btnText: 'Create PIN',
                                  isLoading: isLoading,
                                  onTap: _createPin,
                                  isBtnActive:
                                      _newPinController.text.isNotEmpty &&
                                          _confirmPinController.text.isNotEmpty,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: Platform.isIOS ? 24.h : 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPinInput(
    ThemeData theme,
    String label,
    TextEditingController controller,
    bool isVisible,
    VoidCallback toggleVisibility, {
    required FormFieldValidator<String> validator,
    bool shouldClose = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: label,
                style: GoogleFonts.outfit(
                  color: const Color(0xFFAAAAAA),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
              TextSpan(
                text: ' *',
                style: GoogleFonts.outfit(
                  color: Colors.red,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 4.h),
        CustomTextFormField(
          controller: controller,
          validator: validator,
          overrideValidator: true,
          // validator: (value) {
          //   if (value == null || value.isEmpty) {
          //     return 'PIN is required';
          //   }
          //   if (!blockNonNumeric(value)) {
          //     return 'PIN must be numeric digits';
          //   }
          //   if (value.length != 6) {
          //     return 'PIN must be 6 digits';
          //   }
          //   if (shouldClose &&
          //       _newPinController.text != _confirmPinController.text) {
          //     return ' PINs don\'t match.';
          //   }

          //   if (!validatePin(_newPinController.text)) {
          //     // CustomToastification(context, message: 'PIN cannot contain consecutive numbers.');

          //     return 'PIN cannot contain consecutive numbers.';
          //   }
          //   return null;
          // },
          isPassword: !isVisible,
          textInputType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(6),
          ],
          style: GoogleFonts.plusJakartaSans(
            color: theme.colorScheme.onSurface,
          ),
          onEditingComplete: () {
            _formKey.currentState?.validate();
            debugPrint('onEditingComplete ⛑️');
          },
          fillColor: theme.colorScheme.onTertiary,
          borderRadius: 8,
          maxLines: 1,
          onChange: (value) {
            if (value.length == 6 && shouldClose)
              FocusScope.of(context).unfocus();
          },
          prefixIcon: Icon(Icons.lock_outline, color: theme.primaryColor),
          decoration: InputDecoration(
            filled: true,
            fillColor: theme.colorScheme.onTertiary,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            counterText: '',
            hintText: '*******',
            hintStyle: GoogleFonts.outfit(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            errorMaxLines: 2,
            errorStyle: GoogleFonts.outfit(color: Colors.red, fontSize: 14.sp),
            contentPadding: EdgeInsets.symmetric(
              vertical: 16.h,
              horizontal: 20.w,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                isVisible
                    ? Icons.visibility_off_outlined
                    : Icons.visibility_outlined,
                color: Colors.grey.shade500,
              ),
              onPressed: toggleVisibility,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleChangePin() async {
    // Retrieve input values
    final newPin = _newPinController.text.trim();
    final confirmPin = _confirmPinController.text.trim();

    // // Validation
    // if (newPin.isEmpty || confirmPin.isEmpty) {
    //   CustomToastification(context, message: 'PIN cannot be blank.');
    //   return;
    // } else if (!RegExp(r'^\d{6}$').hasMatch(newPin) ||
    //     !RegExp(r'^\d{6}$').hasMatch(confirmPin)) {
    //   CustomToastification(context,
    //       message: 'PIN must be exactly 6 numeric digits.');
    //   return;
    // }

    // if (newPin.contains(RegExp(r'\D')) || confirmPin.contains(RegExp(r'\D'))) {
    //   CustomToastification(context, message: 'PIN must contain only numbers.');
    //   return;
    // }

    // if (!validatePin(newPin) || !validatePin(confirmPin)) {
    //   // CustomToastification(context, message: 'Invalid PIN format. Please enter exactly 6 numeric digits.');
    //   return;
    // }

    // if (newPin != confirmPin) {
    //   CustomToastification(context,
    //       message: 'New PIN and confirmation PIN do not match.');
    //   return;
    // }

    try {
      // Store PIN for biometric login
      final deviceController = Get.find<DeviceCheckController>();
      await deviceController.storePin(newPin);

      // Dispatch the event to create the PIN
      context.read<AuthBloc>().add(
            CreatePinEvent(
              pin: newPin,
              source: widget.source,
            ),
          );
    } catch (e) {
      CustomToastification(
        context,
        message: 'Error setting up biometric login',
      );
    }
  }

  bool validatePin(String pin) {
    // Validation for repeating digits and sequential patterns
    final repeatingPattern = RegExp(r'^(\d)\1{5}$'); // e.g., 111111
    final sequentialPattern = RegExp(
      '(012|123|234|345|456|567|678|789|890|987|876|765|654|543|432|321|210)',
    );

    if (repeatingPattern.hasMatch(pin)) {
      CustomToastification(
        context,
        message: 'PIN cannot contain repeating digits.',
      );
      return false;
    }

    if (sequentialPattern.hasMatch(pin)) {
      CustomToastification(
        context,
        message: 'PIN cannot contain sequential numbers ',
      );
      return false;
    }

    return true; // PIN is valid
  }

  bool blockNonNumeric(String value) {
    bool returnValue;
    if (value.contains(RegExp(r'\D'))) {
      returnValue = false;
    } else {
      returnValue = true;
    }
    return returnValue;
  }
}
