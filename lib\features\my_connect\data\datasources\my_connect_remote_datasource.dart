// ignore_for_file: sort_constructors_first

import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/my_connect/data/models/connection_list_response_model.dart';
import 'package:cbrs/features/my_connect/data/models/connection_request_model.dart';
import 'package:cbrs/features/my_connect/data/models/connection_request_response_model.dart';
import 'package:flutter/foundation.dart';

abstract class MyConnectRemoteDataSource {
  Future<ConnectionRequestModel> sendConnectionRequest({
    required String recipientId,
    required String recipientName,
    String? recipientEmail,
    required String recipientPhone,
    String? recipientAvatar,
  });

  Future<ConnectionListResponseModel> getConnections({
    String? status,
    String? scope,
    int? page,
    int? limit,
  });

  Future<void> acceptConnectionRequest({
    required String requestId,
    required bool accept,
  });
}

class MyConnectRemoteDataSourceImpl implements MyConnectRemoteDataSource {
  final ApiService _apiService;
  final AuthLocalDataSource _localDataSource;

  MyConnectRemoteDataSourceImpl({
    required ApiService apiService,
    required AuthLocalDataSource localDataSource,
  })  : _apiService = apiService,
        _localDataSource = localDataSource;

  Future<String> getUserId() async {
    final userId = await _localDataSource.getUserId();
    return userId ?? '';
  }

  @override
  Future<ConnectionRequestModel> sendConnectionRequest({
    required String recipientId,
    required String recipientName,
    String? recipientEmail,
    required String recipientPhone,
    String? recipientAvatar,
  }) async {
    try {
      final userId = await getUserId();
      final data = {
        'recipientId': recipientId,
        'recipientName': recipientName,
        if (recipientEmail != null) 'recipientEmail': recipientEmail,
        'recipientPhone': recipientPhone,
        if (recipientAvatar != null) 'recipientAvatar': recipientAvatar,
      };
      debugPrint("data for sending connection request $data");
      final result = await _apiService.post<ConnectionRequestResponseModel>(
        ApiEndpoints.sendConnectionRequest(recipientId),
        requiresAuth: true,
        data: data,
        parser: (json) => ConnectionRequestResponseModel.fromJson(
            json as Map<String, dynamic>),
      );
      debugPrint("send conecction data source ");

      return result.fold(
        (response) => response.data,
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  @override
  Future<ConnectionListResponseModel> getConnections({
    String? status,
    String? scope,
    int? page,
    int? limit,
  }) async {
    try {
      final queryParams = {
        if (status != null) 'status': status,
        if (scope != null) 'scope': scope,
        if (page != null) 'page': page,
        if (limit != null) 'limit': limit,
      };

      final result = await _apiService.get(ApiEndpoints.myConnect,
          requiresAuth: true,
          queryParameters: queryParams,
          parser: (data) => data as Map<String, dynamic>
          //ConnectionListResponseModel.fromJson(json as Map<String, dynamic>),
          );
      debugPrint("resulewrewt $result");

      return result.fold(
        (response) {
          final returnedData = ConnectionListResponseModel.fromJson(
              response as Map<String, dynamic>);
          debugPrint("Resssopon $returnedData");

          return returnedData;
        },
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  @override
  Future<void> acceptConnectionRequest({
    required String requestId,
    required bool accept,
  }) async {
    try {
      final data = {
        'requestId': requestId,
        'accept': accept,
      };

      final result = await _apiService.post<void>(
        ApiEndpoints.acceptConnectionRequest,
        requiresAuth: true,
        data: data,
        parser: (json) => null,
      );

      result.fold(
        (response) => null,
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }
}
