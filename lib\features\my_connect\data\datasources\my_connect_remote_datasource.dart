import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/my_connect/data/models/connection_list_response_model.dart';
import 'package:cbrs/features/my_connect/data/models/connection_request_model.dart';
import 'package:cbrs/features/my_connect/data/models/connection_request_response_model.dart';

abstract class MyConnectRemoteDataSource {
  Future<ConnectionRequestModel> sendConnectionRequest({
    required String recipientId,
    required String recipientName,
    String? recipientEmail,
    required String recipientPhone,
    String? recipientAvatar,
  });

  Future<ConnectionListResponseModel> getConnections({
    String? status,
    String? scope,
    int? page,
    int? limit,
  });

  Future<void> acceptConnectionRequest({
    required String requestId,
    required bool accept,
  });
}

class MyConnectRemoteDataSourceImpl implements MyConnectRemoteDataSource {
  final ApiService _apiService;

  MyConnectRemoteDataSourceImpl({required ApiService apiService})
      : _apiService = apiService;

  @override
  Future<ConnectionRequestModel> sendConnectionRequest({
    required String recipientId,
    required String recipientName,
    String? recipientEmail,
    required String recipientPhone,
    String? recipientAvatar,
  }) async {
    try {
      final data = {
        'recipientId': recipientId,
        'recipientName': recipientName,
        if (recipientEmail != null) 'recipientEmail': recipientEmail,
        'recipientPhone': recipientPhone,
        if (recipientAvatar != null) 'recipientAvatar': recipientAvatar,
      };

      final result = await _apiService.post<ConnectionRequestResponseModel>(
        ApiEndpoints.sendConnectionRequest,
        data: data,
        parser: (json) => ConnectionRequestResponseModel.fromJson(
            json as Map<String, dynamic>),
      );

      return result.fold(
        (response) => response.data,
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  @override
  Future<ConnectionListResponseModel> getConnections({
    String? status,
    String? scope,
    int? page,
    int? limit,
  }) async {
    try {
      final queryParams = {
        if (status != null) 'status': status,
        if (scope != null) 'scope': scope,
        if (page != null) 'page': page,
        if (limit != null) 'limit': limit,
      };

      final result = await _apiService.get<ConnectionListResponseModel>(
        ApiEndpoints.myConnect,
        queryParameters: queryParams,
        parser: (json) =>
            ConnectionListResponseModel.fromJson(json as Map<String, dynamic>),
      );

      return result.fold(
        (response) => response,
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  @override
  Future<void> acceptConnectionRequest({
    required String requestId,
    required bool accept,
  }) async {
    try {
      final data = {
        'requestId': requestId,
        'accept': accept,
      };

      final result = await _apiService.post<void>(
        ApiEndpoints.acceptConnectionRequest,
        data: data,
        parser: (json) => null,
      );

      result.fold(
        (response) => null,
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }
}
