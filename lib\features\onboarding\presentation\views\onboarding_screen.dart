import 'package:cbrs/core/common/pages/loading_view.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_eth_switch_card.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/gradient_bg.dart';
import 'package:cbrs/core/extensions/context_extensions.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/onboarding/domain/entities/page_content.dart';
import 'package:cbrs/features/onboarding/presentation/cubit/on_boarding_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:googleapis/sheets/v4.dart';

class OnBoardingScreen extends StatefulWidget {
  const OnBoardingScreen({super.key});

  @override
  State<OnBoardingScreen> createState() => _OnBoardingScreenState();
}

class _OnBoardingScreenState extends State<OnBoardingScreen>
    with SingleTickerProviderStateMixin {
  final bool _isCreatingAccount = false;
  bool _isSigningIn = false;
  bool _showSecondPage = false;

  // Add animation controllers
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    context.read<OnBoardingCubit>().checkIfUserFirstTimer();

    // Setup animations
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<double>(begin: 50, end: 0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic),
    );

    _controller.forward();

    // Auto-advance to next page after delay
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && !_showSecondPage) {
        setState(() => _showSecondPage = true);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _navigateToRoute(BuildContext context, String route) {
    if (route == AppRouteName.signIn) {
      setState(() {
        _isSigningIn = true;
      });
    }
    _controller.reverse().then((_) {
      context.read<OnBoardingCubit>().cacheFirstTimer(route: route);
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    const pageContent = PageContent.second();
    // _showSecondPage ? PageContent.second() : PageContent.first();

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(0xFFE6F1FF), // Light blue for USD
              const Color(0xFFE6F1FF).withOpacity(0.6),
            ],
            stops: const [0.0, 0.4],
          ),
        ),
        child: BlocConsumer<OnBoardingCubit, OnBoardingState>(
          listener: (context, state) {
            if (state is UserCached) {
              context.go(state.route);
            }
          },
          builder: (context, state) {
            return
                // _showSecondPage
                //   ?
                SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    const Spacer(flex: 2),

                    Container(
                      child: Image.asset(
                        pageContent.image,
                        fit: BoxFit.contain,
                        width: 120.w,
                        height: 142.h,
                      ),
                    ),
                    SizedBox(height: 20.h),
                    Text(
                      'Your Trusted Transfer Solution',
                      style: GoogleFonts.outfit(
                        fontSize: 23.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Send money to loved ones effortlessly and securely. '
                      'Experience fast, reliable transfers with just a few taps '
                      'and make every transaction count.',
                      style: GoogleFonts.outfit(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFFAAAAAA),
                        height: 1.3,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    // const Spacer(flex: 1),
                    SizedBox(
                      height: 100.h,
                    ),

                    SizedBox(
                      height: 16.h,
                    ),

                    CustomRoundedBtn(
                      btnText: 'Sign Up',
                      isLoading: state is CachingFirstTimer && !_isSigningIn,
                      onTap: () =>
                          _navigateToRoute(context, AppRouteName.signUp),
                    ),
                    SizedBox(height: 16.h),
                    CustomRoundedBtn(
                      btnText: 'Login',
                      isLoading: state is CachingFirstTimer && _isSigningIn,
                      bgColor: Colors.white,
                      loaderColor: Theme.of(context).primaryColor,
                      textColor: Theme.of(context).primaryColor,
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                      ),
                      onTap: () =>
                          _navigateToRoute(context, AppRouteName.signIn),
                    ),
                    SizedBox(height: 16.h),
                    // text button

                    CustomRoundedBtn(
                      btnText: 'Continue as Guest',
                      isLoading: false,
                      // isLoading: state is CachingFirstTimer && _isSigningIn,
                      bgColor: Colors.white,
                      loaderColor: Theme.of(context).primaryColor,
                      textColor: Theme.of(context).primaryColor,
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                      ),
                      onTap: () => context.goNamed('guestDeviceScreen'),
                    ),

                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              ),
            );
            // : _buildFirstPage(size, pageContent);
          },
        ),
      ),
    );
  }
  //
  // // Replace the first page content with this new design
  // Widget _buildFirstPage(Size size, PageContent pageContent) {
  //   return GradientBg(
  //     child: Center(
  //         child: SizedBox(
  //       height: size.height * 0.3,
  //       width: size.height * 0.3,
  //       child: Hero(
  //         tag: 'logo',
  //         child: Image.asset(
  //           MediaRes.lightlogo,
  //           fit: BoxFit.contain,
  //           color: Colors.white,
  //           colorBlendMode: BlendMode.srcIn,
  //         ),
  //       ),
  //     )),
  //   );
  // }
}
