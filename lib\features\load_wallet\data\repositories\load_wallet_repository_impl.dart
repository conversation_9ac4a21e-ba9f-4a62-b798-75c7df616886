import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/load_wallet/data/datasources/remote_data_source.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_request.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_response.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_status_response.dart';
import 'package:cbrs/features/load_wallet/domain/repositories/load_wallet_repository.dart';
import 'package:dartz/dartz.dart';

class LoadWalletRepositoryImpl implements LoadWalletRepository {
  const LoadWalletRepositoryImpl(this._remoteDataSource,);
  final LoadTowalletRemoteDataSource _remoteDataSource;

  @override
  ResultFuture<LoadWalletStatusResponse> checkLoadWalletStatus(
      String billRefNo) async {
    try {
      final returnData = await _remoteDataSource.checkLoadWalletStatus(
        billRefNo,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  ResultFuture<LoadWalletResponse> getLoadWalletDetails(
      String billRefNo) async {
    try {
      final returnData = await _remoteDataSource.getLoadWalletDetails(
        billRefNo,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  ResultFuture<LoadWalletResponse> loadToWallet(
      LoadWalletRequest request) async {
    try {
      final returnData = await _remoteDataSource.loadToWallet(
        request,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

 
}
