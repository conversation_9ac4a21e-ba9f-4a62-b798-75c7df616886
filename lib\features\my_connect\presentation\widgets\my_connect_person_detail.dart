import 'package:cbrs/core/common/widgets/custom_name_and_phone.dart';
import 'package:cbrs/core/common/widgets/custom_name_or_avatar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MyConnectPersonDetail extends StatelessWidget {
  const MyConnectPersonDetail({
    required this.avatar,
    required this.name,
    required this.phoneOrEmail,
    super.key,
  });

  final String avatar;
  final String name;
  final String phoneOrEmail;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CustomNameOrAvatar(name: name, avatar: avatar),
        SizedBox(width: 5.w),
        CustomNameAndPhone(name: name, phoneOrEmail: phoneOrEmail),
      ],
    );
  }
}
