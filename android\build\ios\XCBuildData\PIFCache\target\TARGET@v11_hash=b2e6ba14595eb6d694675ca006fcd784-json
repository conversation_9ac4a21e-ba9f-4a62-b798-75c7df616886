{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a225f915d6c43400fa8eb14d836d24a8", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8ffff8c4608b48a02b8e6dff7150e62", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98793fa2c01be377d2642a4cdc2e188717", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef1fc6d25699796b1f97b56600d50813", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98793fa2c01be377d2642a4cdc2e188717", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b5afe3ea1143ea5558fd7a311b1ef47e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e0e6e745262d0ccde46491803744e8ea", "guid": "bfdfe7dc352907fc980b868725387e98d6a9faf101b28e0cb6b0f6b28fd8ed44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f14babc2d250bdbb2e47bd83072aa234", "guid": "bfdfe7dc352907fc980b868725387e981f92bf70f178b27d9a3efa8ed140403f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982f000430adb083ec7cc5dc23620bf9cb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9842e8a132a7e5b2fbcae4f1a845cdfcbb", "guid": "bfdfe7dc352907fc980b868725387e98793caf8167e7011ce094e87f31b30b03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7b6beee0d91e8e1f52cba2ada4ce3db", "guid": "bfdfe7dc352907fc980b868725387e98263009124ff76943b278466d51a2be88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a1617ac3cee079010029438a09526dc", "guid": "bfdfe7dc352907fc980b868725387e98d9ad7a1fd83ab08a997b7463fbfa9a98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbe6da1348786b8c28573848ce9991bf", "guid": "bfdfe7dc352907fc980b868725387e98f4add278f5b9add1fb3a1d53f911946f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98460fb7140efc8907aa1faf813eafcb89", "guid": "bfdfe7dc352907fc980b868725387e98a6ee27c2d92bb828c22d4d5428d618c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853fbb7b5d926e34ea37a6629001607b6", "guid": "bfdfe7dc352907fc980b868725387e9830c714ca63519cd2b6fd4c3889cce027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98149b27718500ed84eb710ae3537fd1a0", "guid": "bfdfe7dc352907fc980b868725387e985ccb9cdf975d0a3aae85f5ed4a2331d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c298c62432e8ada1ba71a37547aa6979", "guid": "bfdfe7dc352907fc980b868725387e98e3e174dea831267a68d2d3fb9317e890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848773e2cdce306418d96567427d93420", "guid": "bfdfe7dc352907fc980b868725387e981edb9baa65b84c3d649b2eb6d67ecc52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9dac130789633ada4d4bb55a9b702b0", "guid": "bfdfe7dc352907fc980b868725387e989c9d8fd3ffbfaf3eea40189dc2343b9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ada3f4283294cb7a1da80bfd1fea42", "guid": "bfdfe7dc352907fc980b868725387e98e06f8ad587c8c6787c1a696d75887b60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870b2f9c6bbf6df9b3db57f5eee6b8b32", "guid": "bfdfe7dc352907fc980b868725387e98020f73c02cd7305c22ba22735f6269f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828d029ecb296fa5027f055e4961f7ded", "guid": "bfdfe7dc352907fc980b868725387e98aab974d220566b743a1495cfadc2e83d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceaf1c093c67342f7481bcca0c08c0ef", "guid": "bfdfe7dc352907fc980b868725387e9830d2019e22015de92bc026367a34b33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e3543d9444c900b34997d0a7bd13ad", "guid": "bfdfe7dc352907fc980b868725387e98065b2048d2e476a0abfdbd7d52ed8f3a"}], "guid": "bfdfe7dc352907fc980b868725387e987c966a3b129a240cdc79c574c521c055", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e98660c51cf828305bc3a5ec4c52aa8b13a"}], "guid": "bfdfe7dc352907fc980b868725387e98fa71aa902bf434f3999bc057159ce99f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988a175a6448755246720f963908648c4a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e983dbb0d5d79b94fc9af349ed668188ddd", "name": "flutter_contacts", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9844de8c75da5c0dc8b59260788428245e", "name": "flutter_contacts.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}