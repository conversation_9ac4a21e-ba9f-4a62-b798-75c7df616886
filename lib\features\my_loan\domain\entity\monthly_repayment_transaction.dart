import 'package:equatable/equatable.dart';

// this class is for success repayment response
class MonthlyRepaymentTransactionEntity extends Equatable {
  const MonthlyRepaymentTransactionEntity({
    required this.vat,
    required this.status,
    required this.elstRef,
    required this.bankCode,
    required this.bankName,
    required this.senderId,
    required this.billRefNo,
    required this.createdAt,
    required this.billAmount,
    required this.totalAmount,
    required this.connectRef,
    required this.senderName,
    required this.senderPhone,
    required this.beneficiaryId,
    required this.penaltyAmount,
    required this.serviceCharge,
    required this.interestAmount,
    required this.lastModifiedAt,
    required this.beneficiaryName,
    required this.facilitationFee,
 
    required this.transactionType,
    required this.transactionOwner,
    required this.originalCurrency,
    required this.authorizationType,
  });

  final double vat;
  final String status;
  final String elstRef;
  final String bankCode;
  final String bankName;
  final String senderId;
  final String billRefNo;
  final DateTime createdAt;
  final double billAmount;
  final double totalAmount;

  final String connectRef;
  final String senderName;
  final String senderPhone;
  final String beneficiaryId;
  final double penaltyAmount;
  final double serviceCharge;
  final double interestAmount;
  final DateTime lastModifiedAt;
  final String beneficiaryName;
  final double facilitationFee;

  final String transactionType;
  final String transactionOwner;
  final String originalCurrency;
  final String authorizationType;

  @override
  List<Object?> get props => [
        vat,
        status,
        elstRef,
        bankCode,
        bankName,
        senderId,
        billRefNo,
        createdAt,
        billAmount,
        totalAmount,
        connectRef,
        senderName,
        senderPhone,
        beneficiaryId,
        penaltyAmount,
        serviceCharge,
        interestAmount,
        lastModifiedAt,
        beneficiaryName,
        facilitationFee,
        transactionType,
        transactionOwner,
        originalCurrency,
        authorizationType,
      ];
}
