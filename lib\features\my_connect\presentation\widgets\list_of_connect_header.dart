import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/features/my_connect/presentation/views/add_connection_page.dart';
import 'package:cbrs/features/my_connect/presentation/views/scan_and_connect_with_qr_page.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/my_connect_btn_card.dart';
import 'package:flutter/material.dart';

class ListOfConnectHeader extends StatelessWidget {
  const ListOfConnectHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const CustomBuildText(
              text: 'List of Connects',
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
            const SizedBox(
              height: 4,
            ),
            CustomBuildText(
              text: 'Easily send money from your Connect List.',
              color: Colors.black.withValues(alpha: 128),
              fontSize: 10,
            ),
          ],
        ),
        const Spacer(),
        Container(
          child: InkWell(
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const ScanAndConnectWithQrPage(),
                ),
              );
            },
            child: const Icon(Icons.qr_code),
          ),
        ),
        const SizedBox(
          width: 12,
        ),
        MyConnectBtnCard(
          horizontalPadding: 23,
          bgColor: Theme.of(context).primaryColor,
             onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AddConnectionPage(),
                ),
              );
            },
          child: const CustomBuildText(
            text: 'Connect',
            color: Colors.white,
          ),
        ),
      ],
    );
  }
}
