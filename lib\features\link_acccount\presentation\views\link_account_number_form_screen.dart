import 'dart:async';

import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_phone_field.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/link_acccount/application_layer/bloc/link_account_bloc.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart';
import 'package:cbrs/features/send_money/domain/entities/bank.dart';

import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';

import 'package:country_flags/country_flags.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';

class LinkAccountNumberFormScreen extends StatefulWidget {
  const LinkAccountNumberFormScreen({
    required this.bank,
    super.key,
  });

  final Bank bank;

  @override
  State<LinkAccountNumberFormScreen> createState() =>
      _LinkAccountNumberFormScreenState();
}

class _LinkAccountNumberFormScreenState
    extends State<LinkAccountNumberFormScreen> {
  final _accountNumberController = TextEditingController();
  final _focusNode = FocusNode();
  bool _showRecipientCard = false;
  bool _isLoading = false;
  String? _recipientName;
  bool _isLoadingRecipient = false;
  double? _walletBalance;
  double? _exchangeRate;
  final _phoneInputKey = GlobalKey<CustomPhoneInputState>();
  String? _senderName;
  final bool _isTyping = false;
  bool _isLookupMode = true;
  bool _isLookupLoading = false;
  bool _isAccountLinkingMode = false;
  Timer? _debounceTimer;

  bool get _isValidAccount => _accountNumberController.text.length > 6;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _handleAccountLookup(
    String accountNumber,
    String bankCode,
  ) async {
    if (!mounted || !_focusNode.hasFocus) return;

    // Only proceed if account number meets minimum length
    if (accountNumber.length < (widget.bank.isWallet ? 9 : 3)) return;

    setState(() {
      _isLoadingRecipient = true;
      _showRecipientCard = false;
    });

    context.read<BankTransferBloc>().add(
          LookupAccountEvent(
            accountNumber: accountNumber,
            bankCode: bankCode,
          ),
        );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _accountNumberController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _handleContinue() {
    FocusScope.of(context).unfocus();

    // Validate account number
    if (_accountNumberController.text.isEmpty) return;

    final accountNumber = _accountNumberController.text;

    // Validate based on account type

    if (accountNumber.length < 3) {
      CustomToastification(
        context,
        message: 'Account number must be at least 3 digits',
        errorTitle: 'Invalid Input',
      );
      return;
    }

    if (_showRecipientCard && _recipientName != null) {
      // If we already have recipient info, proceed to next screen
      _onContinue();
    } else {
      // Otherwise perform lookup
      setState(() {
        _isLookupLoading = true;
        _isLookupMode = true;
        _isLoadingRecipient = true;
        _showRecipientCard = false;
      });

      context.read<BankTransferBloc>().add(
            LookupAccountEvent(
              accountNumber: accountNumber,
              bankCode: widget.bank.etSwitchCode,
            ),
          );
    }
  }

  void _onContinue() {
    FocusScope.of(context).unfocus();

    context.read<AccountLinkBloc>().add(
          GenerateLinkCodeEvent(
            bankID: widget.bank.id,
            accountNumber: _accountNumberController.text,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeColor = Theme.of(context).primaryColor;

    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, userState) {
         final userState = context.read<HomeBloc>().state;
    if (userState is HomeProfileLoadedState) {
      _senderName = userState.localUser.fullName;

    }

        return BlocConsumer<AccountLinkBloc, AccountLinkState>(
          listener: (context, state) async {
            if (state is GenerateCodeLoadedState) {
              debugPrint(state.toString());
              debugPrint(state.accountLink.success.toString());
              setState(
                () {
                  _isAccountLinkingMode = false;
                },
              );
              await context.pushNamed(
                AppRouteName.linkedAccountDetail,
                extra: {
                  'accountData': state.accountLink.accountData,
                  'bankLogo': widget.bank.logo ?? '',
                  'bankName': widget.bank.name,
                  'accountHolderName': _recipientName,
                  'accountNumber': _accountNumberController.text,
                  'linkCode':
                      state.accountLink.accountData.linkingCode.toString(),
                  'isFromPendingScreen': false,
                  'requestedDate': '',
                },
              );
            } else if (state is AccountLinkLoadingState) {
              debugPrint('AJVHSVAJVS');
              setState(
                () {
                  _isAccountLinkingMode = true;
                },
              );
            } else if (state is AccountLinkErrorState) {
              debugPrint('state messae: ${state.message}');
              setState(
                () {
                  _isAccountLinkingMode = false;
                },
              );
              CustomToastification(
                context,
                message: state.message,
              );
            }
          },
          builder: (context, userState) {
            return BlocConsumer<BankTransferBloc, BankTransferState>(
              listenWhen: (previous, current) {
                return current is AccountLookupSuccess ||
                    current is AccountLookupEmpty ||
                    current is BankTransferError;
              },
              listener: (context, state) {
                if (state is AccountLookupSuccess) {
                  FocusScope.of(context).unfocus();
                  setState(() {
                    _isLoadingRecipient = false;
                    _showRecipientCard = true;
                    _recipientName = state.account.accountInfo;

                    _isLookupLoading = false;
                    _isLookupMode = false;
                  });
                } else if (state is AccountLookupEmpty) {
                  setState(() {
                    _isLoadingRecipient = false;
                    _showRecipientCard = false;
                    _recipientName = null;

                    _isLookupMode = true;
                    _isLookupLoading = false;
                  });

                  CustomToastification(
                    context,
                    message: 'Please check the account number and try again',
                    errorTitle: 'Member not found',
                  );
                } else if (state is BankTransferLoading) {
                  setState(() {
                    _isLoadingRecipient = true;
                    _showRecipientCard = false;
                    _isLookupLoading = false;
                  });
                } else if (state is BankTransferError) {
                  setState(() {
                    _isLoading = false;
                    _isLoadingRecipient = false;
                    _showRecipientCard = false;

                    _isLookupLoading = false;
                  });

                  CustomToastification(
                    context,
                    message: state.message,
                  );
                }
              },
              builder: (context, state) => Scaffold(
                appBar: AppBar(
                  title: const Text(
                    'Link Account',
                  ),
                ),
                body: SafeArea(
                  // bottom: false,
                  child: GestureDetector(
                    onTap: () => FocusScope.of(context).unfocus(),
                    child: Column(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0.h),
                            child: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const CustomPageHeader(
                                    pageTitle: 'Enter Account Number',
                                    description:
                                        'Enter the account number for the selected bank to link it with Connect.',
                                  ),
                                  SizedBox(height: 16.h),
                                  CustomBuildText(
                                    text: 'Selected Bank',
                                    fontSize: 14.sp,
                                    color: const Color(0xFFAAAAAA),
                                  ),
                                  SizedBox(height: 4.h),
                                  buildBankDescription(),
                                  SizedBox(height: 16.h),
                                  CustomTextInput(
                                    hintText: 'Enter Account Number',
                                    inputLabel: 'Account Number',
                                    labelSize: 14,
                                    controller: _accountNumberController,
                                    onChanged: (value) {
                                      setState(() {
                                        _showRecipientCard = false;
                                      });
                                    },
                                  ),
                                  if (_isLookupLoading && _isLookupMode)
                                    _buildShimmerEffect()
                                  else if (_showRecipientCard &&
                                      _recipientName != null)
                                    GestureDetector(
                                      onTap: _handleContinue,
                                      child: RecipientCard(
                                        isBirrTransfer: true,
                                        name: _recipientName!,
                                        accountNumber:
                                            _accountNumberController.text,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16.w,
                            vertical: 24.h,
                          ),
                          child: CustomRoundedBtn(
                            btnText: _isLookupMode
                                ? 'Continue'
                                : _showRecipientCard && _recipientName != null
                                    ? 'Link Account'
                                    : 'Continue',
                            onTap: _isValidAccount ? _handleContinue : null,
                            isLoading: state is BankTransferLoading ||
                                _isAccountLinkingMode,
                            isBtnActive: _isValidAccount,
                          ),

                          /*
                          Center(
                            child: CustomButton(
                              text: _isLookupMode
                                  ? 'Continue'
                                  : _showRecipientCard && _recipientName != null
                                      ? 'Link Account'
                                      : 'Continue',
                              onPressed: _isValidAccount ? _handleContinue : null,
                              showLoadingIndicator:
                                  state is BankTransferLoading ||
                                      _isAccountLinkingMode,
                  // TODO258118181851
                              //  _isAccountLinkingMode ||
                              //     (_isLookupLoading && _isLookupMode),
                              options: CustomButtonOptions(
                                padding: EdgeInsets.symmetric(vertical: 16.h),
                                color: _isValidAccount
                                    ? themeColor
                                    : Colors.grey[300],
                                textStyle: GoogleFonts.outfit(
                                  fontSize: 16.h,
                                  fontWeight: FontWeight.w600,
                                  color: _isValidAccount
                                      ? Colors.white
                                      : Colors.grey[600],
                                ),
                                elevation: _isValidAccount ? 2 : 0,
                                borderRadius: BorderRadius.circular(30.r),
                              ),
                            ),
                          ),
                       */
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget buildBankDescription() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 16,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            height: 36.h,
            width: 36.w,
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).primaryColor),
              shape: BoxShape.circle,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(23.r),
              child: CachedNetworkImage(
                imageUrl: widget.bank.logo ?? '',
                fit: BoxFit.cover,
                width: 30.w,
                height: 30.h,
                placeholder: (context, url) => Container(
                  width: 30.w,
                  height: 30.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.red,
                  ),
                  child: const Icon(Icons.error, color: Colors.white),
                ),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          CustomBuildText(
            text: widget.bank.name,
            fontWeight: FontWeight.w600,
            fontSize: 14.sp,
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerEffect() {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 160,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: 120,
                  height: 14,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
