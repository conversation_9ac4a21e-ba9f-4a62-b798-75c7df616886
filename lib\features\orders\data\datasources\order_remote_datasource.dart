import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';

abstract class OrderRemoteDataSource {
  Future<Map<String, dynamic>> getOrders({
    bool? isBeneficiary,
    required int page,
    required int limit,
  });
  Future<Map<String, dynamic>> getOrderDetails(String orderId);
  Future<int> getUnredeemedCount();
  Future<String> getOrderReceipt(String billRefNo);
}

class OrderRemoteDataSourceImpl implements OrderRemoteDataSource {
  final ApiService apiService;
  final AuthLocalDataSource authLocalDataSource;

  OrderRemoteDataSourceImpl({
    required this.apiService,
    required this.authLocalDataSource,
  });

  @override
  Future<Map<String, dynamic>> getOrders({
    bool? isBeneficiary,
    required int page,
    required int limit,
  }) async {
    final queryParams = {
      'transactionType': 'merchant_payment',
      'billReason': 'Package',
      'page': page,
      'limit': limit,
      'sort': '-createdAt',
    };

    if (isBeneficiary != null) {
      queryParams['isBeneficiary'] = isBeneficiary.toString();
    }

    final result = await apiService.get(
      ApiEndpoints.memberTransactionsPaginate,
      parser: (data) => data as Map<String, dynamic>,
      queryParameters: queryParams,
      requiresAuth: true,
    );

    print("🎀🎊 🎀🎊Orders Response🎀🎊🎀🎊");
    print(result);

    return result.fold(
      (data) => data,
      (error) => throw ServerException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<Map<String, dynamic>> getOrderDetails(String orderId) async {
    final result = await apiService.get(
      ApiEndpoints.memberTransactionDetails(orderId),
      parser: (data) => data as Map<String, dynamic>,
      requiresAuth: true,
    );

    return result.fold(
      (data) => data,
      (error) => throw ServerException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }


//2223  0000 0000 0007
  @override
  Future<int> getUnredeemedCount() async {
    final result = await apiService.get(
      ApiEndpoints.unredeemedMessage,
      parser: (data) {
        final responseData = data as Map<String, dynamic>;
        return responseData['data'] as int;
      },
      requiresAuth: true,
    );

    return result.fold(
      (data) => data,
      (error) => throw ServerException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<String> getOrderReceipt(String billRefNo) async {
    final result = await apiService.get(
      ApiEndpoints.getOrderReceipt(billRefNo),
      parser: (data) {
        final responseData = data as Map<String, dynamic>;
        return responseData['invoiceURL'] as String;
      },
      requiresAuth: false,
    );

    return result.fold(
      (data) => data,
      (error) => throw ServerException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }
}
