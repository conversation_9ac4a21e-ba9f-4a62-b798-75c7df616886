part of 'miniapp_bloc.dart';

abstract class MiniappState extends Equatable {
  const MiniappState();
  @override
  List<Object?> get props => [];
}

class MiniappInitialState extends MiniappState {
  const MiniappInitialState();
}

class MiniappLoadingState extends MiniappState {
  const MiniappLoadingState();
  @override
  List<Object?> get props => [];
}

// for any errors during utitility
class MiniappErrorState extends MiniappState {
  const MiniappErrorState({required this.message});
  final String message;
  @override
  List<Object?> get props => [message];
}

// to fetch utilities
class GetLoadedMiniappsState extends MiniappState {
  const GetLoadedMiniappsState({required this.miniapp});
  final MiniappEntity miniapp;
  @override
  List<Object?> get props => [miniapp];
}

// gettting confirm
class ConfirmedMiniappState extends MiniappState {
  const ConfirmedMiniappState({required this.miniapp});
  final CreateOrderMiniappDataEntity miniapp;
  @override
  List<Object?> get props => [miniapp];
}

// submitting pin and getting success
class PinSubmittedState extends MiniappState {
  const PinSubmittedState({required this.miniapp});
  final MiniappSuccessDataEntity miniapp;
  @override
  List<Object?> get props => [miniapp];
}

// submitting otp and getting success
class OtpSubmittedState extends MiniappState {
  const OtpSubmittedState({required this.miniapp});
  final MiniappEntity miniapp;
  @override
  List<Object?> get props => [miniapp];
}

/*
enum UtilityStatus {
  initial,
  loading,
  loaded,
  error,
  processingPayment,
  paymentSuccess,
  paymentError,
  pinRequired,
  pinInvalid,
  transactionLoaded,
}

class UtilityState extends Equatable {
  final UtilityStatus status;
  final List<Utility> utilities;
  final String? errorMessage;
  final Map<String, dynamic>? paymentResponse;
  final UtilityTransaction? utilityTransactionConfirmResponse;

  final bool requiresOtp;
  final String? billRefNo;
  final String? transactionType;
  final UtilityTransaction? transaction;

  const UtilityState({
    required this.status,
    this.utilities = const [],
    this.errorMessage,
    this.paymentResponse,
    this.utilityTransactionConfirmResponse,
    this.requiresOtp = false,
    this.billRefNo,
    this.transactionType,
    this.transaction,
  });

  const UtilityState.initial()
      : this(
          status: UtilityStatus.initial,
          utilities: const [],
        );

  const UtilityState.loading()
      : this(
          status: UtilityStatus.loading,
          utilities: const [],
        );

  const UtilityState.loaded(List<Utility> utilities)
      : this(
          status: UtilityStatus.loaded,
          utilities: utilities,
        );

  const UtilityState.error(String message)
      : this(
          status: UtilityStatus.error,
          errorMessage: message,
        );

  const UtilityState.processingPayment()
      : this(
          status: UtilityStatus.processingPayment,
        );

  const UtilityState.paymentSuccess(UtilityTransaction response)
      : this(
          status: UtilityStatus.paymentSuccess,
          utilityTransactionConfirmResponse: response,
        );

  const UtilityState.paymentError(String message)
      : this(
          status: UtilityStatus.paymentError,
          errorMessage: message,
        );

  const UtilityState.pinRequired({
    required String billRefNo,
    required bool requiresOtp,
  }) : this(
          status: UtilityStatus.pinRequired,
          billRefNo: billRefNo,
          requiresOtp: requiresOtp,
        );

  const UtilityState.pinInvalid(String message)
      : this(
          status: UtilityStatus.pinInvalid,
          errorMessage: message,
        );

  const UtilityState.transactionLoaded(UtilityTransaction transaction)
      : this(
          status: UtilityStatus.transactionLoaded,
          transaction: transaction,
        );

  @override
  List<Object?> get props => [
        status,
        utilities,
        errorMessage,
        paymentResponse,
        requiresOtp,
        billRefNo,
        transaction,
      ];
}

*/
