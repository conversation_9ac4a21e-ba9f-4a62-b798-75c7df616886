import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/constants/mini_apps_urls.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/mini_apps/application/bloc/miniapp_bloc.dart';
import 'package:cbrs/features/mini_apps/data/models/miniapp_payment_model.dart';
// import 'package:cbrs/features/utility/data/repositories_impl/utility_payment_repository_impl.dart';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_confirm_page.dart';
import 'package:cbrs/main.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_success_screen.dart';
import 'package:cbrs/features/mini_apps/domain/entities/payment_request.dart';
import 'package:dio/dio.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:get_it/get_it.dart';

class MiniappWebview extends StatefulWidget {
  const MiniappWebview({
    required this.url,
    required this.appName,
    super.key,
  });
  final String url;
  final String appName;

  @override
  State<MiniappWebview> createState() => _MiniappWebviewState();
}

class _MiniappWebviewState extends State<MiniappWebview> {
  late final WebViewController _controller;
  // late final MiniappPaymentDataSource _paymentService;
  bool _isLoading = true;
  bool _hasError = false;
  int _retryCount = 0;
  static const int _maxRetries = 3;

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  void _initWebView() {
    if (widget.url.isEmpty) {
      setState(() => _hasError = true);
      return;
    }

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..enableZoom(true)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
            debugPrint('page startttted.');
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
            }
          },
          onPageFinished: (url) {
            debugPrint('page finishedddd');
            if (mounted) {
              setState(() => _isLoading = false);
            }
            // Inject the MiniAppChannel interface
            /*
            _controller.runJavaScript('''
              window.MiniAppChannel = {
                postMessage: function(message) {
                  try {
                    // Ensure message is properly stringified
                    let parsedMessage;
                    if (typeof message === 'string') {
                      try {
                        parsedMessage = JSON.parse(message);
                      } catch (e) {
                        parsedMessage = message;
                      }
                    } else {
                      parsedMessage = message;
                    }

                    // Convert old format to new format
                    const convertedMessage = {
                      action: parsedMessage.functionName === 'initiatePayment' ? 'PAYMENT_INITIATION' : parsedMessage.functionName,
                      data: parsedMessage.params?.orderPayload ? {
                        ...JSON.parse(JSON.stringify(parsedMessage.params.orderPayload)), // Ensure deep copy
                        headers: {
                          'x-api-key': parsedMessage.params['x-api-key'],
                          'datatoken': parsedMessage.params.datatoken,
                          'a-access-token': parsedMessage.params['a-access-token'],
                          'deviceuuid': parsedMessage.params.deviceuuid || 'test-device-123',
                          'sourceapp': parsedMessage.params.sourceapp || 'memberapp',
                          'x-transfer-flag': parsedMessage.params.x_transfer_flag || 'true'
                        }
                      } : parsedMessage.data
                    };

                    // Stringify the final message before sending to Flutter
                    window.flutter.postMessage(JSON.stringify(convertedMessage));
                  } catch (error) {
                    console.error('Error in MiniAppChannel.postMessage:', error);
                    window.flutter.postMessage(JSON.stringify({
                      action: 'ERROR',
                      data: {
                        message: 'Failed to process message: ' + error.message
                      }
                    }));
                  }
                },
                onMessage: function(callback) {
                  window.handleDataCallback = callback;
                }
              };
            ''');
         */
          },
          onWebResourceError: (error) {
            debugPrint('Web resource error: ${error.description}');
            if (mounted) {
              setState(() {
                _hasError = true;
                _retryCount++;
              });
            }
          },
          onNavigationRequest: (request) {
            if (MiniAppsConfig.isAllowedDomain(request.url)) {}
            launchUrl(
              Uri.parse(request.url),
              mode: LaunchMode.externalNonBrowserApplication,
            );
            return NavigationDecision.navigate;
            // Open non-allowed domains in external browser

            return NavigationDecision.prevent;
          },
        ),
      )
      ..addJavaScriptChannel(
        'MiniAppChannel',
        onMessageReceived: (JavaScriptMessage message) async {
          try {
            debugPrint('we are fine here $message');

            final payload =
                json.decode(message.message) as Map<String, dynamic>;
            debugPrint('Payload $payload');

            debugPrint("Payload params ${payload['params']}");
            debugPrint(
              "Payload order paylo ${payload['params']?['orderPayload']}",
            );
            debugPrint("Payload function aname ${payload['functionName']}");

            final action = payload['functionName'] as String;

            final data = payload['params'] as Map<String, dynamic>;

            debugPrint(' respose from web $data');
            switch (action) {
              case 'initiatePayment':
                await _handlePaymentInitiation(payload['params']);
              case 'NAVIGATION':
                _handleNavigation(data);
              case 'ERROR':
                _handleError(data);
              case 'PREPARE_ORDER_PAYLOAD':
                _handlePrepareOrderPayload(data);
              default:
                debugPrint('Unknown action: $action');
            }
          } catch (e) {
            debugPrint('Error processing message: $e');
            _sendErrorToWebApp('Failed to process request: $e');
          } finally {
            debugPrint('Take me plead');
          }
        },
      );
    debugPrint('hello dd');

    _loadWebView();
  }

  void _loadWebView() {
    try {
      _controller.loadRequest(
        Uri.parse(widget.url),
        headers: {
          'Content-Security-Policy': "frame-ancestors 'self'",
          'X-Frame-Options': 'SAMEORIGIN',
          'X-Content-Type-Options': 'nosniff',
          'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        },
      );
    } catch (e) {
      debugPrint('Error loading WebView: $e');
      if (mounted) {
        setState(() => _hasError = true);
      }
    }
  }

  Future<void> _handlePaymentInitiation(
    dynamic payLoad,
    //  Map<String, dynamic> payload,
  ) async {
    try {
      debugPrint('_handlePaymentInitiation');
      // Parse stringified data if needed

      // Map<String, dynamic> parsedData;
      // Map<String, dynamic> parsedData =
      //     payload['orderPayload'] as Map<String, dynamic>;

      // if (data is String) {
      //   parsedData = json.decode(data as String) as Map<String, dynamic>;
      // } else {
      //   parsedData = Map<String, dynamic>.from(data);
      // }

      // Extract headers from the parsed data
      // final headers = parsedData['headers'] as Map<String, dynamic>?;

      // Create payment request with parsed data
      // final paymentRequest = UtilityPaymentModel(
      //   stage: parsedData['stage'] as String? ?? 'UAT',
      //   timestamp: parsedData['timestamp'] as String ?? '',
      //   //DateTime.now().toIso8601String(),
      //   nonceStr: parsedData['nonce_str'] as String? ?? '',

      //   method: parsedData['method'] as String ?? '',
      //   bizContent: BizContentModel(
      //     notifyUrl: parsedData['notify_url'] as String?,
      //     tradeType: parsedData['trade_type'] as String?,
      //     appId: parsedData['appid'] as String?,
      //     merchCode: parsedData['merch_code'],
      //     merchOrderId: parsedData['merch_order_id'],
      //     title: parsedData['title'] as String?,
      //     totalAmount:
      //     (parsedData['total_amount'] as num?)?.toDouble() ?? 0.0,
      //     transCurrency: parsedData['trans_currency'] as String?,
      //     timeoutExpress: parsedData['timeout_express'] as String?,
      //     payeeIdentifier: parsedData['payee_identifier'],
      //     payeeIdentifierType: parsedData['payee_identifier_type'] as String?,
      //     payeeType: parsedData['payee_type'] as String?,
      //     utilityProvider: parsedData['utility_provider'] as String?,
      //     utilityData: UtilityDataModel(
      //       id: parsedData['id'] as String?,
      //       payerFullName: parsedData['payer_full_name'] as String?,
      //       reference: parsedData['reference'] as String?,
      //       serviceType: parsedData['serviceType'] as String?,
      //       cardNumber: parsedData['card_number'] as String?,
      //       packageTime: parsedData['package_time'] as int?,
      //       productCode: parsedData['product_code'] as String?,
      //       customerNumber: parsedData['customer_number'] as String?,
      //       orderId: parsedData['orderId'] as String?,
      //       narrative: parsedData['narrative'] as String?,
      //     ),
      //     additionalData: parsedData['additional_data'] as String?,
      //   ),
      //   sign: parsedData['sign'] as String? ??
      //       '', // Generate or handle missing sign
      //   confirmPayload: parsedData['confirmpayload'] as String? ?? '',
      //   dataToken: payload?['datatoken'] as String?,
      //   deviceUuid: payload?['deviceuuid'] as String?,
      //   sourceApp: payload?['sourceapp'] as String?,
      //   accessToken: payload?['a-access-token'] as String?,
      //   apiKey: payload?['x-api-key'] as String?,
      // );

      context.read<MiniappBloc>().add(
            CreatingOrderMiniappEvent(
              data: payLoad,
            ),
          );

/*
      final result = await
       _processUtilityPayment(payLoad);



      await result.fold(
        (failure) {
          // Send stringified error back to web
          _controller.runJavaScript('''
            if (typeof window.handleDataCallback === 'function') {
              window.handleDataCallback({
                success: false,
                error: ${json.encode(failure.message)}
              });
            }
          ''');
          _handleError({'message': "hh ${failure.message}"});
        },
        (response) async {
          debugPrint("How bekakkka ${response}");
          context.goNamed(AppRouteName.utilityConfirmation, extra: {
            "amount": response.amount
                .toString(), //response.amount.toStringAsFixed(2),
            "utilityType": response.transactionType,
            "accountNumber": '99',
            "senderName": response.senderName,
            "serviceProvider": response.recipientName,
            "serviceFee": response.serviceFee.toString(),
            "vat": response.vat.toString(),
            "total": response.total.toString(),
            "billRefNo": response.billRefNo,
            "transactionType": response.transactionType,

            /// TODO
          });
          // Send stringified success response back to web
          await _controller.runJavaScript('''
            if (typeof window.handleDataCallback === 'function') {
              window.handleDataCallback({
                success: true,
                data: ${json.encode(json.encode(response))} // Double encode to ensure proper stringification
              });
            }
          ''');

          if (mounted && context.mounted) {
            /*
            await Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => UtilitySuccessScreen(
                  amount: paymentRequest.bizContent.totalAmount.toString(),
                  miniAppName: widget.appName,
                  customerName:
                      paymentRequest.bizContent.utilityData.payerFullName ??
                          'N/A',
                  referenceNumber:
                      paymentRequest.bizContent.utilityData.reference ?? 'N/A',
                  serviceType:
                      paymentRequest.bizContent.utilityData.serviceType ??
                          'Utility Payment',
                  serviceFee: paymentRequest.bizContent.totalAmount * 0.02,
                  vat: paymentRequest.bizContent.totalAmount * 0.02 * 0.15,
                ),
              ),
            );

*/
          }
        },
      );
    */
    } catch (e) {
      // Send stringified error back to web
      _controller.runJavaScript('''
        if (typeof window.handleDataCallback === 'function') {
          window.handleDataCallback({
            success: false,
            error: ${json.encode(e.toString())}
          });
        }
      ''');
      _handleError({'message': 'Payment initiation failed: $e'});
    } finally {
      debugPrint('continue 😂😂😂😂😂');
    }
  }

  void _handleNavigation(Map<String, dynamic> data) {
    final url = data['url']?.toString() ?? '';
    if (url.isNotEmpty) {
      launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  void _handleError(Map<String, dynamic> data) {
    final message = data['message']?.toString() ?? 'An error occurred';
    if (mounted && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: GoogleFonts.outfit(color: Colors.white),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _sendErrorToWebApp(String errorMessage) {
    _controller.runJavaScript('''
      if (typeof window.handleDataCallback === 'function') {
        window.handleDataCallback({
          action: 'ERROR',
          data: {
            message: "ggg${errorMessage.replaceAll('"', r'\"')}"
          }
        });
      }
    ''');
  }

  void _handlePrepareOrderPayload(Map<String, dynamic> data) {
    try {
      final headers = data['headers'] as Map<String, dynamic>?;

      // Ensure data is properly stringified
      final requestBody = {
        'dataToken': data['dataToken'],
        'serviceType': data['serviceType'] ?? 'traffic_authority',
        'stage': data['stage'] ?? 'UAT',
      };

      final requestHeaders = {
        'Content-Type': 'application/json',
        'deviceuuid': headers?['deviceuuid'] ?? 'test-device-123',
        'sourceapp': headers?['sourceapp'] ?? 'memberapp',
        'x-transfer-flag': headers?['x_transfer_flag'] ?? 'true',
      };

      // Make API call to prepare order payload with proper stringification
      _controller.runJavaScript('''
        try {
          fetch("${data['url']}", {
            method: "${data['method'] ?? 'POST'}",
            headers: ${json.encode(requestHeaders)},
            body: ${json.encode(json.encode(requestBody))} // Double encode to ensure proper stringification
          })
          .then(response => response.text()) // Get response as text first
          .then(textResponse => {
            try {
              const jsonResponse = JSON.parse(textResponse);
              const aAccessToken = localStorage.getItem("sessionUpdated");
              
              // Prepare the response payload
              const responsePayload = {
                functionName: "initiatePayment",
                params: {
                  orderPayload: jsonResponse,
                  "x-api-key": "74a93bc1d226ad223c15febf8b4b2c5864764b4468312fe9",
                  datatoken: "${data['dataToken']}",
                  "a-access-token": aAccessToken,
                  callbackName: "handleDataCallback",
                  deviceuuid: "${requestHeaders['deviceuuid']}",
                  sourceapp: "${requestHeaders['sourceapp']}",
                  x_transfer_flag: "${requestHeaders['x-transfer-flag']}"
                }
              };

              // Send the stringified response back to Flutter
              if (typeof window.handleDataCallback === 'function') {
                window.handleDataCallback({
                  success: true,
                  data: JSON.stringify(responsePayload)
                });
              }
            } catch (parseError) {
              throw new Error('Failed to parse response: ' + parseError.message);
            }
          })
          .catch(error => {
            console.error('Error:', error);
            if (typeof window.handleDataCallback === 'function') {
              window.handleDataCallback({
                success: false,
                error: JSON.stringify(error.toString())
              });
            }
          });
        } catch (error) {
          console.error('Error in prepare order payload:', error);
          if (typeof window.handleDataCallback === 'function') {
            window.handleDataCallback({
              success: false,
              error: JSON.stringify(error.toString())
            });
          }
        }
      ''');
    } catch (e) {
      _handleError(
        {'message': 'Failed to prepare order payload: $e'},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        title: Text(
          widget.appName,
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () async {
            if (await _controller.canGoBack()) {
              await _controller.goBack();
            } else {
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            }
          },
        ),
      ),
      body: SafeArea(
        child: BlocConsumer<MiniappBloc, MiniappState>(
          listener: (context, state) {
            if (state is MiniappErrorState) {
              CustomToastification(context, message: state.message);
            }
            if (state is ConfirmedMiniappState) {
              debugPrint("state.utility ${state.miniapp}  ");
              context.pushNamed(
                AppRouteName.miniappConfirmation,
                extra: {
                  'confirm': state.miniapp,
                },
              );
            }
          },
          builder: (context, state) {
            return Stack(
              children: [
                if (!_hasError) WebViewWidget(controller: _controller),
                if (_isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
                if (_hasError && _retryCount < _maxRetries)
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Failed to load ${widget.appName}',
                          style: GoogleFonts.outfit(
                            fontSize: 16.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 16.h),
                        ElevatedButton(
                          onPressed: _loadWebView,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF065234),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(100.r),
                            ),
                          ),
                          child: Text(
                            'Retry',
                            style: GoogleFonts.outfit(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
