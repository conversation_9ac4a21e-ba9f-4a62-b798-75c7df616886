import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildWarning extends StatelessWidget {
  final bool hasMargin;
  final String? message;
  const BuildWarning({super.key, this.hasMargin = true, this.message});

  @override
  Widget build(BuildContext context) {
    return _buildWarning();
  }

  Widget _buildWarning() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      margin: hasMargin
          ? EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 16.h)
          : EdgeInsets.zero,
      decoration: ShapeDecoration(
        color: Color(0xFFFBF4CE),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
            side: BorderSide(width: 1.w, color: Color(0xFFF7E89A))),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            child: CircleAvatar(
              backgroundColor: Color(0xFFEBC400),
              radius: 16.w,
              child: Icon(
                Icons.warning,
                size: 16.w,
                // height: 16.h,
              ),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomBuildText(
                text: "Warning",
                fontWeight: FontWeight.w600,
                fontSize: 18.sp,
              ),
              SizedBox(height: 2.h),
              CustomBuildText(
                text: message ??
                    'You have exceeded the maximum number of attempts. Please request a new OTP after 1 min.',
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                color: Colors.black.withOpacity(0.4)
              ),
            ],
          )),
        ],
      ),
    );
  }
}
