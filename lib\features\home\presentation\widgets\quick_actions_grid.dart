import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class QuickActionsGrid extends StatelessWidget {
  const QuickActionsGrid({
    required this.walletType,
    super.key,
  });
  final String walletType;

  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 108.h,
      padding: EdgeInsets.fromLTRB(
        8.w,
        10.h,
        8.w,
        10.h,
      ),
      // margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 24,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: walletType == 'USD'
                ? _buildUSDActions(context)
                : _buildETBActions(context),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildETBActions(BuildContext context) {
    return [
      _buildActionItem(
        icon: MediaRes.bankIcon,
        label: 'Bank\nTransfer',
        hasColor: true,
        width: 29.w,
        height: 29.h,
        onTap: () {
          GlobalVariable.currentlySelectedWallet = 'ETB';

          context.pushNamed(
            AppRouteName.sendMoney,
            pathParameters: {'currency': 'etb'},
          );
        },
      ),
      _buildActionItem(
        icon: MediaRes.walletTransferIcon,
        label: 'Wallet\nTransfer',
        onTap: () {
          GlobalVariable.currentlySelectedWallet = 'ETB';
          context.pushNamed(
            AppRouteName.walletTransferPage,
            pathParameters: {'currency': 'etb'},
          );
        },
      ),
      _buildActionItem(
        icon: MediaRes.addMoneyIcon,
        label: 'Add\nMoney',
        hasColor: true,
        onTap: () {
          context.pushNamed(AppRouteName.addMoney);
        },
      ),
      _buildActionItem(
        icon: MediaRes.myBanksIcon,
        label: 'My\nBanks',
        width: 40.w,
        height: 40.h,
        onTap: () {
          context.pushNamed(AppRouteName.balanceCheck);
        },
      ),

/*
      
      _buildActionItem(
        icon: MediaRes.cashInOutIcon,
        label: 'Cash\nIn/Out',
        onTap: () {
          context.pushNamed(AppRouteName.cashInOut);
        },
      ),
      _buildActionItem(
        icon: MediaRes.addMoneyIcon,
        label: 'Add\nMoney',
        onTap: () {
          context.pushNamed(AppRouteName.addMoney);
        },
      ),
      */
    ];
  }

  List<Widget> _buildUSDActions(BuildContext context) {
    return [
      _buildActionItem(
        icon: MediaRes.bankIcon,
        label: 'Bank\nTransfer',
        onTap: () {
          // CustomToastification(context, message: "Error happended");

          GlobalVariable.currentlySelectedWallet = 'USD';

          context.pushNamed(
            AppRouteName.sendMoney,
            pathParameters: {'currency': 'usd'},
          );
        },
      ),
      _buildActionItem(
        icon: MediaRes.walletTransferIcon,
        label: 'Wallet\nTransfer',
        onTap: () {
          GlobalVariable.currentlySelectedWallet = 'USD';

          context.pushNamed(
            AppRouteName.walletTransferPage,
            pathParameters: {'currency': 'usd'},
          );
        },
      ),
      _buildActionItem(
        icon: MediaRes.changeToBirrIcon,
        label: 'Change\nTo ETB',
        onTap: () {
          GlobalVariable.currentlySelectedWallet = 'USD';

          context.pushNamed(AppRouteName.changeToBirr);
        },
      ),
      _buildActionItem(
        icon: MediaRes.loadToWalletIcon,
        width: 29.w,
        height: 29.h,
        label: 'Load\nWallet',
        onTap: () {
          GlobalVariable.currentlySelectedWallet = 'USD';

          context.pushNamed(AppRouteName.loadToWallet);
        },
      ),
    ];
  }

  Widget _buildActionItem({
    required String icon,
    required String label,
    required VoidCallback onTap,
    double? width,
    double? height,
    bool hasColor = false,
  }) {
    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap,
        child: AbsorbPointer(
          child: Container(
            // color: hasColor ? Colors.green :Colors.black,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 44.w,
                  height: 44.h,
                  decoration: BoxDecoration(
                    gradient: walletType == 'USD'
                        ? const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Color(0xFF0D451B), // Top color
                              Color(0xFF1A6B2F), // Bottom color
                            ],
                          )
                        : const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Color(0xFF69AC5C), // Primary color top
                              Color(0xFF085905), // Primary color bottom
                            ],
                          ),
                    // borderRadius: BorderRadius.circular(16.r),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Image.asset(
                      icon,
                      width: width ?? 30.w,
                      height: height ?? 30.h,
                      color: Colors.white,
                    ),
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  label,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.outfit(
                    color: const Color(0xff000000),
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
