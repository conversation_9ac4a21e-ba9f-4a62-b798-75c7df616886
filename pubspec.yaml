name: cbrs
description: "A new Remittance system"
publish_to: "none"
version: 1.1.0+29

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter

  # UI Components and Styling
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.2.0
  font_awesome_flutter: ^10.8.0
  fluentui_system_icons: ^1.1.263
  flutter_animate: ^4.5.0
  pinput: ^5.0.0

  url_launcher: ^6.3.1

  # Network and API
  dio: ^5.7.0
  cached_network_image: ^3.4.1
  connectivity_plus: ^5.0.2

  # State Management and Navigation
  flutter_bloc: ^8.1.6
  get: ^4.6.6
  get_it: ^7.6.0
  go_router: ^14.4.1

  image_picker: ^1.1.2
  permission_handler: ^12.0.0+1

  country_flags: ^3.0.0
  country_code_picker: ^3.1.0

  # Utils and Helpers
  dartz: ^0.10.1
  equatable: ^2.0.5
  hugeicons: ^0.0.7
  intl: ^0.19.0
  flutter_i18n:
    ^0.36.2
    # path: ../
  # flutter_localizations:
  #   sdk: flutter


  smooth_page_indicator: ^1.2.0+3
  country_picker: ^2.0.26
  email_validator: ^3.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.5
  calendar_date_picker2: ^1.1.7
  local_auth: ^2.3.0
  shared_preferences: ^2.3.3
  device_info_plus: ^11.1.1
  uuid: ^4.5.1
  google_fonts: ^6.0.0
  webview_flutter: ^4.10.0
  retry: ^3.1.2
  shimmer: ^3.0.0
  phone_numbers_parser: ^9.0.2
  flutter_contacts: ^1.1.7
  image_cropper: ^8.0.2
  youtube_player_flutter: ^9.1.1
  photo_view: ^0.15.0
  cloud_firestore: ^5.5.1
  firebase_core: ^3.8.1
  google_maps_flutter: ^2.10.0

  freezed_annotation: ^2.4.4
  http: ^1.2.2
  googleapis: ^13.2.0
  googleapis_auth: ^1.6.0
  firebase_messaging: ^15.1.5
  app_settings: ^5.1.1
  flutter_local_notifications:

  json_annotation: ^4.9.0
  # carousel_slider: ^4.2.1
  syncfusion_flutter_gauges: ^29.2.8
  flutter_screenutil: ^5.9.3
  share_plus: ^11.0.0
  animations: ^2.0.11

  super_tooltip: ^2.0.9
  toastification: ^2.3.0
  rxdart: ^0.28.0
  # rxdart: ^0.28.0
  mobile_scanner:
  image_gallery_saver_plus: ^4.0.1
  pretty_dio_logger: ^1.4.0

  phone_form_field:
    git:
      url: https://github.com/HenokMillion/phone_form_field.git
      branch: huawei

  # approov_service_flutter_httpclient:
  #   git:
  #     url: https://github.com/net-cyber/approov-service-flutter-httpclient-android.git
  #     branch: main
  chewie: ^1.8.5
  # video_player: ^2.9.2
  flutter_secure_storage: ^9.2.2
  flutter_native_splash: ^2.4.1
  package_info_plus: ^8.1.2
  flutter_widget_from_html: ^0.15.3
  android_id: ^0.4.0
  flutter_html: ^3.0.0-beta.2
  carousel_slider: ^5.0.0
  widget_to_marker: ^1.0.6
  geolocator:
  flutter_file_dialog: ^3.0.2
  qr_flutter: ^4.1.0
  flutter_native_contact_picker: ^0.0.10

  lottie: ^3.3.1

  gif: ^2.3.0
  video_player: ^2.9.5
  scrollable_positioned_list: ^0.3.8
  confetti: ^0.8.0
  another_flushbar: ^1.12.30
  leak_tracker: ^10.0.8

  camerawesome: ^2.4.0
  qr_code_tools: ^0.2.0
  syncfusion_flutter_pdfviewer: ^29.2.11
  fast_contacts: ^4.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Development Tools
  build_runner: ^2.4.6
  flutter_lints: ^4.0.0
  very_good_analysis: ^6.0.0
  # sentry_flutter: ^8.9.0

  # App Configuration
  flutter_launcher_icons: ^0.13.1
  hive_generator: ^2.0.0
  freezed: ^2.5.7
  json_serializable: ^6.9.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/

    - assets/vectors/
    - assets/icons/
    - assets/json/

    - assets/i18n_namespace/en/
    - assets/i18n_namespace/am/
    - assets/i18n_namespace/ti/
    - assets/i18n_namespace/so/
    - assets/i18n_namespace/om/
    - assets/animations/
