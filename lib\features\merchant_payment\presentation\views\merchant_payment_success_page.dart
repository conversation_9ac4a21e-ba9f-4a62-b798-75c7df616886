import 'package:cbrs/core/common/widgets/success/custom_transaction_success_row.dart';
import 'package:cbrs/core/common/widgets/success/custom_transaction_success_screen.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class MerchantPaymentSuccessPage extends StatelessWidget {
  const MerchantPaymentSuccessPage({
    required this.billRefNo,
    required this.amount,
    required this.merchantName,
    required this.senderName,
    required this.serviceCharge,
    required this.vat,
    required this.total,
    super.key,
  });
  final String billRefNo;
  final String amount;
  final String merchantName;
  final String senderName;
  final double serviceCharge;
  final double vat;
  final double total;


  Widget _buildTransactionDetail(
    BuildContext context,
    String label,
    String value, {
    bool isLast = false,
    bool hasCurrency = false,
  }) {
    return CustomTransactionSuccessRow(
      label: label,
      value: value,
      isLast: isLast,
      hasCurrency: hasCurrency,
      isBirrTransfer: true,
    );
  }

  Widget _buildTransactionDetails(BuildContext context) {
    return Column(
      children: [
        _buildTransactionDetail(context, 'Sender Name ', senderName),
        _buildTransactionDetail(context, 'Merchant Name', merchantName),
        _buildTransactionDetail(context, 'Payment Reference', billRefNo),
        _buildTransactionDetail(
          context,
          'Payment Date',
          DateFormat('MMMM dd, yyyy').format(DateTime.now()),
        ),
        _buildTransactionDetail(context, 'Amount in birr', '$amount ETB'),
        _buildTransactionDetail(context, 'Service Fee', '$serviceCharge ETB'),
        _buildTransactionDetail(context, 'VAT(15%)', '$vat ETB'),
        _buildTransactionDetail(context, 'Total', '${total.toStringAsFixed(2)} ETB', isLast: true),
     
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.sizeOf(context);

    return CustomTransactionSuccessScreen(
      alwaysShowETBColor: true,
      pageDescription: 'Your transfer has been completed successfully.',
      totalAmount: double.parse(amount),
      billRefNo: billRefNo,
      
      child: _buildTransactionDetails(context),
      onQrTap: () {},
      onShareTap: () {},
      onGetRecieptTap: () {},
    );
    /*
    
     AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark.copyWith(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
      child: 
      
      
      
      Scaffold(
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.only(bottom: 80.h),
            child: Center(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(16.h),
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                    decoration: BoxDecoration(
                      color: const Color(0xFFDCF0E8),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(
                          'assets/images/success_image.png',
                          height: 84.h,
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          '$amount Birr',
                          style: GoogleFonts.outfit(
                            fontSize: _getResponsiveFontSize(context, 34),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          'Payment Successful!',
                          style: GoogleFonts.outfit(
                            fontSize: _getResponsiveFontSize(context, 24),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 36.h),
                        Container(
                          width: double.infinity,
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Transaction Details',
                            style: GoogleFonts.outfit(
                              fontSize: _getResponsiveFontSize(context, 18),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        SizedBox(height: 12.h),
                        _buildTransactionDetail(
                            context, 'Sender Name', senderName),
                        _buildTransactionDetail(
                            context, 'Merchant Name', merchantName),
                        _buildTransactionDetail(
                            context, 'Payment Reference', billRefNo),
                        _buildTransactionDetail(context, 'Payment Date',
                            DateFormat('MMMM dd, yyyy').format(DateTime.now())),
                        _buildTransactionDetail(
                            context, 'Amount in birr', '$amount Birr'),
                        _buildTransactionDetail(
                            context, 'Service Fee', '$serviceCharge Birr'),
                        _buildTransactionDetail(
                            context, 'VAT(15%)', '$vat Birr'),
                        SizedBox(height: 8.h),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenSize.width * 0.03,
                          ),
                          height: 1,
                          child: CustomPaint(
                            painter: DottedLinePainter(),
                            size: const Size(double.infinity, 1),
                          ),
                        ),
                        SizedBox(height: screenSize.height * 0.01),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Total Amount',
                              style: GoogleFonts.outfit(
                                fontSize: 20.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Flexible(
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  '$total Birr',
                                  style: GoogleFonts.outfit(
                                    fontSize: 20.sp,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        floatingActionButton: Padding(
          padding: EdgeInsets.fromLTRB(
            screenSize.width * 0.04,
            0,
            screenSize.width * 0.04,
            MediaQuery.of(context).viewInsets.bottom > 0 ? 24 : 0,
          ),
          child: CustomButton(
            text: 'Back to Home',
            onPressed: () => context.go(AppRouteName.home),
            options: CustomButtonOptions(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              color: Theme.of(context).primaryColor,
              textStyle: GoogleFonts.outfit(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              borderRadius: BorderRadius.circular(32.r),
            ),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      ),
    );
  
  */
  }
}
