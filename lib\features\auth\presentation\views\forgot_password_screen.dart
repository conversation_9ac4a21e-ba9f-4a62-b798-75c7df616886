import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_phone_field.dart';
import 'package:cbrs/core/common/widgets/custom_tab_bar_button.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  String _selectedCountryCode = '+251';
  final _phoneController = TextEditingController();
  final _phoneInputKey = GlobalKey<CustomPhoneInputState>();
  bool _isFormValid = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {});
      _validateForm();
    });

    // Add listeners to controllers
    _emailController.addListener(_validateForm);
    _phoneController.addListener(_validateForm);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  void _validateForm() {
    if (_formKey.currentState == null) return;

    setState(() {
      if (_tabController.index == 1) {
        // Email validation
        final email = _emailController.text.trim();
        final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
        _isFormValid = email.isNotEmpty && emailRegex.hasMatch(email);

        // Dismiss keyboard if email is valid
        if (_isFormValid) {
          FocusManager.instance.primaryFocus?.unfocus();
        }
      } else {
        // Phone validation
        final phoneField = _phoneInputKey.currentState;
        if (phoneField != null) {
          final phoneController = phoneField.phoneFieldController;
          final phoneNumber = phoneController.value;

          _isFormValid =
              phoneNumber.isValid() && _phoneController.text.isNotEmpty;

          if (_isFormValid && phoneNumber.countryCode == '251') {
            final nationalNumber = phoneNumber.nsn;
            _isFormValid = nationalNumber.length >= 9 &&
                nationalNumber.length <= 12 &&
                (nationalNumber.startsWith('9') ||
                    nationalNumber.startsWith('7'));
          }
        } else {
          _isFormValid = false;
        }
      }
    });
  }

  Widget _buildInputField({
    required String hintText,
    required FormFieldValidator<String> validator,
    required FormFieldSetter<String> onSaved,
    required ThemeData theme,
    bool isRequired = false,
    TextInputType? keyboardType,
    Widget? prefixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: hintText,
                style: GoogleFonts.plusJakartaSans(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: GoogleFonts.plusJakartaSans(
                    color: Colors.red,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        CustomTextFormField(
          validator: (value) {
            if (isRequired && (value == null || value.isEmpty)) {
              return 'This field is required';
            }
            return validator(value);
          },
          onSaved: onSaved,
          textInputType: keyboardType,
          style: GoogleFonts.plusJakartaSans(
            color: theme.colorScheme.onSurface,
          ),
          fillColor: theme.colorScheme.onTertiary,
          borderRadius: 15,
          maxLines: 1,
          prefixIcon: prefixIcon,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: GoogleFonts.outfit(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: theme.colorScheme.onTertiary,
            contentPadding:
                const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Forgot Password',
          style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w500),
        ),
      ),
      // BuildCustomAppBar(context: context, title: 'Forgot Password'),
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          debugPrint('\n=== AUTH STATE CHANGED ===');
          debugPrint('Current state: ${state.runtimeType}');

          if (state is AuthError) {
            debugPrint('Error occurred: ${state.message}');
            CustomToastification(context, message: state.message);

            if (state.message.toLowerCase().contains('account not found')) {
              _emailController.clear();
              _formKey.currentState?.reset();
            }
          } else if (state is EmailVerificationSentState) {
            debugPrint('Email verification sent successfully');
            try {
              if (!state.response.success)
                CustomToastification(
                  context,
                  message: state.response.message,
                  isError: !state.response.success,
                );

              if (state.response.success) {
                context.go(
                  AppRouteName.verifyEmail,
                  extra: {
                    'email': _emailController.text.trim(),
                    'source': 'forgot_password',
                  },
                );
              }
            } catch (e) {
              debugPrint('Navigation error: $e');
              CustomToastification(context, message: 'Please try again Later');
            }
          } else if (state is OtpSentState) {
            debugPrint('OTP sent successfully');

            if (!state.response.success)
              CustomToastification(
                context,
                message: state.response.message,
                isError: !state.response.success,
              );

            final formattedNumber =
                '$_selectedCountryCode${_phoneController.text}';
            debugPrint('Navigating to OTP screen with phone: $formattedNumber');

            try {
              if (state.response.success) {
                context.goNamed(
                  'verifyOtp',
                  extra: {
                    'phoneNumber': formattedNumber,
                    'source': 'forgot_password',
                  },
                );
              }
            } catch (e) {
              debugPrint('Navigation error: $e');

              CustomToastification(context, message: 'Please try again Later');
            }
          } else if (state is PinResetRequestedState) {
            debugPrint('hellow');
            final formattedNumber =
                '$_selectedCountryCode${_phoneController.text}';

            CustomToastification(
              context,
              message: state.response.message,
              isError: !state.response.success,
            );

            if (state.response.success) {
              context.goNamed(
                'verifyOtp',
                extra: {
                  'phoneNumber': formattedNumber,
                  'source': 'forgot_password',
                },
              );
            }
          }
        },
        builder: (context, state) {
          debugPrint('Rebuilding UI with state: ${state.runtimeType}');
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus(); // Hide the keyboard
            },
            child: Stack(
              fit: StackFit.expand, // Make stack fill entire space
              children: [
                SingleChildScrollView(
                  child: Container(
                    padding: EdgeInsets.only(
                      top: 16.h,
                      left: 16.0.w,
                      right: 16.0.w,
                      bottom: 16.0.w,
                    ),
                    height: MediaQuery.sizeOf(context).height -
                        kToolbarHeight -
                        (Platform.isIOS ? 16 : 30.h),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Center(
                            child: Image.asset(
                              MediaRes.connectBirrMainLogo,
                              height: 130.h,
                            ),
                          ),
                          SizedBox(height: 20.h),
                          Text(
                            'Forgot PIN',
                            style: GoogleFonts.outfit(
                              fontSize: 24.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'To reset your PIN, please enter your email or phone number below and verify it first.',
                            style: GoogleFonts.outfit(
                              fontSize: 18.sp,
                              color: const Color(0xFFAAAAAA),
                              height: 1.5,
                            ),
                          ),
                          SizedBox(height: 30.h),
                          Container(
                            decoration: BoxDecoration(
                              color: theme.colorScheme.onTertiary,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: CustomButtonTabBar(
                              controller: _tabController,
                              labelColor: Colors.white,
                              unselectedLabelColor: theme.primaryColor,
                              backgroundColor: theme.primaryColor,
                              unselectedBackgroundColor:
                                  theme.colorScheme.onTertiary,
                              borderRadius: 12.r,
                              labelStyle: GoogleFonts.outfit(
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w500,
                              ),
                              unselectedLabelStyle: GoogleFonts.outfit(
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w500,
                              ),
                              tabs: const [
                                Tab(text: 'Phone Number'),
                                Tab(text: 'Email'),
                              ],
                            ),
                          ),
                          SizedBox(height: 32.h),
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child: _tabController.index == 1
                                ? _buildEmailForm(theme)
                                : _buildPhoneForm(theme),
                            transitionBuilder:
                                (Widget child, Animation<double> animation) {
                              return FadeTransition(
                                opacity: animation,
                                child: child,
                              );
                            },
                          ),
                          const Spacer(),
                          Column(
                            children: [
                              SafeArea(
                                child: CustomButton(
                                  text: 'Reset PIN',
                                  showLoadingIndicator: state is AuthLoading,
                                  onPressed: !_isFormValid
                                      ? null // Button will be disabled when form is invalid
                                      : () {
                                          if (_formKey.currentState
                                                  ?.validate() ??
                                              false) {
                                            final email =
                                                _emailController.text.trim();

                                            if (_tabController.index == 1) {
                                              debugPrint(
                                                '✉️ Initiating forgot PIN with email: $email',
                                              );
                                              context.read<AuthBloc>().add(
                                                    ForgotPinEvent(
                                                        email: email),
                                                  );
                                            } else {
                                              final formattedNumber =
                                                  '$_selectedCountryCode${_phoneController.text}';
                                              debugPrint(
                                                'Initiating forgot PIN with phone: $formattedNumber',
                                              );
                                              context.read<AuthBloc>().add(
                                                    ForgotPinWithPhoneEvent(
                                                      phoneNumber:
                                                          formattedNumber,
                                                    ),
                                                  );
                                            }
                                          }
                                        },
                                  options: CustomButtonOptions(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 16.h),
                                    color: _isFormValid
                                        ? theme.primaryColor
                                        : Colors.grey[300],
                                    loadingColor: Colors.white,
                                    loadingSize: 24.0.h,
                                    loadingStrokeWidth: 2.5.w,
                                    textStyle: GoogleFonts.outfit(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                      color: _isFormValid
                                          ? Colors.white
                                          : Colors.grey[500],
                                    ),
                                    borderRadius: BorderRadius.circular(32.r),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: Platform.isIOS ? 24 : 16.h,
                          ),
                          // const SizedBox(height: 80),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmailForm(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Email',
          style: GoogleFonts.outfit(
            color: Colors.grey.shade600,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
        SizedBox(
          height: 8.h,
        ),
        CustomTextFormField(
          controller: _emailController,
          validator: (value) {
            debugPrint('\n=== VALIDATING EMAIL ===');
            if (value == null || value.trim().isEmpty) {
              debugPrint('❌ Email validation failed: Empty input');
              return 'Email is required';
            }

            final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
            if (!emailRegex.hasMatch(value.trim())) {
              debugPrint('❌ Email validation failed: Invalid format');
              return 'Please enter a valid email address';
            }

            debugPrint('✅ Email validation passed: ${value.trim()}');
            return null;
          },
          textInputType: TextInputType.emailAddress,
          style: GoogleFonts.plusJakartaSans(
            color: theme.colorScheme.onSurface,
          ),
          fillColor: theme.colorScheme.onTertiary,
          borderRadius: 15.r,
          maxLines: 1,
          prefixIcon: Icon(
            Icons.email_outlined,
            color: theme.primaryColor,
          ),
          decoration: InputDecoration(
            hintText: 'Enter Email Address',
            hintStyle: GoogleFonts.outfit(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
              fontSize: 16.sp,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: theme.colorScheme.onTertiary,
            contentPadding: EdgeInsets.symmetric(
              vertical: 15.h,
              horizontal: 20.w,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneForm(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormField<String>(
          validator: (value) {
            if (_phoneController.text.isEmpty) {
              return 'Phone number is required';
            }
            return null;
          },
          builder: (FormFieldState<String> field) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomPhoneInput(
                  key: _phoneInputKey,
                  initialCountryCode: _selectedCountryCode,
                  phoneController: _phoneController,
                  onCountrySelected: (dialCode, isoCode) {
                    setState(() {
                      _selectedCountryCode = dialCode;
                    });
                  },
                  onChange: (value) {
                    // This will trigger form validation on phone number change
                    _validateForm();
                  },
                  validator: (_) => null,
                  theme: theme,
                  isRequired: true,
                  validateOnChange: true,
                ),
                if (field.hasError)
                  Padding(
                    padding: EdgeInsets.only(top: 8.h, left: 16),
                    child: Text(
                      field.errorText!,
                      style: GoogleFonts.outfit(
                        fontSize: 12,
                        color: Colors.red,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
    );
  }
}
