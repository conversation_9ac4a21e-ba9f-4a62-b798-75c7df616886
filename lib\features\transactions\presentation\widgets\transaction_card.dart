import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TransactionCard extends StatelessWidget {
  const TransactionCard({
    required this.title,
    required this.transactionType,
    required this.onTap,
    required this.amount,
    required this.date,
    required this.currentUserId,
    required this.senderId,
    required this.beneficiaryId,
    required this.transaction,
    super.key,
  });
  final String title;
  final String transactionType;
  final VoidCallback onTap;
  final String date;
  final String amount;
  final String currentUserId;
  final String senderId;
  final String beneficiaryId;
  final Transaction transaction;

  bool get isCurrentUserSender => currentUserId == senderId;
  bool get isWalletTransfer =>
      transactionType.toLowerCase() == 'wallet_transfer';

  String _formatAmount() {
    if (!isWalletTransfer) return amount;

    return isCurrentUserSender ? '-$amount' : '+$amount';
  }

  Color _getAmountColor(BuildContext context) {
    if (!isWalletTransfer)
      return Theme.of(context).textTheme.bodyMedium!.color!;

    return isCurrentUserSender ? Color(0xFFD70808) : Theme.of(context).primaryColor;
  }

  String _formatDate(String dateStr) {
    try {
      final dateTime = DateTime.parse(dateStr);
      return DateFormat('MMM dd, yyyy').format(dateTime);
    } catch (e) {
      return dateStr; // Return original string if parsing fails
    }
  }

  String _formatTransactionType(String type) {
    // Split by underscore or hyphen
    final words = type.split(RegExp('[_-]'));
    // Capitalize each word and join with spaces
    return words
        .map((word) => word[0].toUpperCase() + word.substring(1).toLowerCase())
        .join(' ');
  }

  @override
  Widget build(BuildContext context) {
    // final imageUrl = transactionType == 'wallet_transfer'
    //     ? 'assets/vectors/wallet_transfer_dollar_4x_img.png'
    //     : transactionType == 'change_to_birr'
    //         ? 'assets/vectors/change_to_birr_4x_img.png'
    //         : 'assets/vectors/load_by_wallet_4x_img.png';

    // debugPrint(
    //     " transactionType $transactionType==currentUser $currentUserId == Sender $senderId =====  $beneficiaryId");

    final imageUrl = getTransactionTypeIcon(transactionType);
    final hasArrowIcon =
        transactionType != 'change_to_birr' && transactionType != 'package';

    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.white,
        width: double.infinity,
        child: Row(
          children: [
            BlocBuilder<HomeBloc, HomeState>(
              builder: (context, state) {
                final isUSD =
                    state is HomeLoaded && state.data.selectedWallet == 'USD';

                return Container(
                  padding: EdgeInsets.all(16.h),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,

                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    //  isUSD
                    //     ? const Color(0xFFE6EDF8)
                    //     : const Color(0xFFE9F8F2),
                  ),
                  child: Image.asset(
                    imageUrl,
                    width: 30.w,
                    height: 30.h,
                    fit: BoxFit.cover,

                    // color: isUSD
                    //     ? const Color(0xFF0D3C89)
                    //     : const Color(0xFF006837),
                  ),
                );
              },
            ),
            SizedBox(width: 8.w),
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBuildText(
                    text: transactionType == 'load_to_wallet'
                        ? transaction.senderName
                        : title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    // textScaler: const TextScaler.linear(1),
                    // style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    //       fontSize: 16.sp,
                    //       fontWeight: FontWeight.bold
                    //     ),
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    children: [
                      Image.asset(
                        isCurrentUserSender && transactionType != 'add_money'
                            ? MediaRes.arrowUpFlow
                            : MediaRes.arrowDownFlow,
                        width: 20.w,
                        height: 20.h,
                        color: isCurrentUserSender &&
                                transactionType != 'add_money'
                            ? Color(0xFFD70808)
                            : Theme.of(context).primaryColor,
                      ),
                      Text(
                        textScaler: const TextScaler.linear(1),
                        _formatTransactionType(transactionType),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: GoogleFonts.outfit(
                          color: Colors.grey.withOpacity(0.7),
                          fontSize: 14.sp,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Text(
                          _formatAmount(),
                          textAlign: TextAlign.end,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.outfit(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w700,
                            color: isCurrentUserSender
                                ? Color(0xFFD70808)
                                : Theme.of(context).primaryColor,

                            // _getAmountColor(context),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    _formatDate(date),
                    style: GoogleFonts.outfit(
                      color: Colors.grey.withOpacity(0.7),
                      fontSize: 14.sp,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String getTransactionTypeIcon(String transactionType) {
    switch (transactionType) {
      case 'bank_transfer':
        return MediaRes.bankIcon;
      case 'wallet_transfer':
        return MediaRes.walletTransferIcon;
      case 'cash_pickup':
        return MediaRes.ordersIcon;
      case 'top_up':
        return MediaRes.mobileTopUpIcon;

      case 'wallet_prefund':
        return MediaRes.walletTransferIcon;
      case 'load_to_wallet':
        return MediaRes.loadToWalletIcon;
      case 'add_money':
        return MediaRes.addMoneyIcon;
      case 'change_to_birr':
        return MediaRes.changeToBirrIcon;

      case 'guest_send_money':
        return MediaRes.walletTransferIcon;

      case 'gift_package':
        return MediaRes.sentGiftCardIcon;

      case 'redeem':
        return MediaRes.sentGiftCardIcon;

      case 'money_request':
        return isCurrentUserSender
            ? MediaRes.walletTransferIcon
            : MediaRes.walletTransferReceivedIcon;

      default:
        return MediaRes.changeToBirrIcon;
    }
  }
}
