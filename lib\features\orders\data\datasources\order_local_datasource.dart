import 'package:shared_preferences/shared_preferences.dart';
import '../models/order_model.dart';
import '../models/order_detail_model.dart';

abstract class OrderLocalDataSource {
  Future<void> cacheOrders(List<OrderModel> orders);
  Future<List<OrderModel>> getLastOrders();
  Future<void> cacheOrderDetail(OrderDetailModel orderDetail);
  Future<OrderDetailModel?> getLastOrderDetail(String orderId);
  Future<void> clearCache();
}

class OrderLocalDataSourceImpl implements OrderLocalDataSource {
  final SharedPreferences sharedPreferences;
  static const CACHED_ORDERS = 'CACHED_ORDERS';
  static const CACHED_ORDER_DETAIL_PREFIX = 'CACHED_ORDER_DETAIL_';

  OrderLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<void> cacheOrders(List<OrderModel> orders) async {
    final List<String> jsonOrders =
        orders.map((order) => order.toJson().toString()).toList();
    await sharedPreferences.setStringList(CACHED_ORDERS, jsonOrders);
  }

  @override
  Future<List<OrderModel>> getLastOrders() async {
    final jsonOrders = sharedPreferences.getStringList(CACHED_ORDERS);
    if (jsonOrders != null) {
      return jsonOrders
          .map((jsonOrder) =>
              OrderModel.fromJson(Map<String, dynamic>.from(jsonOrder as Map)))
          .toList();
    }
    return [];
  }

  @override
  Future<void> cacheOrderDetail(OrderDetailModel orderDetail) async {
    final String key = CACHED_ORDER_DETAIL_PREFIX + orderDetail.id;
    await sharedPreferences.setString(key, orderDetail.toJson().toString());
  }

  @override
  Future<OrderDetailModel?> getLastOrderDetail(String orderId) async {
    final String key = CACHED_ORDER_DETAIL_PREFIX + orderId;
    final String? jsonOrderDetail = sharedPreferences.getString(key);
    if (jsonOrderDetail != null) {
      return OrderDetailModel.fromJson(
          Map<String, dynamic>.from(jsonOrderDetail as Map));
    }
    return null;
  }

  @override
  Future<void> clearCache() async {
    final keys = sharedPreferences.getKeys();
    for (final key in keys) {
      if (key.startsWith(CACHED_ORDER_DETAIL_PREFIX) || key == CACHED_ORDERS) {
        await sharedPreferences.remove(key);
      }
    }
  }
}
