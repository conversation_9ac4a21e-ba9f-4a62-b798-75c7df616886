import 'package:cbrs/features/profile/data/models/profile_dto.dart';
import 'package:equatable/equatable.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object?> get props => [];
}

class LoadHomeDataEvent extends HomeEvent {}

class UpdateWalletSelection extends HomeEvent {
  const UpdateWalletSelection(this.wallet);
  final String wallet;

  @override
  List<Object?> get props => [wallet];
}

class ToggleAmountVisibility extends HomeEvent {}

class RefreshBalanceEvent extends HomeEvent {}

class LoadRecentTransfersEvent extends HomeEvent {}

class UpdateProfileEvent extends HomeEvent {
  const UpdateProfileEvent(this.profile);
  final ProfileModel profile;

  @override
  List<Object?> get props => [profile];
}

class RefreshHomeData extends HomeEvent {
  @override
  List<Object?> get props => [];
}

class FetchWalletBalance extends HomeEvent {
  @override
  List<Object?> get props => [];
}

class LoadEssentialDataEvent extends HomeEvent {
  const LoadEssentialDataEvent({required this.walletType});
  final String walletType;
  @override
  List<Object?> get props => [walletType];
}

class FetchExchangeRate extends HomeEvent {
  @override
  List<Object?> get props => [];
}

class FetchWalletEvent extends HomeEvent {
  const FetchWalletEvent({this.isUsdWallet = true, this.forceTheme = true});
  final bool isUsdWallet;
  final bool forceTheme;
  @override
  List<Object?> get props => [];
}

class WalletSwitchingEvent extends HomeEvent {
  const WalletSwitchingEvent({
    required this.walletType,
    required this.etbBalance,
    required this.usdBalance,
  });
  final String walletType; // usd, etb
  final double etbBalance;
  final double usdBalance;

  @override
  List<Object?> get props => [];
}

class HomeProfileFetchingEvent extends HomeEvent {
  const HomeProfileFetchingEvent();
}
