{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989a3790132ef68d6167c1a9c7b0ae419a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f8a9a3f9b4078ed1fb39ca834eb6fc4a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b9f5d542ba145c2ec211016b990cf76", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984061cd59d054d486cd6edaba96dca313", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b9f5d542ba145c2ec211016b990cf76", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989e8a9a535b6b8da413787d2f9414a0c4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c69605cb69cfbacde95c1a8977496d96", "guid": "bfdfe7dc352907fc980b868725387e9850ee379860b96a8285f0faba01942541", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c30b357169b65f32d84657e56584f124", "guid": "bfdfe7dc352907fc980b868725387e98d10597e1ce03246ada394ccfa5802e3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a6d2ac7568a9bbaf4613eef4581caed", "guid": "bfdfe7dc352907fc980b868725387e98ceae516b9d7a81fb2847785d288c35d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bc715b3ddd2e276a115532e89d3d535", "guid": "bfdfe7dc352907fc980b868725387e98ad016ca6c7da492be694eb70cf518136", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faefc86b65bad0131664ba8043ebf825", "guid": "bfdfe7dc352907fc980b868725387e98ceb88d38a23ccef0cfd2d4e40b182ce4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827f978aa1ab5d08c798f07c5ad1933eb", "guid": "bfdfe7dc352907fc980b868725387e98826fde88b0b3b41a7467772beda2d3bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98378129384660d0624c9daeae676810c6", "guid": "bfdfe7dc352907fc980b868725387e985f944c0730e2dd041faa5e045f5d2904", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980658a4f890e760465afdab92e2a9df7e", "guid": "bfdfe7dc352907fc980b868725387e98cbe389eb71a53bf0213b6e88436000de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98774f89504632850324e050b9ca26df59", "guid": "bfdfe7dc352907fc980b868725387e983519cfc2e8c71bbcae5cea716c2d0958"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98903746e5f9efe6b647edccc1a46fc403", "guid": "bfdfe7dc352907fc980b868725387e98e11246c06ca2e15c8602ffda13b3f48a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d8a3a2fa68dfdb6f4544cb33aa9b208", "guid": "bfdfe7dc352907fc980b868725387e98889aa52e783d7deebfcd13e64159c1b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98955c1625aa5cd9ac85f15295c1e8179f", "guid": "bfdfe7dc352907fc980b868725387e9846498026cbeb262ffb86505237c102bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2353e4d676f683f0b2acf7d1428588f", "guid": "bfdfe7dc352907fc980b868725387e98ff9c70a730b07700a2193591478e64f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826bff9230a4985c3ec090a053b5b82a5", "guid": "bfdfe7dc352907fc980b868725387e98bae27d99c81244538a9d4856bfc7c06e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987da9696558da9d77c9b5a0ffccd8a842", "guid": "bfdfe7dc352907fc980b868725387e98824b53872ef0fa4817c261f6f73040a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb9bf3ea153d7b62e39439641567e6e4", "guid": "bfdfe7dc352907fc980b868725387e983eee46e0748202cf75bdb7bb88e89afe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869e4191429862d24fc1069b1fa3a3cac", "guid": "bfdfe7dc352907fc980b868725387e982584eb8b8009ac64875d4b3e295cba8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a368e98d22b797477e45d1aeb5e676a9", "guid": "bfdfe7dc352907fc980b868725387e98a2eae7b1768882e2a6ac3e9c37175038", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c909bb07dc0a55ee5b2faa031bfa69", "guid": "bfdfe7dc352907fc980b868725387e98b19dd2a02d60896df170bd210d48e7a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6f55eebe94e0bf167f0d95a7bae3a2", "guid": "bfdfe7dc352907fc980b868725387e988f376c3dab61b48f2375ab14d738334a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b98e1e0287fc3935171b441a9c0db3", "guid": "bfdfe7dc352907fc980b868725387e98971d0f1ffb310eec137d45e984706c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a460ae51d5e9c462858a0c232554aee8", "guid": "bfdfe7dc352907fc980b868725387e98e11a0e71c7b37520ce7b8dca0a1d26fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f50ece003d7408053dc7f69a638ec46", "guid": "bfdfe7dc352907fc980b868725387e98971f7fc5602ef156d69863e666462471", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d0b3a9b98b79c99d3570aeeed19e569", "guid": "bfdfe7dc352907fc980b868725387e98242f06e4adfa3400a05981d45debc3cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821dd02f4c3e7d0588a80a4255b84f3b4", "guid": "bfdfe7dc352907fc980b868725387e98210cd75c8a5fa551aa8185752d1a2f85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850992a8fa0b4d9c7c9566e1a84bb8486", "guid": "bfdfe7dc352907fc980b868725387e9851a302d1d5159419647ef2a891185f55"}], "guid": "bfdfe7dc352907fc980b868725387e98eb88a998c3459af410eea87a6647fda8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f63ed7ef477a95c61934a06a86fba19d", "guid": "bfdfe7dc352907fc980b868725387e98f6748571b26024ac01f5860f1e8d8cd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f99e7025d311c554773fd74a279cb44", "guid": "bfdfe7dc352907fc980b868725387e9864d8ae87b58cc826a9757dec9e74ea54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872f9ea57529004531362e62f91dfc8c2", "guid": "bfdfe7dc352907fc980b868725387e98609405affa9a9cd0ec076395407a35d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840ebe6d2c3d22ab8849c91bef71acfdb", "guid": "bfdfe7dc352907fc980b868725387e98eb3baa0c1d360fd3861c5bb0f6b7ea7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b8e9084ff2aaead329b42a5e205155a", "guid": "bfdfe7dc352907fc980b868725387e98cb6d6ae955d71d1106a9b24b1f710800"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824189e0bb5eb27d70d16f49269eed67a", "guid": "bfdfe7dc352907fc980b868725387e98d719b6b395028d037cd31daffd35eb7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98833ae00967b45e8481ecc6ba251a49f6", "guid": "bfdfe7dc352907fc980b868725387e98ff296c8171b662e8089b3bea82ec107e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98255b40062ba601b18335ff4a91ad5354", "guid": "bfdfe7dc352907fc980b868725387e98022a25207e252d3c38e085384f91e00d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be4261c4ed0be6fecd5a7285cefeac0f", "guid": "bfdfe7dc352907fc980b868725387e98b2c6c98605cd0ca8ca43ff8b4a431c8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983461d3400114b1396781bdafb3f77682", "guid": "bfdfe7dc352907fc980b868725387e98a8d365f1fc71aa7bfae39b8207690fbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c34dc74dbcbf5a00dc49708048d6f88", "guid": "bfdfe7dc352907fc980b868725387e980987867c2ee7976582675b5cd050f2f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982733c5c7dc8f620d6fbae950d5a4ed86", "guid": "bfdfe7dc352907fc980b868725387e987f0dbca402f3c4f4c11b35905970a1e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eaddc78801a03e5363c85126885b561", "guid": "bfdfe7dc352907fc980b868725387e98e43ee1e2ee6f4adaa5bef8725dc16053"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98041b51269054719feed9b1cbe70d7aa7", "guid": "bfdfe7dc352907fc980b868725387e985d245899930a5790b3ede277b6eee753"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de8ac7717fa22252279593b227f60383", "guid": "bfdfe7dc352907fc980b868725387e9844351150c6bc645e2a8c48113b72bb40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821bafbc0e45a7757ab912c230a31a7cf", "guid": "bfdfe7dc352907fc980b868725387e980146d091bb12565c4b5c7e77aeaf9cd1"}], "guid": "bfdfe7dc352907fc980b868725387e98fbcf4e8d37a8ad01812b9e1f850e3e03", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e986f5d060a934a8f238f51686d86fe22af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eba02be3f7c3de08681eb045e5915c67", "guid": "bfdfe7dc352907fc980b868725387e985c0302a7f9fe0d4e78d7e29ec1211540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fabce28ad9cde17495375bdd0821fdc1", "guid": "bfdfe7dc352907fc980b868725387e98febe5ef65f6d6f9193c025bcc98dce45"}], "guid": "bfdfe7dc352907fc980b868725387e98a42a76d34ebfc7c27311b28becfcdbc1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98120ce2c25f89b3e39c2f77e97969bcf1", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98f668d2fb63970b6943540111e84a8fea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}