{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0a3d19f882c1a9b93d9a526831b841a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camerawesome/camerawesome-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camerawesome/camerawesome-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camerawesome/camerawesome.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camerawesome", "PRODUCT_NAME": "camerawesome", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983c656257966dd7d7096d1acb867614e9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897c3e9c6b952ac7e31e731762f5f1491", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camerawesome/camerawesome-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camerawesome/camerawesome-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camerawesome/camerawesome.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camerawesome", "PRODUCT_NAME": "camerawesome", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981b74ded90d0116816b0fb316027dd43e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897c3e9c6b952ac7e31e731762f5f1491", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camerawesome/camerawesome-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camerawesome/camerawesome-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camerawesome/camerawesome.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camerawesome", "PRODUCT_NAME": "camerawesome", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fa6d927e3a46a4cb871130c28400e359", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c5084b04a19e88018527f9f7b92877e7", "guid": "bfdfe7dc352907fc980b868725387e980bf4be5dc55cea7acbc6af505160275e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d1f3432fb20be04460afbf46aa8fef", "guid": "bfdfe7dc352907fc980b868725387e981a54802fbbb471eb2ed44578b62bbd29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad714dcc4e986c2048bcd5c9dfd1e738", "guid": "bfdfe7dc352907fc980b868725387e9886f77af02e816497193a613c550dd4b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae5e175625d0a77376c891ac8ae0fd1", "guid": "bfdfe7dc352907fc980b868725387e985d3703ce44e8cf1ceeb78946cf528c10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5bf0d071938366129a71d3489be8c07", "guid": "bfdfe7dc352907fc980b868725387e98d33df1d789b979ad458b2a723ca20d69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9edaf0e31eea961f1376a98017dd2f", "guid": "bfdfe7dc352907fc980b868725387e988332498513976b5b751e6e28bfd90e2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98960eaac667d95d5a5c5a5586ef5fe22c", "guid": "bfdfe7dc352907fc980b868725387e98cda269e60d4ae97983d54234c2941d01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d39711a2cc4ab5edcc125b8e42baa888", "guid": "bfdfe7dc352907fc980b868725387e988026dc0d9eafd60c984144e6cb31843c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d558ac0d8c855a1c104e3093c7054297", "guid": "bfdfe7dc352907fc980b868725387e98310f9d10c5902452e625c635b4d2d80c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987700b646ea70506d4050b5d6146e89ca", "guid": "bfdfe7dc352907fc980b868725387e98e3b4dda597a1a0dadc8424a472a5daab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b27873c5784b7e1bfb8b2ed6d886b401", "guid": "bfdfe7dc352907fc980b868725387e9805f8d2e126a65dd040238d13fee5c4d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987053db5cb2d93659cc855c6015190eb1", "guid": "bfdfe7dc352907fc980b868725387e98818041c71b0a33cfd4bec5a76abff2ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fc9f8fac0085f19f3f79a9754eb71a1", "guid": "bfdfe7dc352907fc980b868725387e985d16babe6d796f856cb421297826d84a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ca5beafb3efdddc3d697ea1dbd4edc2", "guid": "bfdfe7dc352907fc980b868725387e98042cfb9336a67a6d1b2a4dd55fb61707", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98043934739acddf8fc28310990c9b8ef3", "guid": "bfdfe7dc352907fc980b868725387e98e241535499fdce08ba62df95e4277b1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985af6eeea892058e4147131e8fce450c3", "guid": "bfdfe7dc352907fc980b868725387e98b75f678698c8c5d76fab4275ce72ba2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab03dfbb2a99fb1cd535f0970a8910c4", "guid": "bfdfe7dc352907fc980b868725387e988426bea76882930b29761e109ae2fa9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd883eff5c068fe8e84e525810540d07", "guid": "bfdfe7dc352907fc980b868725387e988b707c3f8a03ecba50bacbbf87317111", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894cd37c0f79c15afe774154bc5628b73", "guid": "bfdfe7dc352907fc980b868725387e98f291eb871c00fcee6e2014d330311aa3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868e47fe34ef82fbc4f0e6e3a461e1566", "guid": "bfdfe7dc352907fc980b868725387e98785f531dcc9aa653d319e1630b58b6cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d079ff57db94891ad35ea2ca48cd40", "guid": "bfdfe7dc352907fc980b868725387e98b1e5360d829f762712dd99842549ce87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869477cc87ffa88d1e81186e979b54474", "guid": "bfdfe7dc352907fc980b868725387e980136ae238c8c9ad6482e0003ab73c594", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efd2fb014c3f71b72592ed3c5a34b5c7", "guid": "bfdfe7dc352907fc980b868725387e98c85b08c3ff8f20a676899aaa6f3af9c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c87beda614a175fb6f95a8b37722783d", "guid": "bfdfe7dc352907fc980b868725387e988bd79bfb4c01b365ac2116a6911c4d88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862f881ae2f35a7879d6bcdd2b46f45a3", "guid": "bfdfe7dc352907fc980b868725387e98f3fe7260eae52f68334138810e4ec6ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3e8d9770c5e9d5941e8e848bed4684", "guid": "bfdfe7dc352907fc980b868725387e989bdce49427ab714c2da0d8103c4ff2d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983518b54c6c61c29f81760ea3e8a04c0c", "guid": "bfdfe7dc352907fc980b868725387e988d567016355014bacef101ad0db9f334", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989be2f29cfed6189a061a5fee9cc9ab17", "guid": "bfdfe7dc352907fc980b868725387e988c4c003ea794410e2cb23023fa8186d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e29135e0bb4be30361c9e6cb04d9c0", "guid": "bfdfe7dc352907fc980b868725387e98ec99c23de7813d1e12b4a30af3fdc427", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f534e833cc2d65ce8677af56f4ce061d", "guid": "bfdfe7dc352907fc980b868725387e98bf5f57d8470a183ceffd0361c09b1a8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827996e2dfc1e67f7759c5b1af65aa22d", "guid": "bfdfe7dc352907fc980b868725387e9837a77d5e3b4b1bda6472cb32c98ce813", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fbb22c1f8ed5a191db0250ad7ed4e89", "guid": "bfdfe7dc352907fc980b868725387e98db1c752b642e7e880ffc4c38056361f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984932cad7c881bed378c26159f2958478", "guid": "bfdfe7dc352907fc980b868725387e98d68fac6f77fb0c50dece7de6243bbf87", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9890bbcbc0eaab3eec6d79532ae17f7ce9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ada21ec335d5ed80f8fe3eed356ffc30", "guid": "bfdfe7dc352907fc980b868725387e98957ed2854c128340dbbe559d09c57906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffe413943a39e9ad118d478f0dbc0201", "guid": "bfdfe7dc352907fc980b868725387e980a6ec42e034c7b86fc505d96d93c5305"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d62132b6905f166dc7648c92994fcfd", "guid": "bfdfe7dc352907fc980b868725387e9887a4387f180bdeb96c913eee0d152e5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6cf12f6816942131c7c4689c68634f4", "guid": "bfdfe7dc352907fc980b868725387e98203e761937ae0b5c1b1d3844cd572f62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f940ab976b5af21f6aea1f2f07d5b1ec", "guid": "bfdfe7dc352907fc980b868725387e98e509d0b3380178abecac6348f36fb91f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a1d2a983dae4bb5a6c7851145305088", "guid": "bfdfe7dc352907fc980b868725387e98360800f301e05e213c4e88ef370a707f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864625ec33471e2643ea33ba6fe58f38a", "guid": "bfdfe7dc352907fc980b868725387e986030b85d829f709975ee496d85a53162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2a44cefb1380d506adc5090388efa6b", "guid": "bfdfe7dc352907fc980b868725387e98ebacd7f0c55ccc6d322f4f45d98a37a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0112ef7e6e67a537946f4959ee5be1a", "guid": "bfdfe7dc352907fc980b868725387e98df1469f79157798a20799548d0552b81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7c84fa283a9b7a7e06f6b6b61caba2b", "guid": "bfdfe7dc352907fc980b868725387e9879599f63ec6f471e51d434a9ab101386"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830aaf464b7c4801cf2216308eb6efd35", "guid": "bfdfe7dc352907fc980b868725387e98921c9d4f64548263e1d4fa2dc26a7e30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ae8fee028e3be01740fd44ce1efa1e", "guid": "bfdfe7dc352907fc980b868725387e98c0f02b83ca936f88420ae086b32f6512"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98537b486879ea573e59c09058c59c6c19", "guid": "bfdfe7dc352907fc980b868725387e9832a88b90d37bbda91d95cc02697d1a3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892d59924f99eee397f7ddae65e17b269", "guid": "bfdfe7dc352907fc980b868725387e98ac2fd8ca949c9aa9e96e440157a2fe12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981969eadb6d4d20dccb5066f72dce9633", "guid": "bfdfe7dc352907fc980b868725387e98d6d1c33ace747c2c641bc3973f75b140"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efad9ffe34ed633dbaeef38b8c435bea", "guid": "bfdfe7dc352907fc980b868725387e98882ef8473b7557eb706eb320d085f893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844817e4373283d6ad47cab00b9bba556", "guid": "bfdfe7dc352907fc980b868725387e98ef02601d78e98244ee747eeb3db1d6ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c6d8bbe11f6eda9c63c7419f9202b58", "guid": "bfdfe7dc352907fc980b868725387e98de6fe0acde0e4633d65a040e4e2af4ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985835f97efa853f849102eaaa92b4b2e3", "guid": "bfdfe7dc352907fc980b868725387e98a3676a06d1e43ff64812d8e3b04469e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882faf5e462a00e2ffba9c81fa60a92a1", "guid": "bfdfe7dc352907fc980b868725387e980b1017adaeba5fd31e3593da154c7a47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828483dbe87b0d9fca9b7ade013436275", "guid": "bfdfe7dc352907fc980b868725387e9813f1be1c0de9b38ef840cbe5efa8526f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c25a1c4a780ba4c342ec224e1564c22", "guid": "bfdfe7dc352907fc980b868725387e98d41b0f296f6e18acb40a57f4db4c4412"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f79b006fa06f7067937d5747de8447", "guid": "bfdfe7dc352907fc980b868725387e9805930ae1faa1c68bde793389a3b7d222"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a675612a75aca98abe3b914fb714bea", "guid": "bfdfe7dc352907fc980b868725387e9847afde4a791148a9af760620015db128"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d2738cf8dd482f669046673956be7ea", "guid": "bfdfe7dc352907fc980b868725387e98df00c893bebb830ab7308bb2a879e424"}], "guid": "bfdfe7dc352907fc980b868725387e98d32457d1f8d91ca8afbc24993a900875", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e9877d53458ca8ba69d1499e674ef43e1aa"}], "guid": "bfdfe7dc352907fc980b868725387e98c251f5e4fd99e5dbdc7ba75e747c8f4d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989b0753c75b225578248200ece6fe0e04", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98fe07ed81b687337bea25cdc20db4445f", "name": "camerawesome", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986c0ed7e5bd89942d8a1918b0231287fa", "name": "camerawesome.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}