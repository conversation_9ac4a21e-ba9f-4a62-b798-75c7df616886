import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/notifications/domain/repositories/notification_repository.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_confirm_response_entities.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_providers_entity.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_success_response_entities.dart';
import 'package:cbrs/features/top_up/domain/repositories/top_up_repositories.dart';
import 'package:equatable/equatable.dart';

class MarkAllAsReadUsecase extends UsecaseWithoutParams<bool> {
  const MarkAllAsReadUsecase(this._repositores);
  final NotificationRepository _repositores;

  @override
  ResultFuture<bool> call() {
    return _repositores.markAllAsRead();
  }
}
