import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/constants/storage_keys.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/connectivity/connectivity_controller.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/services/storage/hive_box_manager.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gif/gif.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';

class CustomConnectLoader extends StatefulWidget {
  const CustomConnectLoader({super.key});

  @override
  State<CustomConnectLoader> createState() => _CustomConnectLoaderState();
}

class _CustomConnectLoaderState extends State<CustomConnectLoader>
    with TickerProviderStateMixin {
  final bool _isCheckingConnectivity = false;

  late HiveBoxManager _hiveBoxManager;
  late AuthLocalDataSource _authLocalDataSource;
  late AnimationController _lottieController;

  @override
  void initState() {
    _hiveBoxManager = sl<HiveBoxManager>();
    _authLocalDataSource = sl<AuthLocalDataSource>();
  _lottieController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    super.initState();
  }

  late AssetImage loaderGif;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _lottieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Lottie.asset(
       MediaRes.loadingAnimationJson,
        controller: _lottieController,
        width: 200,
        height: 200,
        fit: BoxFit.contain,
        onLoaded: (composition) {
          _lottieController
            ..duration = composition.duration
            ..repeat();
        },
      ),
    );
  }
}
