import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';

class BuildPaymentOption extends StatelessWidget {
  const BuildPaymentOption({
    required this.loanApplication,
    required this.loanId,
    required this.months,
    required this.loanType,
    required this.payWithConnectWallet,
    super.key,
  });
  final String loanId;
  final String loanType;
  final String months;
  final LoanRepaymentDataEntity loanApplication;

  final Function() payWithConnectWallet;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildBottomSheetHeader(context),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 16.h,
        ),
        CustomBuildText(
          text: 'Choose your preferred payment term for your loan repayment.',
          color: Colors.black.withOpacity(0.3),
          fontSize: 14.sp,
        ),
        SizedBox(
          height: 16.h,
        ),
        _buildPaymentRow(
          context,
          iconString: MediaRes.solarWallet,
          onTap: () async {
            Navigator.pop(context);
            payWithConnectWallet();
            // await context.pushNamed(
            //   AppRouteName.confirmRepay,
            //   extra: {
            //     'loanApplication': loanApplication,
            //     'isAgent': false,
            //     'loanId': loanId,
            //     'isRepayment': true,
            //     'loanType': loanType,
            //     'months': months,
            //   },
            // );
          },
          paymentText: 'Pay By Connect wallet',
        ),
        SizedBox(
          height: 8.h,
        ),
        _buildPaymentRow(
          context,
          iconString: MediaRes.solarAgent,
          onTap: () async {
            Navigator.pop(context);
            await context.pushNamed(
              AppRouteName.confirmRepay,
              extra: {
                'loanApplication': loanApplication,
                'isAgent': true,
                'isRepayment': true,
                'loanType': loanType,
              },
            );
          },
          paymentText: 'Pay via Agent',
        ),
      ],
    );
  }

  Widget _buildBottomSheetHeader(BuildContext context) {
    return Container(
      // color: Colors.green,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomBuildText(
            text: 'Payement Terms',
            fontWeight: FontWeight.w700,
            fontSize: 16.sp,
          ),
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Icon(
              Icons.close,
              color: Colors.black,
              size: 20.h,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentRow(
    BuildContext context, {
    required String iconString,
    required String paymentText,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        // height: 56.h,
        padding: EdgeInsets.all(16.h),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: const Color(0x0A2C2B34),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Container(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 24.w,
                      height: 24.h,
                      clipBehavior: Clip.antiAlias,
                      decoration: const BoxDecoration(),
                      child: Image.asset(
                        iconString,
                        color: Theme.of(context).primaryColor.withOpacity(0.8),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    CustomBuildText(
                      text: paymentText,
                      color: Theme.of(context).primaryColor.withOpacity(0.8),
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 10),
            Container(
              clipBehavior: Clip.antiAlias,
              decoration: const BoxDecoration(),
              child: Icon(
                Icons.arrow_forward_ios,
                size: 16.h,
                color: Theme.of(context).primaryColor.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
