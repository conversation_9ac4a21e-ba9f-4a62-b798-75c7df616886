import 'package:cbrs/core/common/widgets/custom_app_bar.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_pin_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/custom_pin_screen.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/name_initial.dart';
import 'package:cbrs/features/money_request/domain/entities/my_requests_list_entity.dart';
import 'package:cbrs/features/money_request/presentation/bloc/money_request_detail/money_request_detail_bloc.dart';
import 'package:cbrs/features/money_request/presentation/widgets/cancel_request_confirmation.dart';
import 'package:cbrs/features/money_request/presentation/widgets/paid_money_request_history.dart';
import 'package:cbrs/features/money_request/presentation/widgets/rejected_money_request_history.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import 'package:cbrs/features/money_request/presentation/bloc/money_request_list/money_request_list_bloc.dart';

class MoneyRequestDetailScreen extends StatefulWidget {
  const MoneyRequestDetailScreen({
    required this.moneyRequest,
    super.key,
  });

  final MoneyRequestEntity moneyRequest;

  @override
  State<MoneyRequestDetailScreen> createState() =>
      _MoneyRequestDetailScreenState();
}

class _MoneyRequestDetailScreenState extends State<MoneyRequestDetailScreen> {
  TextEditingController reasonController = TextEditingController();

  bool isUSD = false;

  @override
  void initState() {
    super.initState();
    isUSD = widget.moneyRequest.currency == 'USD';
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MoneyRequestDetailBloc, MoneyRequestDetailState>(
      listener: (BuildContext context, MoneyRequestDetailState state) {
        if (state is MoneyRequestDetailActionState && state.isCancelSuccess) {
          CustomToastification(
            context,
            message: 'Money request canceled',
            isError: false,
            errorTitle: 'Success',
          );

          context.pop();

          return;
        }

        if (state is MoneyRequestDetailActionState && state.isRejectSuccess) {
          CustomToastification(
            context,
            message: 'Money request rejected',
            isError: false,
            errorTitle: 'Success',
          );

          context.pop();

          return;
        }

        if (state is MoneyRequestDetailActionState && state.isAcceptSuccess) {
          // CustomToastification(context, message: state.message);
          context.pushNamed(
            AppRouteName.moneyRequestCheckAmountScreen,
            extra: widget.moneyRequest,
          );
          return;
        }

        if (state is MoneyRequestDetailActionState && !state.isRejectSuccess) {
          CustomToastification(context, message: state.message);
          return;
        }

        if (state is MoneyRequestDetailErrorState) {
          CustomToastification(context, message: state.message);
          return;
        }
      },
      builder: (pageContext, state) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Request Details'),
          ),
          body: Padding(
            padding: EdgeInsets.only(top: 21.h, left: 16.w, right: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomPageHeader(
                  pageTitle: 'Request Details',
                  description: widget.moneyRequest.isMyRequest
                      ? 'Detailed information about this request, with '
                          'options to accept or reject.'
                      : 'View detailed information about this request,'
                          ' with the option to cancel before the user takes action.',
                ),
                SizedBox(height: 36.h),
                if (widget.moneyRequest.status == 'COMPLETED')
                  PaidMoneyRequestHistory(moneyRequest: widget.moneyRequest)
                else if (widget.moneyRequest.status == 'REJECTED' ||
                    widget.moneyRequest.status == 'CANCELED')
                  RejectedMoneyRequestHistory(moneyRequest: widget.moneyRequest)
                else ...[
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16.w,
                            vertical: 24.h,
                          ),
                          color: Colors.white,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Row(
                                      children: [
                                        // icons
                                        Container(
                                          clipBehavior: Clip.hardEdge,
                                          decoration: ShapeDecoration(
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(100),
                                            ),
                                          ),
                                          child: (widget
                                                      .moneyRequest.isMyRequest
                                                  ? widget.moneyRequest
                                                      .senderAvatar.isEmpty
                                                  : widget
                                                      .moneyRequest
                                                      .beneficiaryAvatar
                                                      .isEmpty)
                                              ? Container(
                                                  clipBehavior: Clip.hardEdge,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    gradient: isUSD
                                                        ? LightModeTheme()
                                                            .usdGradient
                                                        : LightModeTheme()
                                                            .primaryGradient,
                                                  ),
                                                  width: 48,
                                                  height: 48,
                                                  child: Center(
                                                    child: Text(
                                                      separateNames(
                                                        widget.moneyRequest
                                                                .isMyRequest
                                                            ? widget
                                                                .moneyRequest
                                                                .senderName
                                                            : widget
                                                                .moneyRequest
                                                                .beneficiaryName,
                                                      ),
                                                      style: GoogleFonts.outfit(
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontSize: 20,
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              : Image.network(
                                                  widget.moneyRequest
                                                          .isMyRequest
                                                      ? widget.moneyRequest
                                                          .senderAvatar
                                                      : widget.moneyRequest
                                                          .beneficiaryAvatar,
                                                  height: 48,
                                                  width: 48,
                                                  fit: BoxFit.cover,
                                                  alignment:
                                                      Alignment.topCenter,
                                                ),
                                        ),
                                        const SizedBox(width: 10),
                                        //column
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              CustomBuildText(
                                                text: widget.moneyRequest
                                                        .isMyRequest
                                                    ? widget
                                                        .moneyRequest.senderName
                                                    : widget.moneyRequest
                                                        .beneficiaryName,
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              const SizedBox(height: 4),
                                              CustomBuildText(
                                                text:
                                                    '${widget.moneyRequest.billAmount}'
                                                    ' ${widget.moneyRequest.currency}',
                                                fontSize: 16,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ],
                                          ),
                                        ),
                                        Column(
                                          children: [
                                            status(widget.moneyRequest.status),
                                            SizedBox(
                                              height: 7.h,
                                            ),
                                            CustomBuildText(
                                              text: DateFormat('MMM dd,yyyy')
                                                  .format(
                                                widget.moneyRequest.createdAt,
                                              ),
                                              color:
                                                  Colors.black.withOpacity(0.4),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              if (widget.moneyRequest.reason.isNotEmpty) ...[
                                SizedBox(height: 16.h),
                                const CustomBuildText(
                                  text: 'Reason',
                                  fontWeight: FontWeight.w600,
                                  color: Color(0x66000000),
                                ),
                                SizedBox(height: 2.h),
                                CustomBuildText(
                                  text: widget.moneyRequest.reason,
                                ),
                              ],
                            ],
                          ),
                        ),
                        SizedBox(height: 8.h),
                        if (widget.moneyRequest.isMyRequest)
                          Row(
                            children: [
                              Expanded(child: Container()),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: CustomButton(
                                  onPressed: () async => showDialog<void>(
                                    context: pageContext,
                                    builder: (context) =>
                                        CancelRequestConfirmation(
                                      title: 'Cancel Request',
                                      content:
                                          'Are you sure you want to Cancel this request?',
                                      actionButtonLabel: 'Cancel Request',
                                      showRejectReason: false,
                                      onActionButtonClicked: (_) => pageContext
                                          .read<MoneyRequestDetailBloc>()
                                          .add(
                                            CancelRequestActionEvent(
                                              transactionID: widget
                                                  .moneyRequest.transactionId,
                                              amount: widget
                                                  .moneyRequest.billAmount,
                                            ),
                                          ),
                                    ),
                                  ),
                                  options: CustomButtonOptions(
                                    borderRadius: BorderRadius.circular(100),
                                    borderSide: const BorderSide(
                                      color: Color(0xFFFF4545),
                                      width: 0.6,
                                    ),
                                  ),
                                  showLoadingIndicator:
                                      state is MoneyRequestDetailLoadingState &&
                                          state.isCancelLoading,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(
                                        Icons.close,
                                        size: 16,
                                        color: Color(0xFFFF4545),
                                      ),
                                      SizedBox(width: 4.w),
                                      const CustomBuildText(
                                        text: 'Cancel',
                                        color: Color(0xFFFF4545),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          )
                        else
                          Row(
                            children: [
                              Expanded(
                                child: CustomButton(
                                  onPressed: () async => showDialog<void>(
                                    context: pageContext,
                                    builder: (context) =>
                                        CancelRequestConfirmation(
                                      title: 'Reject Request',
                                      content:
                                          'Are you sure you want to reject this request?',
                                      actionButtonLabel: 'Reject',
                                      showRejectReason: true,
                                      onActionButtonClicked: (String? reason) =>
                                          pageContext
                                              .read<MoneyRequestDetailBloc>()
                                              .add(
                                                RejectRequestActionEvent(
                                                  transactionID: widget
                                                      .moneyRequest
                                                      .transactionId,
                                                  amount: widget
                                                      .moneyRequest.billAmount,
                                                  reason: reason,
                                                ),
                                              ),
                                    ),
                                  ),
                                  options: CustomButtonOptions(
                                    borderRadius: BorderRadius.circular(100),
                                    borderSide: const BorderSide(
                                      color: Color(
                                        0xFFFF4545,
                                      ),
                                      width: 0.6,
                                    ),
                                  ),
                                  showLoadingIndicator:
                                      state is MoneyRequestDetailLoadingState &&
                                          state.isRejectLoading,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(
                                        Icons.close,
                                        size: 16,
                                        color: Color(0xFFFF4545),
                                      ),
                                      SizedBox(width: 4.w),
                                      const CustomBuildText(
                                        text: 'Reject',
                                        color: Color(0xFFFF4545),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              if (widget.moneyRequest.status == 'ACCEPTED')
                                Expanded(
                                  child: CustomButton(
                                    onPressed: () {
                                      context.pushNamed(
                                        AppRouteName
                                            .moneyRequestCheckAmountScreen,
                                        extra: widget.moneyRequest,
                                      );
                                    },
                                    options: CustomButtonOptions(
                                      borderRadius: BorderRadius.circular(100),
                                      color: isUSD
                                          ? LightModeTheme().primaryColorUSD
                                          : LightModeTheme().primaryColorBirr,
                                    ),
                                    showLoadingIndicator: state
                                            is MoneyRequestDetailLoadingState &&
                                        state.isAcceptLoading,
                                    trailing: const Icon(Icons.check),
                                    text: '',
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(
                                          Icons.check,
                                          size: 16,
                                          color: Colors.white,
                                        ),
                                        SizedBox(width: 4.w),
                                        const CustomBuildText(
                                          text: 'Accept',
                                          color: Colors.white,
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              else
                                Expanded(
                                  child: CustomButton(
                                    onPressed: _acceptMoneyRequest,
                                    options: CustomButtonOptions(
                                      borderRadius: BorderRadius.circular(100),
                                      color: isUSD
                                          ? LightModeTheme().primaryColorUSD
                                          : LightModeTheme().primaryColorBirr,
                                    ),
                                    showLoadingIndicator: state
                                            is MoneyRequestDetailLoadingState &&
                                        state.isAcceptLoading,
                                    trailing: const Icon(Icons.check),
                                    text: '',
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(
                                          Icons.check,
                                          size: 16,
                                          color: Colors.white,
                                        ),
                                        SizedBox(width: 4.w),
                                        const CustomBuildText(
                                          text: 'Accept',
                                          color: Colors.white,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  final TextEditingController pinController = TextEditingController();

  bool _isCheckingPin = false;

  Future<void> _acceptMoneyRequest() async {
    context.read<MoneyRequestDetailBloc>().add(
          AcceptRequestActionEvent(
            transactionID: widget.moneyRequest.transactionId,
            amount: widget.moneyRequest.billAmount,
          ),
        );
  }

  Future<void> _rejectMoneyRequest() async {
    context.read<MoneyRequestDetailBloc>().add(
          RejectRequestActionEvent(
            transactionID: widget.moneyRequest.transactionId,
            amount: widget.moneyRequest.billAmount,
            reason: reasonController.text,
          ),
        );
  }

  void _handleSubmitPin(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => StatefulBuilder(
        builder: (BuildContext context, StateSetter setModalState) {
          return BlocConsumer<MoneyRequestDetailBloc, MoneyRequestDetailState>(
            listener: (context, state) {
              if (state is MoneyRequestDetailErrorState) {
                _resetPinState(setModalState);
                CustomToastification(
                  context,
                  message: state.message,
                );
              } else if (state is MoneyRequestDetailResultState &&
                  state.isSuccess) {
                context.pop();

                context.pushNamed(
                  AppRouteName.sendMoneyRequestSuccessScreen,
                  extra: widget.moneyRequest,
                );
              }
            },
            builder: (context, state) => CustomPinScreen(
              isLoading: _isCheckingPin,
              controller: pinController,
              onSubmitted: (value) async {
                setState(() => _isCheckingPin = true);
                context.read<MoneyRequestDetailBloc>().add(
                      MoneyRequestDetailConfirmPinEvent(
                        pin: value,
                        billRefNo: widget.moneyRequest.billRefNo,
                      ),
                    );
              },
              onChanged: (keys, isKey) {
                if (_isCheckingPin) return;

                setModalState(() {
                  if (!isKey) {
                    pinController.text = pinController.text.isNotEmpty
                        ? pinController.text
                            .substring(0, pinController.text.length - 1)
                        : '';
                    pinController.selection = TextSelection.fromPosition(
                      TextPosition(offset: pinController.text.length),
                    );
                    return;
                  }
                  pinController.text = "${pinController.text}$keys";
                  pinController.selection = TextSelection.fromPosition(
                    TextPosition(offset: pinController.text.length),
                  );
                });
              },
            ),
          );
        },
      ),
    );
  }

  Future<void> _resetPinState(StateSetter setModalState) async {
    setModalState(() {
      _isCheckingPin = false;
      pinController.clear();
    });
  }

  Widget status(String status) {
    if (status == 'REQUESTED' || status == 'ACCEPTED' || status == 'WAITING') {
      return Container(
        padding: EdgeInsets.symmetric(
          vertical: 4.h,
          horizontal: 11.5.w,
        ),
        decoration: BoxDecoration(
          color: const Color(0x2EFFBB46),
          borderRadius: BorderRadius.circular(100),
        ),
        child: const CustomBuildText(
          text: 'Waiting',
          fontSize: 12,
          color: Color(0xFFFFBB46),
        ),
      );
    }

    if (status == 'COMPLETED') {
      return Container(
        padding: EdgeInsets.symmetric(
          vertical: 4.h,
          horizontal: 11.5.w,
        ),
        decoration: BoxDecoration(
          color: const Color(0x2E09EE65),
          borderRadius: BorderRadius.circular(100),
        ),
        child: const CustomBuildText(
          text: 'Paid',
          fontSize: 12,
          color: Color(0xFF11B22B),
        ),
      );
    }

    if (status == 'REJECTED') {
      return Container(
        padding: EdgeInsets.symmetric(
          vertical: 4.h,
          horizontal: 11.5.w,
        ),
        decoration: BoxDecoration(
          color: const Color(0x1FFF4545),
          borderRadius: BorderRadius.circular(100),
        ),
        child: const CustomBuildText(
          text: 'Rejected',
          fontSize: 12,
          color: Color(0xFFFF4545),
        ),
      );
    }

    if (status == 'CANCELED') {
      return Container(
        padding: EdgeInsets.symmetric(
          vertical: 4.h,
          horizontal: 11.5.w,
        ),
        decoration: BoxDecoration(
          color: const Color(0x1FFF4545),
          borderRadius: BorderRadius.circular(100),
        ),
        child: const CustomBuildText(
          text: 'Canceled',
          fontSize: 12,
          color: Color(0xFFFF4545),
        ),
      );
    }

    return Container();
  }
}
