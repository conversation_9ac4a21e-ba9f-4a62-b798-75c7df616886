import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_connect/domain/repositories/my_connect_repository.dart';
import 'package:equatable/equatable.dart';

class MyConnectAcceptRequestUseCase
    implements UsecaseWithParams<void, AcceptRequestParams> {
  MyConnectAcceptRequestUseCase(this.repository);
  final MyConnectRepository repository;

  @override
  ResultFuture<void> call(AcceptRequestParams params) async {
    if (params.accept) {
      return repository.acceptConnectionRequest(params.requestId);
    } else {
      return repository.rejectConnectionRequest(params.requestId);
    }
  }
}

class AcceptRequestParams extends Equatable {
  final String requestId;
  final bool accept;

  const AcceptRequestParams({
    required this.requestId,
    required this.accept,
  });

  @override
  List<Object?> get props => [requestId, accept];
}
