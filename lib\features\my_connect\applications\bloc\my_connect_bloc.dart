import 'package:bloc/bloc.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_list_response_entity.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';
import 'package:cbrs/features/my_connect/domain/usecases/my_connect_fetch_connects_use_cases.dart';
import 'package:cbrs/features/my_connect/domain/usecases/my_connect_send_request_usecase.dart';
import 'package:cbrs/features/my_connect/domain/usecases/my_connect_accept_request_usecase.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

part 'my_connect_event.dart';
part 'my_connect_state.dart';

class MyConnectBloc extends Bloc<MyConnectEvent, MyConnectState> {
  final MyConnectSendRequestUseCase _sendRequestUseCase;
  final MyConnectFetchConnectsUseCase _fetchConnectsUseCase;
  final MyConnectAcceptRequestUseCase _acceptRequestUseCase;

  MyConnectBloc({
    required MyConnectSendRequestUseCase sendRequestUseCase,
    required MyConnectFetchConnectsUseCase fetchConnectsUseCase,
    required MyConnectAcceptRequestUseCase acceptRequestUseCase,
  })  : _sendRequestUseCase = sendRequestUseCase,
        _fetchConnectsUseCase = fetchConnectsUseCase,
        _acceptRequestUseCase = acceptRequestUseCase,
        super(const MyConnectInitialState()) {
    on<LoadMyConnectDataEvent>(_handleLoadMyConnectData);
    on<SendConnectionRequestEvent>(_handleSendConnectionRequest);
    on<FetchConnectionsEvent>(_handleFetchConnections);
    on<AcceptConnectionRequestEvent>(_handleAcceptConnectionRequest);
    on<RejectConnectionRequestEvent>(_handleRejectConnectionRequest);
  }

  Future<void> _handleLoadMyConnectData(
    LoadMyConnectDataEvent event,
    Emitter<MyConnectState> emit,
  ) async {
    emit(const MyConnectLoadingState());
    try {
      add(const FetchConnectionsEvent());
    } catch (e) {
      emit(MyConnectErrorState(message: e.toString()));
    }
  }

  Future<void> _handleSendConnectionRequest(
    SendConnectionRequestEvent event,
    Emitter<MyConnectState> emit,
  ) async {
    debugPrint("hvhvh");

    // if (state is MyConnectLoadingState) return;
    emit(const MyConnectLoadingState());

    debugPrint("ccccheck");

    final result = await _sendRequestUseCase(
      SendRequestParams(
        recipientId: event.recipientId,
        recipientName: event.recipientName,
        recipientEmail: event.recipientEmail,
        recipientPhone: event.recipientPhone,
        recipientAvatar: event.recipientAvatar,
      ),
    );

    result.fold(
      (failure) => emit(MyConnectErrorState(message: failure.message)),
      (connectionRequest) {
        emit(ConnectionRequestSentState(connectionRequest: connectionRequest));
        add(const FetchConnectionsEvent());
      },
    );
  }

  Future<void> _handleFetchConnections(
    FetchConnectionsEvent event,
    Emitter<MyConnectState> emit,
  ) async {
    // if (state is MyConnectLoadingState) return;
    emit(const MyConnectLoadingState());

    final result = await _fetchConnectsUseCase(
      FetchConnectsParams(
        status: event.status,
        scope: event.scope,
        page: event.page,
        limit: event.limit,
      ),
    );

    result.fold(
      (failure) => emit(MyConnectErrorState(message: failure.message)),
      (response) => emit(ConnectionsLoadedState(
        connections: response.data,
        meta: response.meta,
      )),
    );
  }

  Future<void> _handleAcceptConnectionRequest(
    AcceptConnectionRequestEvent event,
    Emitter<MyConnectState> emit,
  ) async {
    // if (state is MyConnectLoadingState) return;
    emit(const MyConnectLoadingState());

    final result = await _acceptRequestUseCase(
      AcceptRequestParams(
        requestId: event.requestId,
        accept: true,
      ),
    );

    result.fold(
      (failure) => emit(MyConnectErrorState(message: failure.message)),
      (_) {
        emit(const ConnectionRequestAcceptedState());
        add(const FetchConnectionsEvent());
      },
    );
  }

  Future<void> _handleRejectConnectionRequest(
    RejectConnectionRequestEvent event,
    Emitter<MyConnectState> emit,
  ) async {
    // if (state is MyConnectLoadingState) return;
    emit(const MyConnectLoadingState());

    final result = await _acceptRequestUseCase(
      AcceptRequestParams(
        requestId: event.requestId,
        accept: false,
      ),
    );

    result.fold(
      (failure) => emit(MyConnectErrorState(message: failure.message)),
      (_) {
        emit(const ConnectionRequestRejectedState());
        add(const FetchConnectionsEvent());
      },
    );
  }
}
