import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp_success_entity.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:cbrs/core/error/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:cbrs/features/mini_apps/domain/repositories/miniapp_repository.dart';

class SubmitPinMiniappUseCase
    extends UsecaseWithParams<MiniappSuccessEntity, MiniappPinParams> {
  final MiniappRepository _repository;

  const SubmitPinMiniappUseCase(this._repository);

  @override
  ResultFuture<MiniappSuccessEntity> call(MiniappPinParams params) async {
    return _repository.submitPin(
        transactionType: params.transactionType,
        pin: params.pin,
        billRefNo: params.billRefNo


    );
  }
}

class MiniappPinParams extends Equatable {
  final String transactionType;
  final String pin;
  final String billRefNo;

  const MiniappPinParams({
    required this.transactionType,
    required this.pin,
    required this.billRefNo,
  });

  @override
  List<Object> get props => [transactionType, pin, billRefNo];
}
