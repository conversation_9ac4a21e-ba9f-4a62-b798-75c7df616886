import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/add_money/domain/entities/add_money_response.dart';
import 'package:flutter/material.dart';

class AddMoneyResponseModel {
  AddMoneyResponseModel({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.transactionOwner,
    required this.beneficiaryId,
    required this.beneficiaryName,
    required this.beneficiaryAccountNo,
    required this.transactionType,
    required this.billAmount,
    required this.originalCurrency,
    required this.serviceCharge,
    required this.vat,
    required this.totalAmount,
    required this.billRefNo,
    required this.status,
    required this.authorizationType,
    this.sessionId,
    this.senderPhone,
    this.cardNumber,
    this.senderEmail,
    this.beneficiaryPhone,
    this.beneficiaryEmail,
    this.bankName,
    this.bankCode,
    this.walletFTNumber,
  });

  factory AddMoneyResponseModel.fromJson(Map<String, dynamic> json) {
    debugPrint("😔total amount of json ${json['totalAmount']}");
    return AddMoneyResponseModel(
      id: AppMapper.safeString(json['id']),
      senderId: AppMapper.safeString(json['senderId']),
      sessionId: AppMapper.safeString(json['sessionID']),
      senderName: AppMapper.safeString(json['senderName']),
      senderPhone: AppMapper.safeString(json['senderPhone']),
      cardNumber: AppMapper.safeString(json['cardNumber']),
      senderEmail: AppMapper.safeString(json['senderEmail']),
      transactionOwner: AppMapper.safeString(json['transactionOwner']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      beneficiaryPhone: AppMapper.safeString(json['beneficiaryPhone']),
      beneficiaryAccountNo: AppMapper.safeString(json['beneficiaryAccountNo']),
      beneficiaryEmail: AppMapper.safeString(json['beneficiaryEmail']),
      bankName: AppMapper.safeString(json['bankName']),
      bankCode: AppMapper.safeString(json['bankCode']),
      transactionType: AppMapper.safeString(json['transactionType']),
      billAmount: AppMapper.safeDouble(json['billAmount']),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      serviceCharge: AppMapper.safeDouble(json['serviceCharge']),
      vat: AppMapper.safeDouble(json['VAT']),
      totalAmount: AppMapper.safeDouble(json['totalAmount']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      status: AppMapper.safeString(json['status']),
      authorizationType: AppMapper.safeString(json['authorization_type']),
      walletFTNumber: AppMapper.safeString(json['walletFTNumber']),
    );
  }
  final String id;
  final String senderId;
  final String? sessionId;
  final String senderName;
  final String? senderPhone;
  final String? cardNumber;
  final String? senderEmail;
  final String transactionOwner;
  final String beneficiaryId;
  final String beneficiaryName;
  final String? beneficiaryPhone;
  final String beneficiaryAccountNo;
  final String? beneficiaryEmail;
  final String? bankName;
  final String? bankCode;
  final String transactionType;
  final double billAmount;
  final String originalCurrency;
  final double serviceCharge;
  final double vat;
  final double totalAmount;
  final String billRefNo;
  final String status;
  final String authorizationType;
  final String? walletFTNumber;
}
