import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_request.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_response.dart';
import 'package:cbrs/features/load_wallet/data/models/load_wallet_status_response.dart';

abstract class LoadWalletRepository {
  ResultFuture<LoadWalletResponse> loadToWallet(LoadWalletRequest request);
  ResultFuture<LoadWalletResponse> getLoadWalletDetails(String billRefNo);
  ResultFuture<LoadWalletStatusResponse> checkLoadWalletStatus(String billRefNo);



}

