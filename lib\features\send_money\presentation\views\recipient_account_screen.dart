import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/send_money/domain/entities/bank.dart';
import 'package:cbrs/features/send_money/domain/entities/recent_transaction_hive.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:cbrs/features/user/presentation/bloc/user_bloc.dart';
import 'package:cbrs/features/user/presentation/bloc/user_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

class RecipientAccountNumberScreen extends StatefulWidget {
  const RecipientAccountNumberScreen({
    required this.isBirrTransfer,
    required this.bank,
    super.key,
  });
  final bool isBirrTransfer;
  final Bank bank;

  @override
  State<RecipientAccountNumberScreen> createState() =>
      _RecipientAccountNumberScreenState();
}

class _RecipientAccountNumberScreenState
    extends State<RecipientAccountNumberScreen> {
  final _accountNumberController = TextEditingController();
  final _focusNode = FocusNode();
  bool _showRecipientCard = false;
  bool _isLoading = false;
  String? _recipientName;
  String? recipentAccountNumber;
  bool _isLoadingRecipient = false;
  String? _senderName;
  bool _isLookupMode = true;
  bool _isLookupLoading = false;
  Timer? _debounceTimer;

  bool get _isValidAccount => _accountNumberController.text.length >= 3;
  bool get _shouldShowReciepent =>
      _isValidAccount && recipentAccountNumber == _accountNumberController.text;
  @override
  void initState() {
    super.initState();

// used to update button color state
    _accountNumberController.addListener(_updateButtonState);
    // Fetch wallet details
    context.read<BankTransferRecentTransactionBloc>().add(
          GetRecentTransactionsEvent(
            bankId: widget.bank.name,
          ),
        );
  }

  void _updateButtonState() {
    setState(() {});
  }

  // check if entered account number is exist

  Future<void> _handleAccountLookup(
    String accountNumber,
    String bankCode,
  ) async {
    if (!mounted || !_focusNode.hasFocus) return;

    // Only proceed if account number meets minimum length
    if (accountNumber.length < (widget.bank.isWallet ? 9 : 3)) return;

    setState(() {
      _isLoadingRecipient = true;
      _showRecipientCard = false;
    });

    context.read<BankTransferBloc>().add(
          LookupAccountEvent(
            accountNumber: accountNumber,
            bankCode: bankCode,
          ),
        );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _accountNumberController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

// when button pressed
  void _handleContinue() {
    FocusScope.of(context).unfocus();

    // Validate account number
    if (_accountNumberController.text.isEmpty) return;

    final accountNumber = _accountNumberController.text;

    // Validate based on account type
    if (widget.bank.isWallet) {
      if (accountNumber.length != 9 ||
          (!accountNumber.startsWith('9') && !accountNumber.startsWith('7'))) {
        CustomToastification(
          context,
          message: 'Please enter a valid phone number',
          errorTitle: 'Invalid Input',
        );
        return;
      }
    } else {
      if (accountNumber.length < 3) {
        CustomToastification(
          context,
          message: 'Account number must be at least 3 digits',
          errorTitle: 'Invalid Input',
        );
        return;
      }
    }

    // if (_showRecipientCard && _recipientName != null)
    if (_shouldShowReciepent) {
      // If we already have recipient info, proceed to next screen
      _onContinue();
    } else {
      // Otherwise perform lookup
      setState(() {
        _isLookupLoading = true;
        _isLoadingRecipient = true;
        _showRecipientCard = false;
      });

      context.read<BankTransferBloc>().add(
            LookupAccountEvent(
              accountNumber: accountNumber,
              bankCode: widget.bank.etSwitchCode,
            ),
          );
    }
  }

// if acount number is exist  other related data will be fetched.

  void _onContinue() {
    FocusScope.of(context).unfocus();

    final currency = widget.isBirrTransfer ? 'etb' : 'usd';

    context.pushNamed(
      AppRouteName.addAmountBankTransfer,
      pathParameters: {
        'currency': currency,
      },
      extra: {
        'senderName': _senderName,
        'recipientName': _recipientName,
        'recipientAccount': _accountNumberController.text,
        'isBirrTransfer': widget.isBirrTransfer,
        'bank': widget.bank,
        'exchangeRate': widget.bank.exchangeRate?.rate ?? 0.0,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeColor = widget.isBirrTransfer
        ? LightModeTheme().primaryColorBirr
        : Theme.of(context).primaryColor;

    return BlocConsumer<UserBloc, UserState>(
      listener: (context, state) {},
      builder: (context, userState) {
        if (userState is UserLoaded) {
          _senderName =
              '${userState.user.firstName} ${userState.user.lastName}';
        }

        return BlocConsumer<BankTransferBloc, BankTransferState>(
          listenWhen: (previous, current) {
            return current is AccountLookupSuccess ||
                current is AccountLookupEmpty ||
                current is BankTransferLoading ||
                current is WalletDetailsLoaded ||
                current is ExchangeRateLoaded ||
                current is BankTransferError;
          },
          listener: (context, state) {
            if (state is AccountLookupSuccess) {
              FocusScope.of(context).unfocus();
              setState(() {
                _isLoadingRecipient = false;
                _showRecipientCard = true;
                _recipientName = state.account.accountInfo;
                _isLookupLoading = false;
                _isLookupMode = false;
                recipentAccountNumber = _accountNumberController.text;
              });
            } else if (state is AccountLookupEmpty) {
              setState(() {
                _isLoadingRecipient = false;
                _showRecipientCard = false;
                _recipientName = null;
                _isLookupMode = true;
                _isLookupLoading = false;
              });

              CustomToastification(
                context,
                message: 'Please check the account number and try again',
                errorTitle: 'Member not found',
              );
            } else if (state is BankTransferLoading) {
              setState(() {
                _isLoadingRecipient = true;
                _showRecipientCard = false;
              });
            } else if (state is BankTransferError) {
              setState(() {
                _isLoading = false;
                _isLoadingRecipient = false;
                _showRecipientCard = false;
                _isLookupLoading = false;
              });

              CustomToastification(
                context,
                message: state.message,
              );
            }
          },
          builder: (context, state) => Scaffold(
            appBar: AppBar(
              title: Text(
                widget.bank.isWallet
                    ? 'Digital Wallet Transfer'
                    : 'Bank Transfer',
              ),
            ),
            body: SafeArea(
              bottom: true,
              child: GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0.h),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomPageHeader(
                                pageTitle: widget.bank.isWallet
                                    ? 'Recipient Phone Number'
                                    : 'Recipient Account Number',
                                description: widget.bank.isWallet
                                    ? 'Enter the recipient phone number to send money.'
                                    : 'Enter the recipient account number on the selected bank and send money.',
                              ),
                              SizedBox(height: 24.h),
                              CustomTextInput(
                                hintText: 'Enter Account Number',
                                inputLabel: 'Account Number',
                                controller: _accountNumberController,
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(18),
                                ],
                              ),
                              const SizedBox(
                                height: 8,
                              ),
                              if (_isLookupLoading && _isLookupMode)
                                _buildShimmerEffect()
                              else if (_shouldShowReciepent)
                                GestureDetector(
                                  onTap: _handleContinue,
                                  child: RecipientCard(
                                    isBirrTransfer: widget.isBirrTransfer,
                                    name: _recipientName!,
                                    accountNumber: recipentAccountNumber ?? '',
                                  ),
                                ),
                              const SizedBox(
                                height: 16,
                              ),

                              // SizedBox(height: 16.h),
                              _recentBankTransfer(context),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        // vertical: 32.h,
                      ),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                      ),
                      child: CustomRoundedBtn(
                        btnText: 'Continue ',
                        isLoading: _isLookupLoading && _isLookupMode,
                        onTap: _isValidAccount ? _handleContinue : null,
                        bgColor: _isValidAccount
                            ? themeColor
                            : const Color(0xFFACACAC),
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerEffect() {
    return SafeArea(
      child: Container(
        margin: const EdgeInsets.only(top: 24),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.08),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 160,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 120,
                    height: 14,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _recentBankTransfer(BuildContext context) {
    return BlocBuilder<BankTransferRecentTransactionBloc, BankTransferState>(
      builder: (context, state) {
        if (state is LoadingRecentTransactions) {
          return const SizedBox(child: Text('Loading'));
        } else if (state is LoadedRecentTransactions) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (state.recentTransactions.isNotEmpty)
                Text(
                  'Recent Bank Transfers',
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black.withOpacity(0.5),
                  ),
                ),
              Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: state.recentTransactions.length,
                  itemBuilder: (context, index) {
                    final transaction = state.recentTransactions[index];

                    return _buildTransactionItem(
                      transaction: transaction,
                      onTap: () {
                        final currency = widget.isBirrTransfer ? 'etb' : 'usd';

                        context.pushNamed(
                          AppRouteName.addAmountBankTransfer,
                          pathParameters: {
                            'currency': currency,
                          },
                          extra: {
                            'senderName': _senderName,
                            'recipientName': transaction.recipientName,

                            'recipientAccount': transaction.accountNumber,
                            // '**************',
                            'isBirrTransfer': widget.isBirrTransfer,
                            'bank': widget.bank,
                            'exchangeRate':
                                widget.bank.exchangeRate?.rate ?? 0.0,
                          },
                        );
                      },
                    );
                  },
                  separatorBuilder: (context, index) => Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    height: 1,
                    child: CustomPaint(
                      painter: DottedLinePainter(
                        color: Colors.grey.withOpacity(0.2),
                      ),
                      size: const Size(double.infinity, 1),
                    ),
                  ),
                ),
              ),
            ],
          );
        } else
          return const SizedBox.shrink();
      },
    );
  }

  Widget _buildTransactionItem({
    required RecentTransactionHive transaction,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(top: 16.h, bottom: 12.h),
        child: Row(
          children: [
            CachedNetworkImage(
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 44.w,
                  height: 44.h,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
              imageUrl: widget.bank.logo ?? '',
              // 'https://staging.eaglelionsystems.com/_resources/banklogos/**********!*****************.png',
              width: 44.w,
              height: 44.h,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.recipientName,
                    //  'Solomon Kebede Belachew',
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  Text(
                    transaction.accountNumber,
                    //   '************',
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      color: const Color(0xFFAAAAAA),
                    ),
                  ),
                ],
              ),
            ),
            // Column(
            //   crossAxisAlignment: CrossAxisAlignment.end,
            //   children: [
            //     Text(
            //       'May 12, 2025',
            //       //AppMapper.safeFormattedDate(transaction.createdAt),
            //       style: GoogleFonts.outfit(
            //         fontSize: 14.sp,
            //         color: const Color(0xFFAAAAAA),
            //       ),
            //     ),
            //   ],
            // ),
            SizedBox(width: 8.w),
            Image.asset(
              MediaRes.forwardIcon,
              width: 20.w,
              color: Colors.black.withOpacity(0.4),
            ),
          ],
        ),
      ),
    );
  }
}
