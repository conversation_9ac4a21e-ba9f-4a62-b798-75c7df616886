import 'package:cbrs/features/mini_apps/domain/entities/miniapp_transaction.dart';

class MiniappTransactionModel extends MiniappTransaction {
  final String senderId;
  final String senderName;
  final String senderPhone;
  final String transactionOwner;
  final String merchantId;
  final String merchantType;
  final String billRefNo;
  final String billReason;
  final String authorizationType;
  final String status;
  final PaymentDetailsModel paymentDetails;

  const MiniappTransactionModel({
    required this.senderId,
    required this.senderName,
    required this.senderPhone,
    required this.transactionOwner,
    required this.merchantId,
    required this.merchantType,
    required this.billRefNo,
    required this.billReason,
    required this.authorizationType,
    required this.status,
    required this.paymentDetails,
    required String transactionType,
    required String recipientName,
    required double amount,
    required double serviceFee,
    required double vat,
    required DateTime date,
    required double total,
  }) : super(
    transactionType: transactionType,
    recipientName: recipientName,
    senderName: senderName,
    amount: amount,
    serviceFee: serviceFee,
    vat: vat,
    date: date,
    total: total,
    billRefNo: billRefNo,
  );

  factory MiniappTransactionModel.fromJson(Map<String, dynamic> json) {
    final billAmount = (json['billAmount'] as num?)?.toDouble() ?? 0.0;
    final serviceCharge = (json['serviceCharge'] as num?)?.toDouble() ?? 0.0;
    final vat = (json['VAT'] as num?)?.toDouble() ?? 0.0;

    return MiniappTransactionModel(
      senderId: json['senderId'] as String? ?? '',
      senderName: json['senderName'] as String? ?? '',
      senderPhone: json['senderPhone'] as String? ?? '',
      transactionOwner: json['transactionOwner'] as String? ?? '',
      merchantId: json['merchantId'] as String? ?? '',
      merchantType: json['merchantType'] as String? ?? '',
      billRefNo: json['billRefNo'] as String? ?? '',
      billReason: json['billReason'] as String? ?? '',
      authorizationType: json['authorization_type'] as String? ?? '',
      status: json['status'] as String? ?? '',
      paymentDetails: PaymentDetailsModel.fromJson(
          json['paymentDetails'] as Map<String, dynamic>? ?? {}),
      transactionType: json['transactionType'] as String? ?? '',
      recipientName: json['merchantType'] as String? ?? '',
      amount: billAmount,
      serviceFee: serviceCharge,
      vat: vat,
      date: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      total: billAmount + serviceCharge + vat,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'senderId': senderId,
      'senderName': senderName,
      'senderPhone': senderPhone,
      'transactionOwner': transactionOwner,
      'transactionType': transactionType,
      'merchantId': merchantId,
      'merchantType': merchantType,
      'billAmount': amount,
      'serviceCharge': serviceFee,
      'VAT': vat,
      'paymentDetails': paymentDetails.toJson(),
      'billRefNo': billRefNo,
      'billReason': billReason,
      'authorization_type': authorizationType,
      'status': status,
      'createdAt': date.toIso8601String(),
    };
  }
}

class PaymentDetailsModel {
  final String violationReference;
  final String ticketNo;
  final String driverFullName;
  final DateTime issueDate;
  final Map<String, dynamic> request;

  const PaymentDetailsModel({
    required this.violationReference,
    required this.ticketNo,
    required this.driverFullName,
    required this.issueDate,
    required this.request,
  });

  factory PaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    return PaymentDetailsModel(
      violationReference: json['violationReference'] as String? ?? '',
      ticketNo: json['ticket_no'] as String? ?? '',
      driverFullName: json['driver_full_name'] as String? ?? '',
      issueDate: json['issueDate'] != null
          ? DateTime.parse(json['issueDate'] as String)
          : DateTime.now(),
      request: json['request'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'violationReference': violationReference,
      'ticket_no': ticketNo,
      'driver_full_name': driverFullName,
      'issueDate': issueDate.toIso8601String(),
      'request': request,
    };
  }
}
