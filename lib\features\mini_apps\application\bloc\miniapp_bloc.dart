import 'package:bloc/bloc.dart';
import 'package:cbrs/features/mini_apps/domain/entities/create_order_miniapp_entity.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp_success_entity.dart';
import 'package:cbrs/features/mini_apps/domain/usecases/create_order_use_case.dart';
import 'package:cbrs/features/mini_apps/domain/usecases/submit_otp_use_case.dart';
import 'package:cbrs/features/mini_apps/domain/usecases/submit_pin_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp_transaction.dart';
import 'package:cbrs/features/mini_apps/domain/usecases/get_miniapp_use_case.dart';

part 'miniapp_event.dart';
part 'miniapp_state.dart';

class MiniappBloc extends Bloc<MiniappEvent, MiniappState> {
  MiniappBloc({
    required MiniappCreateOrderUseCase miniappcreateOrderUseCase,
    required GetMiniappsUseCase getMiniappsUseCase,
    required SubmitOtpMiniappUseCase submitOtpMiniappUseCase,
    required SubmitPinMiniappUseCase submitPinMiniappUseCase,
  })  : _miniappcreateOrderUseCase = miniappcreateOrderUseCase,
        _getMiniappsUseCase = getMiniappsUseCase,
        _submitOtpMiniappUseCase = submitOtpMiniappUseCase,
        _submitPinMiniappUseCase = submitPinMiniappUseCase,
        super(const MiniappInitialState()) {
    on<GettingMiniappEvent>(_handleGetMiniapps);
    on<CreatingOrderMiniappEvent>(_handleCreateMiniappOrder);
    on<SubmitPinMiniappEvent>(_handleSubmitPin);
    on<SubmitOtpMiniappEvent>(_handleSubmitOtp);
  }
  final MiniappCreateOrderUseCase _miniappcreateOrderUseCase;
  final GetMiniappsUseCase _getMiniappsUseCase;
  final SubmitOtpMiniappUseCase _submitOtpMiniappUseCase;
  final SubmitPinMiniappUseCase _submitPinMiniappUseCase;

  Future<void> _handleGetMiniapps(
    GettingMiniappEvent event,
    Emitter<MiniappState> emit,
  ) async {
    emit(const MiniappLoadingState());
    final result = await _getMiniappsUseCase(
      GetMiniappsParams(
        page: event.page,
        perPage: event.perPage,
        stage: event.stage,
      ),
    );

    result.fold(
      (failure) => emit(MiniappErrorState(message: failure.message)),
      (miniapps) => emit(GetLoadedMiniappsState(miniapp: miniapps)),
    );
  }

  Future<void> _handleCreateMiniappOrder(
    CreatingOrderMiniappEvent event,
    Emitter<MiniappState> emit,
  ) async {
    emit(const MiniappLoadingState());
    final result = await _miniappcreateOrderUseCase(
      CreateOrderParams(
        data: event.data,
      ),
    );

    result.fold(
      (failure) => emit(MiniappErrorState(message: failure.message)),
      (miniapps) => emit(ConfirmedMiniappState(miniapp: miniapps.data)),
    );
  }

  Future<void> _handleSubmitPin(
    SubmitPinMiniappEvent event,
    Emitter<MiniappState> emit,
  ) async {
    emit(const MiniappLoadingState());
    final result = await _submitPinMiniappUseCase(
      MiniappPinParams(
        transactionType: event.transactionType,
        pin: event.pin,
        billRefNo: event.billRefNo,
      ),
    );

    result.fold(
      (failure) => emit(MiniappErrorState(message: failure.message)),
      (miniapps) => emit(PinSubmittedState(miniapp: miniapps.data)),
    );
  }

  Future<void> _handleSubmitOtp(
    SubmitOtpMiniappEvent event,
    Emitter<MiniappState> emit,
  ) async {
    emit(const MiniappLoadingState());
    final result = await _submitOtpMiniappUseCase(
      MiniappOtpParams(
        transactionType: event.transactionType,
        otp: event.otp,
        billRefNo: event.billRefNo,
      ),
    );

    result.fold(
      (failure) => emit(MiniappErrorState(message: failure.message)),
      (miniapps) => emit(OtpSubmittedState(miniapp: miniapps)),
    );
  }
}






/*
class UtilityBloc extends Bloc<UtilityEvent, UtilityState> {
  final GetUtilities _getUtilities;
  final ProcessUtilityPayment _processUtilityPayment;
  final GetUtilityTransaction _getUtilityTransaction;

  UtilityBloc({
    required GetUtilities getUtilities,
    required ProcessUtilityPayment processUtilityPayment,
    required GetUtilityTransaction getUtilityTransaction,
  })  : _getUtilities = getUtilities,
        _processUtilityPayment = processUtilityPayment,
        _getUtilityTransaction = getUtilityTransaction,
        super(const UtilityState.initial()) {
    on<LoadUtilitiesEvent>(_onLoadUtilities);
    on<ProcessUtilityPaymentEvent>(_onProcessUtilityPayment);
    on<SubmitUtilityPinEvent>(_onSubmitPin);
    on<GetUtilityTransactionEvent>(_onGetUtilityTransaction);
  }

  Future<void> _onLoadUtilities(
    LoadUtilitiesEvent event,
    Emitter<UtilityState> emit,
  ) async {
    emit(const UtilityState.loading());

    final result = await _getUtilities(
      UtilitiesParams(
        page: event.page,
        perPage: event.perPage,
        stage: event.stage,
      ),
    );

    result.fold(
      (failure) => emit(UtilityState.error(failure.message)),
      (utilities) => emit(UtilityState.loaded(utilities)),
    );
  }

  Future<void> _onProcessUtilityPayment(
    ProcessUtilityPaymentEvent event,
    Emitter<UtilityState> emit,
  ) async {
    debugPrint('Processing utility payment...');
    emit(const UtilityState.processingPayment());

    final result = await _processUtilityPayment(event.request);

    result.fold(
      (failure) {
        debugPrint('Payment failed: ${failure.message}');
        if (failure.message.contains('PIN')) {
          emit(const UtilityState.pinRequired(
            billRefNo: '20', // This should come from your API
            requiresOtp: false,
          ));
        } else {
          emit(UtilityState.paymentError(failure.message));
        }
      },
      (response) {
        debugPrint('Payment successful: $response');
        emit(UtilityState.paymentSuccess(response));
      },
    );
  }

  Future<void> _onSubmitPin(
    SubmitUtilityPinEvent event,
    Emitter<UtilityState> emit,
  ) async {
    debugPrint('Submitting PIN for utility payment...');
    emit(const UtilityState.processingPayment());

    final request = {
      ...state.paymentResponse ?? {},
      'pin': event.pin,
      'billRefNo': event.billRefNo,
    };

    final result = await _processUtilityPayment(request);

    result.fold(
      (failure) {
        debugPrint('PIN verification failed: ${failure.message}');
        emit(UtilityState.pinInvalid(failure.message));
      },
      (response) {
        debugPrint('PIN verified successfully: $response');
        emit(UtilityState.paymentSuccess(response));
      },
    );
  }

  Future<void> _onGetUtilityTransaction(
    GetUtilityTransactionEvent event,
    Emitter<UtilityState> emit,
  ) async {
    debugPrint('Getting utility transaction...');
    emit(const UtilityState.loading());

    try {
      final transaction =
          await _getUtilityTransaction.execute(event.transactionId);
      emit(UtilityState.transactionLoaded(transaction));
    } catch (e) {
      emit(UtilityState.error(e.toString()));
    }
  }
}
*/