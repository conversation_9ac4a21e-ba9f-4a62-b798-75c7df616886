{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eb8a43cbd6cb1134889d8d77b12090ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c9f288d7fcd7c8eb88680db3c76a416", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c9f288d7fcd7c8eb88680db3c76a416", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985ccfc7221de5c0663736e98cb534a4b5", "guid": "bfdfe7dc352907fc980b868725387e98fadb486075b285ced26dc074123c7de0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98596e6cc3d2b31c68d36b808a0a3d4c83", "guid": "bfdfe7dc352907fc980b868725387e982fe32e357c24218ffd83a8755a5aef4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98625765a1db6261d25e7689a680923c2a", "guid": "bfdfe7dc352907fc980b868725387e984e8ef2d0871966b0e1c3eb4e1dabb288", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c85379f412db651fe5ac6b5f06e028e", "guid": "bfdfe7dc352907fc980b868725387e98f617d1f5eb7c59ea0dbb9b965cfd32e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987239c1b5426605076fb657af31b84ca6", "guid": "bfdfe7dc352907fc980b868725387e9845e19ba25df26c3ccc091052c94434e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c3b30b88ea496db44cac2eb55db5bb9", "guid": "bfdfe7dc352907fc980b868725387e980f66a8466556e02887e640a18a15d2a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ac50cf8ac56ac906fa09a4ff23a9bd5", "guid": "bfdfe7dc352907fc980b868725387e98c7c54b07bcddd6ccbf7ab80cf5a1cd10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841fabb490c245b45387fe203033c7fb9", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4990832df41a262c87f07ae8bd6443e", "guid": "bfdfe7dc352907fc980b868725387e989b10e4f1a2392b46188b46a696fe6f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf0db35a1b7d1e911e45ba800888ee4", "guid": "bfdfe7dc352907fc980b868725387e98dd654257a1fd0c6e921147a42f67b5a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf562f84e0698ec0641426bce0df4bff", "guid": "bfdfe7dc352907fc980b868725387e98a45519d9b7df12fe88c43fd3734edd28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e1d7e563f7b10fca201a86563607c68", "guid": "bfdfe7dc352907fc980b868725387e98f682da9a79143524b5f497de034aab22", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc2ba1fb0081ee20f77b3d0c4aae4868", "guid": "bfdfe7dc352907fc980b868725387e982719fbf133fe11908b7646a8732e8c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1bf0fbb23beae0c7b9cb02a6e3ae330", "guid": "bfdfe7dc352907fc980b868725387e98e7f1adcf8bcdd041993e95b76576e783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cecb91e92bf9ebbecc03c731eecb272", "guid": "bfdfe7dc352907fc980b868725387e98c8ecff818db0dd67fdeef6fae4daf174"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98297aa91a7654b4348812265b86573287", "guid": "bfdfe7dc352907fc980b868725387e98c4a5745524eda0f2de3c7aafd595192d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dca6cf845762a3f93d4a93304639606", "guid": "bfdfe7dc352907fc980b868725387e98028d032725aacbef77923adda72dc897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bacd0becf5f9c810d506fbcd04c1e3d", "guid": "bfdfe7dc352907fc980b868725387e9862752dfe48b852effa95e644f27aa841"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981baee1ca6681f0ed6fba30574d694c4e", "guid": "bfdfe7dc352907fc980b868725387e980d82d6002380c17426352d2b6dcc403d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98610fe32bb6d8de4a51bdce25d0ee386f", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98258b7364f31ae50e76ff065e2a741a9f", "guid": "bfdfe7dc352907fc980b868725387e981104cbc90d9acbd26870d72ac58f83e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa00c4ed7a9b9ed68f8dd6c2c18dc9ab", "guid": "bfdfe7dc352907fc980b868725387e98228a73a0dea2578b100514b934e774fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e19881ef062d8330c8ec714cf392ec4a", "guid": "bfdfe7dc352907fc980b868725387e98f01d1de579313563442377b603c7f1b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988913c5f860cf83304c6f2bb737029d1e", "guid": "bfdfe7dc352907fc980b868725387e9898e57582cd28f465ead1f88705177fee"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}