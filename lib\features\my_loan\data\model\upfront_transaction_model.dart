// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_transaction_entity.dart';
import 'package:flutter/material.dart';

class UpfrontTransactionModel extends UpfrontTransactionEntity {
  const UpfrontTransactionModel({
    required super.elstRef,
    required super.billRefNo,
    required super.beneficiaryId,
    required super.beneficiaryName,
    required super.senderName,
    required super.senderPhone,
    required super.bankName,
    required super.bankCode,
    required super.senderId,
    required super.transactionOwner,
    required super.authorizationType,
    required super.status,
    required super.vat,
    required super.serviceCharge,
    required super.originalCurrency,
    required super.billAmount,
    required super.totalAmount,
    required super.facilitationFee,
    required super.createdAt,
    required super.lastModifiedAt,
    required super.transactionType,
    required super.percentage,
    required super.loanType,
    required super.paidDate,
    required super.paymentMethod,
    required super.paidAmount,
    required super.paymentDetails,
    required super.paymentReference,
  });

  factory UpfrontTransactionModel.fromJson(Map<String, dynamic> json) {

    debugPrint("json of UpfrontTransactionModel ${json} ");
    
    return UpfrontTransactionModel(
      elstRef: AppMapper.safeString(json['ELSTRef']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      senderName: AppMapper.safeString(json['senderName']),
      senderPhone: AppMapper.safeString(json['senderPhone']),
      bankName: AppMapper.safeString(json['bankName']),
      bankCode: AppMapper.safeString(json['bankCode']),
      senderId: AppMapper.safeString(json['senderId']),
      transactionOwner: AppMapper.safeString(
          json['transactionOwner'] ?? json['TransactionOwner']),
      authorizationType: AppMapper.safeString(json['authorization_type']),
      status: AppMapper.safeString(json['status']),
      vat: AppMapper.safeDouble(json['VAT']),
      serviceCharge: AppMapper.safeDouble(json['serviceCharge'] ?? 0),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      billAmount: AppMapper.safeDouble(json['billAmount']),
      totalAmount: AppMapper.safeDouble(json['totalAmount']),
      facilitationFee: AppMapper.safeDouble(json['facilitationFee']),



    
      createdAt: AppMapper.safeString(json['createdAt']),
      lastModifiedAt: AppMapper.safeString(json['lastModifiedAt']),
      transactionType: AppMapper.safeString(json['transactionType']),
      percentage: AppMapper.safeString(json['percentage']),
      loanType: AppMapper.safeString(json['loanType']),
      paidDate: AppMapper.safeString(json['paidDate']),
      paymentMethod: AppMapper.safeString(json['paymentMethod']),
      paidAmount: AppMapper.safeDouble(json['paidAmount'] ?? 0),
      paymentDetails: UpfrontPaymentDetailsModel.fromJson(
          AppMapper.safeMap(json['paymentDetails'])),
      paymentReference: AppMapper.safeString(json['paymentReference']),
    );
  }
}

class UpfrontPaymentDetailsModel extends UpfrontPaymentDetailsEntity {
  const UpfrontPaymentDetailsModel({
    required super.walletId,
    required super.currency,
  });

  factory UpfrontPaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    return UpfrontPaymentDetailsModel(
      walletId: AppMapper.safeString(json['walletId']),
      currency: AppMapper.safeString(json['currency']),
    );
  }
}
