import 'package:cbrs/core/common/widgets/confirm/custom_transaction_confirm_screen.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_row_transaction.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/extensions/string_extensions.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/download_reciept_url.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';

// import 'package:cbrs/features/home/<USER>/widgets/transactions/dotted_line_painter.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';

class TransactionDetailPage extends StatelessWidget {
  const TransactionDetailPage({required this.transaction, super.key});
  final Transaction transaction;

//  transactionID: extra['transactionID'] as String,

  void _downloadReceipt(BuildContext context) {
    context.read<WalletTransferBloc>().add(
          GenerateRecieptEvent(
            billRefNo: transaction.billRefNo,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Transactions Detail',
        ),
      ),
      body: BlocConsumer<WalletTransferBloc, WalletTransferState>(
        listener: (context, state) {
          if (state is WalletTransferFailure) {
            CustomToastification(context, message: state.message);
          }
          if (state is GeneratedReceiptState) {
            HandleDownloadReciept.downloadReceipt(context, state.receiptUrl);
          }
        },
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0.h),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: CustomTransactionConfirmScreen(
                      totalAmount:
                          AppMapper.safeDouble(transaction.totalAmount),
                      description: 'Transaction Amount',
                      customImage: Container(
                        width: 96.w,
                        height: 96.h,
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 16.h,
                        ),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).secondaryHeaderColor,
                        ),
                        child: Center(
                          child: Image.asset(
                            'assets/images/empty_transaction_screen_img.png',
                            width: 56.w,
                            height: 56.h,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                      child: Column(
                        children: [
                          _buildTransactionDetails(),
                        ],
                      ),
                    ),

                    /* 
                     Column(
                      children: [
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(10.w),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(24.r),
                            boxShadow: [
                              BoxShadow(
                                color: Color(0xFF000000).withOpacity(0.06),
                                blurRadius: 20
                              )
                            ]
                          ),
                          child: 
                          
                          
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                           
                              Center(
                                child: Container(
                                  width: 80.w,
                                  height: 80.h,
                                  decoration: const BoxDecoration(
                                    color: Color(0xFFE6F7E2),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(10.w),
                                    child: Image.asset(
                                      'assets/images/empty_transaction_screen_img.png',
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(height: 0.h),
                              _buildAmountSection(),
                              Text(
                                'Transaction Details',
                                style: GoogleFonts.outfit(
                                  fontSize: 17.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                              SizedBox(height: 5.h),
                              _buildTransactionDetails(),
                            ],
                          ),
                       
                       
                       
                        ),
                      ],
                    ),
                
                
                */
                  ),
                ),
                SafeArea(
                  top: false,
                  child: _buildGetReceiptButton(context),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAmountSection() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(
            horizontal: 22.w,
            vertical: 0.h,
          ),
          margin: EdgeInsets.only(bottom: 2.h),
          child: Text(
            "${AppMapper.safeFormattedNumberWithDecimal(
              transaction.totalAmount ?? '',
            )} ETB",
            style: GoogleFonts.outfit(
              fontSize: 40.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Text(
          'Transaction Amount',
          style: GoogleFonts.outfit(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black54,
          ),
        ),
        SizedBox(height: 15.h),
      ],
    );
  }

  Widget _buildTransactionDetails() {
    return Column(
      children: [
        _buildDetailRow('Sender Name', transaction.senderName),
        _buildDetailRow(
          'Sender Account',
          transaction.beneficiaryAccountNo ?? '',
        ),
        _buildDetailRow('Bank Name', transaction.bankName ?? ''),
        _buildDetailRow(
          'Date',
          AppMapper.safeFormattedDate(transaction.paidDate ?? ''),
        ),
        _buildDetailRow('Transfer Type', 'Add Money'),
        _buildDetailRow('Reference Number', transaction.billRefNo),
        _buildDetailRow(
          'VAT (15%)',
          transaction.vat != null 
              ? '${transaction.vat} ETB'
              : '',
        ),

        // _buildDetailRow('VAT(15%)', "${transaction?.vat} ETB"),

        _buildDetailRow(
          'Amount',
          '${AppMapper.safeFormattedNumberWithDecimal(
            transaction.paidAmount.toString(),
          )}ETB',
        ),
        _buildDetailRow(
          'Service Fee',
          AppMapper.safeFormattedNumberWithDecimal(
                transaction.serviceCharge?.toString(),
              ) ??
              '',
        ),
        _buildDetailRow('VAT(15%):', transaction.vat?.toString() ?? ''),
        _buildDetailRow(
          'Transaction Status:',
          transaction.status.substring(0, 1).toUpperCase() +
              transaction.status.substring(1).toLowerCase(),
          isStatus: true,
        ),
        SizedBox(height: 16.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w),
          height: 1,
          child: CustomPaint(
            painter: DottedLinePainter(
              color: Colors.grey.withOpacity(0.2),
            ),
            size: const Size(double.infinity, 1),
          ),
        ),
        SizedBox(height: 16.h),
        _buildDetailRow(
          'Total Amount ',
          "${AppMapper.safeFormattedNumberWithDecimal(
            transaction.totalAmount ?? '',
          )} ETB",
          isTotal: true,
          textStyle: GoogleFonts.outfit(
            fontSize: 24.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    bool isStatus = false,
    bool isTotal = false,
    TextStyle? textStyle,
  }) {
    return value.isNotEmpty
        ? !isStatus
            ? CustomRowTransaction(
                label: label,
                value: value,
                isLast: isTotal,
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: GoogleFonts.outfit(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 16),
                  if (isStatus)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFD4FECB),
                        borderRadius: BorderRadius.circular(22),
                      ),
                      child: CustomBuildText(
                        text: value,
                        style: GoogleFonts.outfit(
                          color: const Color(0xFF3EA100),
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                ],
              )
        : const SizedBox.shrink();
  }

  Widget _buildGetReceiptButton(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(32.r),
              color: Colors.white,
              border: Border.all(
                color: Theme.of(context).primaryColor,
              ),
            ),
            child: TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Back',
                style: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Container(
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(32.r),
              color: Theme.of(context).primaryColor,
            ),
            child: BlocConsumer<WalletTransferBloc, WalletTransferState>(
              listener: (context, state) {},
              builder: (context, state) {
                return TextButton(
                  onPressed: () {
                    _downloadReceipt(context);
                  },
                  child:
                  state is WalletTransferLoading
                                  ? SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 3,
                                      ),
                                    )
                                  :
                   Text(
                    'Get Receipt',
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
