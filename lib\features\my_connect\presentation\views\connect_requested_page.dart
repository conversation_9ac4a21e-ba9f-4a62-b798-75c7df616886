// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:math';

import 'package:cbrs/features/my_connect/presentation/views/my_connect_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_name_and_phone.dart';
import 'package:cbrs/core/common/widgets/custom_name_or_avatar.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/custom_toast.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/my_connect/applications/bloc/my_connect_bloc.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_list_response_entity.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/my_connect_btn_card.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/empty_connection_card.dart';

class ConnectRequestedPage extends StatefulWidget {
  const ConnectRequestedPage({super.key});

  @override
  State<ConnectRequestedPage> createState() => _ConnectRequestedPageState();
}

class _ConnectRequestedPageState extends State<ConnectRequestedPage> {
  @override
  void initState() {
    super.initState();
    _loadConnections();
  }

  void _loadConnections() {
    // Load received requests
    // context.read<MyConnectBloc>().add(
    //       const FetchConnectionsEvent(
    //         scope: 'received',
    //         status: 'pending',
    //       ),
    //     );

    // Load sent requests
    context.read<MyConnectBloc>().add(
          const FetchConnectionsEvent(
            scope: 'sent',
            status: 'pending',
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Requests'),
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            _loadConnections();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Received Requests Section
                BlocBuilder<MyConnectBloc, MyConnectState>(
                  builder: (context, state) {
                    if (state is ConnectionsLoadedState) {
                      final receivedRequests = state.connections
                          .where((req) => req.status == 'pending')
                          .toList();

                      return _buildReceivedRequestsSection(
                          receivedRequests, state.meta);
                    }
                    if (state is MyConnectLoadingState) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }
                    if (state is MyConnectErrorState) {
                      return _buildErrorWidget(
                        state.message,
                        _loadConnections,
                      );
                    }
                    return const SizedBox();
                  },
                ),

                const SizedBox(height: 20),

                // Sent Requests Section
                BlocBuilder<MyConnectBloc, MyConnectState>(
                  builder: (context, state) {
                    if (state is ConnectionsLoadedState) {
                      final sentRequests = state.connections
                          .where((req) => req.status == 'pending')
                          .toList();

                      return _buildSentRequestsSection(
                          sentRequests, state.meta);
                    }
                    if (state is MyConnectLoadingState) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }
                    if (state is MyConnectErrorState) {
                      return _buildErrorWidget(
                        state.message,
                        _loadConnections,
                      );
                    }
                    return const SizedBox();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String message, VoidCallback onRetry) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRetry,
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyStateWidget(String message, VoidCallback onRefresh) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.grey,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRefresh,
            child: Text('Refresh'),
          ),
        ],
      ),
    );
  }

  Widget _buildReceivedRequestsSection(
    List<ConnectionRequestEntity> requests,
    PaginationMetaEntity meta,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomPagePadding(
          child: CustomPageHeader(
            pageTitle: 'Connection Requests (${requests.length})',
            description: 'View and Manage your connect requests',
          ),
        ),
        SizedBox(height: 12),
        if (requests.isEmpty)
          const EmptyConnectionCard()
        else
          ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (context, index) {
              final request = requests[index];
              if (request == null) return SizedBox.shrink();
              return _RequestCard(request: request);
            },
            separatorBuilder: (context, index) => const SizedBox(height: 6),
            itemCount: requests.length,
          ),
        if (meta.hasNextPage)
          _SeeMoreText(
            onTap: () {
              context.read<MyConnectBloc>().add(
                    FetchConnectionsEvent(
                      scope: 'received',
                      status: 'pending',
                      page: meta.currentPage + 1,
                    ),
                  );
            },
          ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _buildSentRequestsSection(
    List<ConnectionRequestEntity> requests,
    PaginationMetaEntity meta,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomPagePadding(
          child: CustomPageHeader(
            pageTitle: 'Sent Requests (${requests.length})',
            description: 'View and Manage your sent requests',
          ),
        ),
        if (requests.isEmpty)
          const EmptyConnectionCard()
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) =>
                _SentRequestCard(request: requests[index]),
            separatorBuilder: (context, index) => const SizedBox(height: 6),
            itemCount: requests.length,
          ),
        if (meta.hasNextPage)
          _SeeMoreText(
            onTap: () {
              context.read<MyConnectBloc>().add(
                    FetchConnectionsEvent(
                      scope: 'sent',
                      status: 'pending',
                      page: meta.currentPage + 1,
                    ),
                  );
            },
          ),
      ],
    );
  }
}

class _RequestCard extends StatelessWidget {
  final ConnectionRequestEntity request;

  const _RequestCard({
    required this.request,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPagePadding(
      margin: const EdgeInsets.fromLTRB(16, 4, 16, 4),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.08), blurRadius: 20),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: MyConnectPersonDetail(
                  // avatar: 'hh',
                  // name: 'hh',
                  // phoneOrEmail: 'hh',

                  // phoneOrEmail: request?.recipient?.phoneNumber ?? '09002',
                  avatar: request.requester?.firstName ?? 'hhhhh',
                  name: request?.recipient?.fullName ?? 'hhh',
                  phoneOrEmail: 'hhhh',
                ),
              ),
              Text(request.recipient?.phoneNumber ?? 'j'),
              CustomBuildText(
                text: _formatDate(request.sentAt),
                color: Color(0xFf7C7C7C),
                fontSize: 12,
                caseType: '',
              ),
            ],
          ),
          const SizedBox(
            height: 6,
          ),
          Row(
            children: [
              const CustomBuildText(
                text: 'Connect Request',
                caseType: '',
                color: Color(0xFFEF910E),
              ),
              const Spacer(),
              MyConnectBtnCard(
                onTap: () {
                  context.read<MyConnectBloc>().add(
                        RejectConnectionRequestEvent(
                          requestId: request.id,
                        ),
                      );
                  CustomToastification(context, message: 'Connection Declined');
                },
                borderColor: Theme.of(context).primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: const CustomBuildText(text: 'Decline'),
              ),
              const SizedBox(
                width: 8,
              ),
              MyConnectBtnCard(
                onTap: () {
                  context.read<MyConnectBloc>().add(
                        AcceptConnectionRequestEvent(
                          requestId: request.id,
                        ),
                      );
                  CustomToastification(
                    context,
                    message: 'Connection accepted',
                    isError: false,
                  );
                },
                hasBorder: false,
                bgColor: Theme.of(context).primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: const CustomBuildText(
                  text: 'Connect',
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }
}

class _SentRequestCard extends StatelessWidget {
  final ConnectionRequestEntity request;

  const _SentRequestCard({
    required this.request,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPagePadding(
      margin: const EdgeInsets.fromLTRB(16, 4, 16, 4),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.04), blurRadius: 20),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: MyConnectPersonDetail(
                  avatar: request.recipient.firstName ?? '',
                  name: request.recipient.lastName ?? '',
                  // phoneOrEmail: request.recipient.phoneNumber ?? '',
                  phoneOrEmail: 'hhhh',
                ),
              ),
              MyConnectBtnCard(
                onTap: () {
                  context.read<MyConnectBloc>().add(
                        RejectConnectionRequestEvent(
                          requestId: request.id,
                        ),
                      );
                  CustomToastification(context, message: 'Request cancelled');
                },
                bgColor: Theme.of(context).secondaryHeaderColor,
                hasBorder: false,
                padding:
                    const EdgeInsets.symmetric(vertical: 2, horizontal: 12),
                child: const CustomBuildText(text: 'Cancel'),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CustomBuildText(
                text: _formatDate(request.sentAt),
                color: Color(0xFf7C7C7C),
                fontSize: 12,
                caseType: '',
              ),
            ],
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }
}

class MyConnectPersonDetail extends StatelessWidget {
  const MyConnectPersonDetail({
    required this.avatar,
    required this.name,
    required this.phoneOrEmail,
    super.key,
  });

  final String avatar;
  final String name;
  final String phoneOrEmail;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CustomNameOrAvatar(name: name, avatar: avatar),
        SizedBox(width: 5.w),
        CustomNameAndPhone(name: name, phoneOrEmail: phoneOrEmail),
      ],
    );
  }
}

class _SeeMoreText extends StatelessWidget {
  const _SeeMoreText({
    super.key,
    this.onTap,
  });
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(32),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 20,
            ),
          ],
        ),
        child: InkWell(
          onTap: onTap,
          child: CustomBuildText(
            text: 'See More',
            color: Theme.of(context).primaryColor,
            fontSize: 16,
          ),
        ),
      ),
    );
  }
}
