// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_name_and_phone.dart';
import 'package:cbrs/core/common/widgets/custom_name_or_avatar.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/custom_toast.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/my_connect_btn_card.dart';

class ConnectRequestedPage extends StatelessWidget {
  const ConnectRequestedPage({super.key});
  final int conctRequestCount = 4;
  final int sentRequestCount = 6;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Requests'),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomPagePadding(
                child: CustomPageHeader(
                  pageTitle: 'Connection Requests ($conctRequestCount)',
                  description: 'View and Manage your connect requests',
                ),
              ),
              SizedBox(height: 12,),
              ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemBuilder: (context, index) => const _RequestCard(),
                separatorBuilder: (context, index) => const SizedBox(height: 6),
                itemCount: conctRequestCount,
              ),
              const SizedBox(
                height: 10,
              ),
              _SeeMoreText(onTap: () {}),
              const SizedBox(
                height: 10,
              ),
              CustomPagePadding(
                child: CustomPageHeader(
                  pageTitle: 'Sent Requests ($sentRequestCount)',
                  description: 'View and Manage your sent requests',
                ),
              ),
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) => const _SentRequestCard(),
                separatorBuilder: (context, index) => const SizedBox(height: 6),
                itemCount: sentRequestCount,
              ),
              const SizedBox(
                height: 10,
              ),
              _SeeMoreText(onTap: () {}),
            ],
          ),
        ),
      ),
    );
  }
}

class _RequestCard extends StatelessWidget {
  const _RequestCard({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomPagePadding(
      margin: const EdgeInsets.fromLTRB(16,4,16,4),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.08), blurRadius: 20),
        ],
      ),
      child: Column(
        children: [
          const Row(
            children: [
              Expanded(
                child: MyConnectPersonDetail(
                  avatar: 'avatar',
                  name: 'name',
                  phoneOrEmail: 'phoneOrEmail',
                ),
              ),
              CustomBuildText(
                text: '2 Days ago',
                color: Color(0xFf7C7C7C),
                fontSize: 12,
                caseType: '',
              ),
            ],
          ),
          const SizedBox(
            height: 6,
          ),
          Row(
            children: [
              const CustomBuildText(
                text: 'Connect Request',
                caseType: '',
                color: Color(0xFFEF910E),
              ),
              const Spacer(),
              MyConnectBtnCard(
                onTap: () {
                  CustomToastification(context, message: 'Connection Declined');
                },
                borderColor: Theme.of(context).primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: const CustomBuildText(text: 'Decline'),
              ),
              const SizedBox(
                width: 8,
              ),
              MyConnectBtnCard(
                onTap: () {
                  CustomToastification(
                    context,
                    message: 'Connection accepted',
                    isError: false,
                  );
                },
                hasBorder: false,
                bgColor: Theme.of(context).primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: const CustomBuildText(
                  text: 'Connect',
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }
}

class _SentRequestCard extends StatelessWidget {
  const _SentRequestCard({super.key});

  @override
  @override
  Widget build(BuildContext context) {
    return CustomPagePadding(
          margin: const EdgeInsets.fromLTRB(16,4,16,4),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),

      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.04), blurRadius: 20),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Expanded(
                child: MyConnectPersonDetail(
                  avatar: 'avatar',
                  name: 'name',
                  phoneOrEmail: 'phoneOrEmail',
                ),
              ),
              MyConnectBtnCard(
                bgColor: Theme.of(context).secondaryHeaderColor,
                hasBorder: false,
                padding:
                    const EdgeInsets.symmetric(vertical: 2, horizontal: 12),
                child: const CustomBuildText(text: 'Cancel'),
              ),
            ],
          ),
          const SizedBox(
            height: 6,
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }
}

class MyConnectPersonDetail extends StatelessWidget {
  const MyConnectPersonDetail({
    required this.avatar,
    required this.name,
    required this.phoneOrEmail,
    super.key,
  });

  final String avatar;
  final String name;
  final String phoneOrEmail;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CustomNameOrAvatar(name: name, avatar: avatar),
        SizedBox(width: 5.w),
        CustomNameAndPhone(name: name, phoneOrEmail: phoneOrEmail),
      ],
    );
  }
}

class _SeeMoreText extends StatelessWidget {
  const _SeeMoreText({
    super.key,
    this.onTap,
  });
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(32),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 20,
            ),
          ],
        ),
        child: InkWell(
          onTap: onTap,
          child: CustomBuildText(
            text: 'See More',
            color: Theme.of(context).primaryColor,
            fontSize: 16,
          ),
        ),
      ),
    );
  }
}
