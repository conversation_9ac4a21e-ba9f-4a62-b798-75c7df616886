import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/price_formatter.dart';
import 'package:cbrs/features/guest/presentation/widget/show_guest_mode_bottom_sheet.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_item_image_carousel.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_item_info_cards.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_item_specifications.dart';
import 'package:cbrs/features/loans/presentation/widgets/similar_loan_items_section.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_player/video_player.dart';

class LoanItemDetailPage extends StatefulWidget {
  const LoanItemDetailPage({
    required this.loanItem,
    super.key,
    this.isGuest = false,
  });

  final LoanItem loanItem;
  final bool isGuest;

  @override
  State<LoanItemDetailPage> createState() => _LoanItemDetailPageState();
}

class _LoanItemDetailPageState extends State<LoanItemDetailPage> {
  final PageController _pageController = PageController();
  final Map<int, VideoPlayerController> _videoControllers = {};
  final Map<int, VideoPlayerController> _thumbnailControllers = {};
  int _currentImageIndex = 0;

  @override
  void initState() {
    super.initState();
    debugPrint('LoanItemDetailPage.initState: isGuest = ${widget.isGuest}');
    _initializeVideoControllers();
  }

  void _initializeVideoControllers() {
    for (var i = 0; i < widget.loanItem.galleryImages.length; i++) {
      final image = widget.loanItem.galleryImages[i];
      if (image.type == 'video') {
        try {
          // Main video controller
          final controller = VideoPlayerController.networkUrl(
            Uri.parse(image.url),
          )..setLooping(true);

          controller.initialize().then((_) {
            if (mounted) {
              setState(() {
                _videoControllers[i] = controller;
              });
            }
          }).catchError((Object error) {
            debugPrint('Error initializing video controller: $error');
          });

          // Thumbnail video controller
          final thumbnailController = VideoPlayerController.networkUrl(
            Uri.parse(image.url),
          )..setLooping(true);

          _thumbnailControllers[i] = thumbnailController;
          thumbnailController.initialize().then((_) {
            if (mounted) setState(() {});
          }).catchError((Object error) {
            debugPrint('Error initializing thumbnail controller: $error');
          });
        } catch (e) {
          debugPrint('Error creating video controller: $e');
        }
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _videoControllers.values) {
      controller.dispose();
    }
    for (final controller in _thumbnailControllers.values) {
      controller.pause();
      controller.dispose();
    }
    _thumbnailControllers.clear();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          _getPageTitle(),
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          // IconButton(
          //   icon: const Icon(Icons.share),
          //   onPressed: _shareItem,
          // ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image/Video Carousel
                  LoanItemImageCarousel(
                    galleryImages: widget.loanItem.galleryImages,
                    pageController: _pageController,
                    videoControllers: _videoControllers,
                    virtualTour: widget.loanItem.virtualTour,
                    onPageChanged: (index) {
                      setState(() {
                        _currentImageIndex = index;
                      });
                    },
                  ),

                  // Thumbnail Navigation
                  if (widget.loanItem.galleryImages.length > 1) ...[
                    const SizedBox(height: 16),
                    _buildThumbnailSection(),
                  ],

                  // Item Basic Info
                  _buildBasicInfo(),

                  // Quick Info Cards
                  LoanItemInfoCards(loanItem: widget.loanItem),

                  // Specifications
                  if (widget.loanItem.specifications.isNotEmpty) ...[
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Text(
                        'Specifications',
                        style: GoogleFonts.outfit(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    LoanItemSpecifications(loanItem: widget.loanItem),
                  ],

                  if (widget.loanItem.description.isNotEmpty)
                    _buildDescription(),

                  if (widget.loanItem.type == LoanItemType.house &&
                      widget.loanItem.location != null)
                    _buildLocationSection(),

                  // Amenities (for properties)
                  if (widget.loanItem.type == LoanItemType.house &&
                      widget.loanItem.amenities?.isNotEmpty == true)
                    _buildAmenitiesSection(),

                  // Similar Items
                  SimilarLoanItemsSection(
                    loanItem: widget.loanItem,
                    onItemTap: _navigateToSimilarItem,
                  ),

                  SizedBox(height: 20.h),
                ],
              ),
            ),
          ),
          _buildBottomBar(),
        ],
      ),
    );
  }

  Widget _buildThumbnailSection() {
    return ConstrainedBox(
      constraints: const BoxConstraints(
        maxHeight: 90,
        minHeight: 90,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        margin: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
            ),
          ],
        ),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: widget.loanItem.galleryImages.length,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemBuilder: (context, index) => _buildThumbnail(
            widget.loanItem.galleryImages[index],
            index,
          ),
        ),
      ),
    );
  }

  Widget _buildThumbnail(GalleryImage media, int index) {
    return GestureDetector(
      onTap: () {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: _currentImageIndex == index
                ? Theme.of(context).primaryColor
                : Colors.grey.shade300,
            width: _currentImageIndex == index ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: media.type == 'video'
              ? Stack(
                  alignment: Alignment.center,
                  children: [
                    if (_thumbnailControllers.containsKey(index))
                      SizedBox(
                        width: 60,
                        height: 60,
                        child: VideoPlayer(_thumbnailControllers[index]!),
                      )
                    else
                      Container(
                        width: 60,
                        height: 60,
                        color: Colors.grey[300],
                      ),
                    Container(
                      width: 60,
                      height: 60,
                      color: Colors.black26,
                      child: const Icon(
                        Icons.play_circle_outline,
                        color: Colors.white,
                      ),
                    ),
                  ],
                )
              : CachedNetworkImage(
                  imageUrl: media.url,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  errorWidget: (context, url, error) => Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[300],
                    child: const Icon(Icons.error),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              final maxWidth = constraints.maxWidth;
              // Responsive padding
              final padding = (maxWidth * 0.03).clamp(8.0, 16.0);

              return Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          widget.loanItem.name,
                          style: GoogleFonts.outfit(
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: Text(
                          widget.loanItem.category.name,
                          style: GoogleFonts.outfit(
                            fontSize: 14.sp,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 14.h),
                  Row(
                    children: [
                      if (widget.loanItem.location != null) ...[
                        Icon(
                          Icons.location_on,
                          size: 20.sp,
                          color: Theme.of(context).primaryColor,
                        ),
                        SizedBox(width: padding * 0.5),
                        Expanded(
                          child: Text(
                            widget.loanItem.location!.fieldName,
                            style: GoogleFonts.outfit(
                              color: const Color(0xFF000000)
                                  .withValues(alpha: 0.5),
                              fontSize: 14.sp,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ] else ...[
                        if (widget.loanItem.subtitle != null)
                          Expanded(
                            child: Text(
                              widget.loanItem.subtitle!,
                              style: GoogleFonts.outfit(
                                color: const Color(0xFF000000)
                                    .withValues(alpha: 0.5),
                                fontSize: 14.sp,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          )
                        else
                          const Expanded(child: SizedBox()),
                      ],
                      Text(
                        PriceFormatter.formatPrice(
                          widget.loanItem.price.toString(),
                        ),
                        style: GoogleFonts.outfit(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
          SizedBox(height: 8.h),
          const Divider(color: Color(0xFFE3E4F0)),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: const Color(0xFF2C2B34).withValues(alpha: 0.04),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'DESCRIPTION',
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            widget.loanItem.description,
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              fontWeight: FontWeight.normal,
              color: Colors.black.withValues(alpha: 0.5),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection() {
    if (widget.loanItem.location == null) return const SizedBox.shrink();

    final location = widget.loanItem.location!;
    final latitude = double.tryParse(location.lat) ?? 0.0;
    final longitude = double.tryParse(location.lng) ?? 0.0;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Location',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 12.h),
          Container(
            height: 200.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.r),
              color: Colors.grey[200],
            ),
            clipBehavior: Clip.antiAlias,
            child: Stack(
              children: [
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: LatLng(latitude, longitude),
                    zoom: 15,
                  ),
                  markers: {
                    Marker(
                      markerId: const MarkerId('property'),
                      position: LatLng(latitude, longitude),
                      infoWindow: InfoWindow(title: location.fieldName),
                    ),
                  },
                  zoomControlsEnabled: false,
                  mapToolbarEnabled: false,
                  myLocationButtonEnabled: false,
                ),
                Positioned(
                  right: 12.w,
                  bottom: 12.h,
                  child: FloatingActionButton.small(
                    onPressed: () => _launchGoogleMaps(latitude, longitude),
                    backgroundColor: Theme.of(context).primaryColor,
                    child: const Icon(
                      Icons.directions,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmenitiesSection() {
    if (widget.loanItem.amenities?.isEmpty != false) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Amenities',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 12.h),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 0.8,
              crossAxisSpacing: 8.w,
              mainAxisSpacing: 8.h,
            ),
            itemCount: widget.loanItem.amenities!.length,
            itemBuilder: (context, index) {
              final amenity = widget.loanItem.amenities![index];
              return Container(
                padding: EdgeInsets.all(6.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                  border: Border.all(color: const Color(0xFFEFEFEF)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 20,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 16.w,
                      height: 16.h,
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.84.r),
                      ),
                      child: CachedNetworkImage(
                        imageUrl: amenity.amenity.icon,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[300],
                          child: const Center(
                            child: CircularProgressIndicator(strokeWidth: 1.5),
                          ),
                        ),
                        errorWidget: (context, url, error) => Icon(
                          Icons.home_outlined,
                          size: 16.w,
                          color: Colors.grey[400],
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      amenity.amenity.name,
                      style: GoogleFonts.outfit(
                        fontSize: 12.sp,
                        color: const Color(0xFF727272),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      amenity.detail,
                      style: GoogleFonts.outfit(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildVirtualTourSection() {
    if (widget.loanItem.virtualTour?.isEmpty != false) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Virtual Tour',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 12.h),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(color: const Color(0xFFEFEFEF)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                ),
              ],
            ),
            child: GestureDetector(
              onTap: _launchVirtualTour,
              child: Row(
                children: [
                  Container(
                    width: 48.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      Icons.view_in_ar,
                      color: Theme.of(context).primaryColor,
                      size: 24.w,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Take a Virtual Tour',
                          style: GoogleFonts.outfit(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          widget.loanItem.type == LoanItemType.house
                              ? 'Experience the property in 360° view'
                              : 'Take a virtual tour of this vehicle',
                          style: GoogleFonts.outfit(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey[400],
                    size: 16.w,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchGoogleMaps(double latitude, double longitude) async {
    final url = Uri.parse(
      'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude',
    );
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchVirtualTour() async {
    if (widget.loanItem.virtualTour?.isNotEmpty == true) {
      final url = Uri.parse(widget.loanItem.virtualTour!);
      if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Could not open virtual tour')),
          );
        }
      }
    }
  }

  Widget _buildBottomBar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
          ),
        ],
      ),
      child: CustomRoundedBtn(
        btnText: 'Apply For Loan',
        onTap: _applyForLoan,
        isLoading: false,
      ),
    );
  }

  String _getPageTitle() {
    switch (widget.loanItem.type) {
      case LoanItemType.car:
        return 'Car Details';
      case LoanItemType.house:
        return 'Property Details';
      case LoanItemType.general:
        return 'Loan Details';
    }
  }

  void _shareItem() {
    // TODO(dev): Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }

  void _applyForLoan() {
    // Pause all videos
    for (final controller in _videoControllers.values) {
      if (controller.value.isPlaying) {
        controller.pause();
      }
    }

    debugPrint('_applyForLoan called, isGuest: ${widget.isGuest}');

    if (widget.isGuest) {
      debugPrint('Showing guest mode bottom sheet');
      showGuestModeBottomSheet(context);
    } else {
      debugPrint('Navigating to loan application');
      _navigateToLoanApplication();
    }
  }

  void _navigateToLoanApplication() {
    // Navigate to loan bank info page where user can select bank and payment options
    context.pushNamed(
      AppRouteName.loanBankInfo,
      extra: {
        'loanItem': widget.loanItem,
      },
    );
  }

  void _navigateToSimilarItem(LoanItem item) {
    // Pause all videos before navigation
    for (final controller in _videoControllers.values) {
      if (controller.value.isPlaying) {
        controller.pause();
      }
    }

    context.pushReplacementNamed(
      AppRouteName.loanItemDetails,
      extra: {
        'loanItem': item,
        'isGuest': widget.isGuest,
      },
    );
  }
}
