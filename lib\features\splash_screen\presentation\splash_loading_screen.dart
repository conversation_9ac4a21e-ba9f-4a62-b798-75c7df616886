import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/constants/storage_keys.dart';
import 'package:cbrs/core/services/connectivity/connectivity_controller.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/services/storage/hive_box_manager.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';

class SplashLoadingScreen extends StatefulWidget {
  const SplashLoadingScreen({super.key});

  @override
  State<SplashLoadingScreen> createState() => _SplashLoadingScreenState();
}

class _SplashLoadingScreenState extends State<SplashLoadingScreen>
    with TickerProviderStateMixin {
  bool _isCheckingConnectivity = false;

  late HiveBoxManager _hiveBoxManager;
  late AuthLocalDataSource _authLocalDataSource;
  late AnimationController _lottieController;

  @override
  void initState() {
    _hiveBoxManager = sl<HiveBoxManager>();
    _authLocalDataSource = sl<AuthLocalDataSource>();
    _lottieController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    initialize();
    super.initState();
  }

  @override
  void dispose() {
    _lottieController.dispose();
    super.dispose();
  }

  Future<void> initialize() async {
    try {
      if (!_isCheckingConnectivity) {
        _isCheckingConnectivity = true;
        final connectivityController = Get.find<ConnectivityController>();
        if (!connectivityController.isConnected) {
          _isCheckingConnectivity = false;
          context.goNamed(AppRouteName.connectionLost);
        }
        _isCheckingConnectivity = false;
      }

      // _hiveBoxManager.mainBox.delete(StorageKeys.firstTimer);

      //.r(StorageKeys.firstTimer, defaultValue: true) as bool;

      final deviceCheckController = Get.find<DeviceCheckController>();
      await deviceCheckController.checkDeviceOnStartup();

      GlobalVariable.isDeviceVerified = deviceCheckController.isDeviceVerified;
      final connectivityController = Get.find<ConnectivityController>();

      if (!connectivityController.isConnected ||
          deviceCheckController.noConnectivity) {
        context.goNamed(AppRouteName.connectionLost);
      }

      debugPrint(
        'From splassh scree   GlobalVariable.isDeviceRegisteredOnConnect ${GlobalVariable.isDeviceRegisteredOnConnect} and  ${GlobalVariable.isDeviceVerified}',
      );
      if (GlobalVariable.isDeviceVerified ||
          GlobalVariable.isDeviceRegisteredOnConnect) {
        await _authLocalDataSource.clearAuthToken();
        await _authLocalDataSource.clearUserData();
        return context.goNamed(AppRouteName.tokenDeviceLogin);
      } else if (GlobalVariable.deviceCheckNetworkError) {
        return context.goNamed(AppRouteName.connectionLost);
      } else {
        final isFirstTimer = _hiveBoxManager.mainBox
            .get(StorageKeys.firstTimer, defaultValue: true) as bool;

        if (isFirstTimer) {
          return context.goNamed(AppRouteName.onboarding);
        }

        return context.goNamed(AppRouteName.guestHomePage);
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
        await _authLocalDataSource.clearAuthToken();
        context.goNamed(AppRouteName.tokenDeviceLogin);
        return;
      }

      context.goNamed(AppRouteName.connectionLost);
    } catch (ex) {
      context.goNamed(AppRouteName.connectionLost);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: 
      
      Center(
        child: Lottie.asset(
          'assets/json/CONNECT_BIRR_LOTTIE.json',
          controller: _lottieController,
          width: 200,
          height: 200,
          fit: BoxFit.contain,
          onLoaded: (composition) {
            _lottieController
              ..duration = composition.duration
              ..repeat();
          },
        ),
      ),
    );
  }
}
