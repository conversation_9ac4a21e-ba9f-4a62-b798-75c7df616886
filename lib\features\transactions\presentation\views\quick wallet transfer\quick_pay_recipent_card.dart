import 'dart:math';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class QuickPayRecipentCard extends StatelessWidget {
  const QuickPayRecipentCard({
    required this.recipientName,
    super.key,
    this.recipientAvatar,
    this.recipientEmail,
    this.recipientPhone,
    this.hasSuffix = false,
  });
  final String recipientName;
  final String? recipientAvatar;
  final String? recipientEmail;
  final String? recipientPhone;
  final bool hasSuffix;

  String formatInternationalPhone(String phone) {
    // Clean the phone number
    phone = phone.replaceAll(RegExp(r'[^+\d]'), '');

    final match = RegExp(r'^\+?(\d{1,4})(\d{8,})$').firstMatch(phone);
    if (match != null) {
      final countryCode = match.group(1)!;
      final number = match.group(2)!;

      debugPrint('Country $countryCode number $number');

      if (number.length >= 8) {
        final part1 = number.substring(0, 4);
        final part2 = number.substring(4, 8);
        final remaining = number.length > 8 ? ' ${number.substring(8)}' : '';

        debugPrint('pRT1 $part1 part2 $part2 part3 $remaining');

        return '+$countryCode  ($part1) ($part2)$remaining';
      }
    }

    // Fallback to original if not match
    return phone;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 20,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child:
             Row(
              // mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).primaryColor,
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: recipientAvatar != null && recipientAvatar!.isNotEmpty
                      ? CustomCachedImage(
                          url: recipientAvatar!,
                        )
                      : Center(
                          child: CustomBuildText(
                            text: recipientName.isNotEmpty
                                ? recipientName
                                    .substring(0, min(2, recipientName.length))
                                    .toUpperCase()
                                : '??',

                            //  recipientName.isNotEmpty
                            //     ? recipientName.substring(0, 1).toUpperCase()
                            //     : '?',
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            caseType: 'all',
                          ),
                        ),
                ),
                SizedBox(width: 5.w),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomBuildText(
                        text: recipientName,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                      CustomBuildText(
                        text: recipientPhone != null
                            ? formatInternationalPhone(recipientPhone!)
                            : recipientEmail ?? '',
                        // recipientPhone ?? recipientEmail ?? '',
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        overflow: TextOverflow.ellipsis,
                        caseType: '',
                        color: Colors.black.withOpacity(0.4),
                      ),
                    ],
                  ),
                ),
              ],
            ),
       
          ),
          if (hasSuffix)
            Image.asset(
              MediaRes.navigationArrowRight,
              width: 24,
            ),
        ],
      ),
    );
  }
}
