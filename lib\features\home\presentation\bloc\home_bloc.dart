import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/home/<USER>/models/home_state_model.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/profile/domain/repositories/profile_repositories.dart';
import 'package:cbrs/features/send_money/domain/repositories/bank_transfer_repository.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeBloc({
    required ProfileRepository profileRepository,
    required TransactionRepository transactionRepository,
    required BankTransferRepository bankTransferRepository,
  })  : _profileRepository = profileRepository,
        _transactionRepository = transactionRepository,
        _bankTransferRepository = bankTransferRepository,
        super(HomeInitial()) {
    on<LoadHomeDataEvent>(_onLoadHomeData);
    on<UpdateWalletSelection>(_onUpdateWalletSelection);
    on<ToggleAmountVisibility>(_onToggleAmountVisibility);
    on<RefreshHomeData>(_onRefreshHomeData);
    on<LoadEssentialDataEvent>(_onLoadEssentialData);

    on<HomeProfileFetchingEvent>(_fetchProfile);
  }
  // Flag to prevent multiple rapid wallet selections
  final ProfileRepository _profileRepository;
  final TransactionRepository _transactionRepository;
  final BankTransferRepository _bankTransferRepository;

  Future<void> _onLoadHomeData(
    LoadHomeDataEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      final profileResult = await _profileRepository.getUserData();
      final themeController = Get.find<GetAppThemeController>();
      final savedWalletType = await themeController.getSavedWalletType();

      final homeState = await profileResult.fold<Future<HomeState>>(
        (failure) async => HomeError(failure.message),
        (profile) async {
          final currentState = state;
          final selectedWallet = (currentState is HomeLoaded)
              ? currentState.data.selectedWallet
              : savedWalletType;

          return HomeLoaded(
            HomeStateModel(
              profile: profile,
              selectedWallet: selectedWallet,
              isBalanceLoading: false,
              isAmountHidden: true,
            ),
          );
        },
      );

      emit(homeState);
    } catch (e) {
      emit(HomeError(e.toString()));
    }
  }

  Future<void> _onUpdateWalletSelection(
    UpdateWalletSelection event,
    Emitter<HomeState> emit,
  ) async {
    // emit(UpdateWalletSelectionIsLoading());

    try {
      if (state is HomeLoaded) {
        final currentState = state as HomeLoaded;

        // Only update if the wallet selection has actually changed
        if (currentState.data.selectedWallet != event.wallet) {
          debugPrint(
            'HomeBloc - Updating wallet selection from '
            '${currentState.data.selectedWallet} to ${event.wallet}',
          );

          // Get the theme controller
          final themeController = Get.find<GetAppThemeController>();

          // First update the state to ensure UI reflects the change immediately
          emit(
            HomeLoaded(
              currentState.data.copyWith(
                selectedWallet: event.wallet,
              ),
            ),
          );

          // Use the new forceSetTheme method to directly set the theme without checks
          await themeController.forceSetTheme(event.wallet);

          debugPrint(
            'HomeBloc - Wallet selection and theme update completed for ${event.wallet}',
          );
        } else {
          debugPrint('HomeBloc - Wallet selection unchanged: ${event.wallet}');
        }
      } else {
        debugPrint(
          'HomeBloc - Cannot update wallet selection, state is not HomeLoaded',
        );
      }
    } catch (e) {
      debugPrint('HomeBloc - Error updating wallet selection: $e');
    } finally {}
  }

  Future<void> _onToggleAmountVisibility(
    ToggleAmountVisibility event,
    Emitter<HomeState> emit,
  ) async {
    if (state is HomeLoaded) {
      final currentState = state as HomeLoaded;

      if (currentState.data.isAmountHidden) {
        // When showing the balance, start with loading state
        emit(
          HomeLoaded(
            currentState.data.copyWith(
              isBalanceLoading: true,
            ),
          ),
        );

        final result = await _bankTransferRepository.getWalletDetails();

        result.fold(
          (failure) => emit(
            HomeLoaded(
              currentState.data.copyWith(
                isAmountHidden: true,
                isBalanceLoading: true,
              ),
            ),
          ),
          (walletDetails) => emit(
            HomeLoaded(
              currentState.data.copyWith(
                isAmountHidden: false,
                isBalanceLoading: false,
                etbBalance: walletDetails.etbBalance,
                usdBalance: walletDetails.usdBalance,
              ),
            ),
          ),
        );
      } else {
        emit(
          HomeLoaded(
            currentState.data.copyWith(
              isAmountHidden: true,
              isBalanceLoading: true,
            ),
          ),
        );
      }
    }
  }

  Future<void> _onRefreshHomeData(
    RefreshHomeData event,
    Emitter<HomeState> emit,
  ) async {
    if (state is HomeLoaded) {
      await _onLoadHomeData(LoadHomeDataEvent(), emit);
    }
  }

  Future<void> _onLoadEssentialData(
    LoadEssentialDataEvent event,
    Emitter<HomeState> emit,
  ) async {
    emit(HomeLoading());

    try {
      // Get user profile data
      final profileResult = await _profileRepository.getUserData();
      final themeController = Get.find<GetAppThemeController>();

      // Always use USD as the wallet type on app start

      return profileResult.fold(
        (failure) => emit(HomeError(failure.message)),
        (profile) async {
          // First emit the state with USD wallet type
          final homeState = HomeLoaded(
            HomeStateModel(
              profile: profile,
              selectedWallet: event.walletType,
              isBalanceLoading: false,
              isAmountHidden: true,
            ),
          );

          emit(homeState);

          // Wait a short time to ensure the state is properly updated
          await Future<void>.delayed(const Duration(milliseconds: 100));

          // Then ensure the theme is set to Dollar theme
          debugPrint('HomeBloc - Setting Dollar theme for app start');
          await themeController.forceSetTheme(event.walletType);

          // Verify the theme was applied correctly
          debugPrint(
            'HomeBloc - Theme initialized: isBirrTheme=${themeController.isBirrTheme.value}',
          );

          return;
        },
      );
    } catch (e) {
      debugPrint('HomeBloc - Error loading essential data: $e');
      emit(HomeError(e.toString()));
    }
  }

  Future<void> _fetchProfile(
    HomeProfileFetchingEvent event,
    Emitter<HomeState> emit,
  ) async {
    emit(ProfileLoadingState());
    try {
      final profile = await sl<AuthLocalDataSource>().getCachedUserData();

      if (profile != null) emit (HomeProfileLoadedState(localUser: profile));
      else   emit(HomeError('Faile to fetch profile'));
    } catch (e) {
         emit(HomeError(e.toString()));

    }
  }
}
