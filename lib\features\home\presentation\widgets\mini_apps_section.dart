import 'package:cbrs/features/home/<USER>/widgets/home_section_headers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_webview.dart';
import 'package:cbrs/core/constants/mini_apps_urls.dart';
import 'package:go_router/go_router.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/common/widgets/build_view_all.dart';
import 'package:cbrs/core/common/global_variable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/features/mini_apps/application/bloc/miniapp_bloc.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';

class MiniAppsSection extends StatefulWidget {
  const MiniAppsSection({super.key});

  @override
  State<MiniAppsSection> createState() => _MiniAppsSectionState();
}

class _MiniAppsSectionState extends State<MiniAppsSection> {
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  bool _hasMoreData = true;
  List<MiniappDataEntity> _miniapps = [];

  @override
  void initState() {
    super.initState();
    _loadMiniApps();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadMiniApps() {
    context.read<MiniappBloc>().add(
      GettingMiniappEvent(
        page: _currentPage,
        perPage: 8,
        stage: 'UAT',
      ),
    );
  }

  void _onScroll() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent &&
        _hasMoreData) {
      _currentPage++;
      _loadMiniApps();
    }
  }

  List<MiniappDataEntity> _filterMiniApps(List<MiniappDataEntity> apps) {
    return apps.where((app) => app.merchantType == "MiniApp").toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HomeSectionHeaders(
            title: "Mini Apps",
            description:
                'All essential apps in one place—pay for all with Connect.',
          ),
          SizedBox(height: 16.h),
          Container(
            padding: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 16.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              children: [
                BlocConsumer<MiniappBloc, MiniappState>(
                  listener: (context, state) {
                    if (state is GetLoadedMiniappsState) {
                      final filteredApps = _filterMiniApps(state.miniapp.miniappData);
                      setState(() {
                        if (_currentPage == 1) {
                          _miniapps = filteredApps;
                        } else {
                          _miniapps.addAll(filteredApps);
                        }
                        _hasMoreData = state.miniapp.miniappPaginate?.currentPage != 
                                     state.miniapp.miniappPaginate?.totalPages;
                      });
                    }
                  },
                  builder: (context, state) {
                    if (state is MiniappLoadingState && _currentPage == 1) {
                      return Container(
                        height: 200.h,
                        alignment: Alignment.center,
                        child: const CircularProgressIndicator(),
                      );
                    }

                    if (state is MiniappErrorState && _miniapps.isEmpty) {
                      return Container(
                        height: 200.h,
                        alignment: Alignment.center,
                        child: Text(
                          state.message,
                          style: GoogleFonts.outfit(
                            fontSize: 14.sp,
                            color: Colors.red,
                          ),
                        ),
                      );
                    }

                    return Column(
                      children: [
                        GridView.builder(
                          shrinkWrap: true,
                          controller: _scrollController,
                          padding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 13.h,
                          ),
                          physics: const AlwaysScrollableScrollPhysics(),
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            mainAxisSpacing: 16.h,
                            crossAxisSpacing: 12.w,
                            mainAxisExtent: 80.h,
                          ),
                          itemCount: _miniapps.length + (state is MiniappLoadingState ? 1 : 0),
                          itemBuilder: (context, index) {
                            if (index == _miniapps.length) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }
                            return _buildMiniApp(
                              context,
                              _miniapps[index].miniAppName,
                              _miniapps[index].miniAppIcon,
                              _miniapps[index].url,
                            );
                          },
                        ),
                        if (state is MiniappLoadingState && _currentPage > 1)
                          Padding(
                            padding: EdgeInsets.all(8.h),
                            child: const CircularProgressIndicator(),
                          ),
                      ],
                    );
                  },
                ),
                SizedBox(height: 12.h),
                BuildViewAll(
                  onTap: () {
                    GlobalVariable.currentTabIndex = 1;
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMiniApp(
    BuildContext context,
    String label,
    String imageUrl,
    String url,
  ) {
    return GestureDetector(
      onTap: () {
        if (url.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('$label is coming soon!')),
          );
          return;
        }

        context.pushNamed(
          AppRouteName.miniappWebView,
          extra: {
            'url': url,
            'appName': label,
          },
        );
      },
      child: Container(
        constraints: BoxConstraints(maxHeight: 90.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12.r),
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.error_outline,
                      size: 24.w,
                      color: Colors.red,
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: 6.h),
            Flexible(
              child: Text(
                label,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xFF1A1A1A),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
/*
// Enhanced Mini App Model with URL
class MiniApp {
  const MiniApp({
    required this.label,
    required this.imageUrl,
    this.url = '',
    // this.permissions = const MiniAppPermission(),
  });
  final String label;
  final String imageUrl;
  final String url;
  // final MiniAppPermission permissions;
}

// Updated Mini Apps Data with URLs
const miniApps = [
  MiniApp(
    label: 'Tele Tv',
    imageUrl: MediaRes.teletvIcon,
    url: MiniAppsUrls.teletvUrl,
  ),
  MiniApp(
    label: 'Guzogo',
    imageUrl: MediaRes.guzoIcon,
    url: MiniAppsUrls.guzoGoUrl,
  ),
  MiniApp(
    label: 'Zmall',
    imageUrl: MediaRes.zmallIcon,
    url: MiniAppsUrls.zmallUrl,
  ),
  MiniApp(
    label: 'Ride',
    imageUrl: MediaRes.rideIcon,
    url: MiniAppsUrls.rideUrl,
  ),
  MiniApp(
    label: 'Adika',
    imageUrl: MediaRes.adikaIcon,
    url: MiniAppsUrls.adikaUrl,
  ),
  MiniApp(
    label: 'Muyalogy',
    imageUrl: MediaRes.muyalogyIcon,
    url: MiniAppsUrls.muyalogyUrl,
  ),
  MiniApp(
    label: 'I Chereta',
    imageUrl: MediaRes.icheretaIcon,
    url: MiniAppsUrls.icheretaUrl,
  ),
  MiniApp(
    label: 'WebSprix',
    imageUrl: MediaRes.webSprixIcon,
    url: MiniAppsUrls.webSprixUrl,
  ),
];
*/
