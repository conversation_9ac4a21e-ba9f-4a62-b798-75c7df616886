import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/load_wallet/domain/usecases/check_load_wallet_status_usecase.dart';
import 'package:cbrs/features/load_wallet/domain/usecases/load_to_wallet_usecase.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_event.dart';
import 'package:cbrs/features/load_wallet/application/bloc/load_wallet_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class LoadWalletBloc extends Bloc<LoadWalletEvent, LoadWalletState> {
  LoadWalletBloc({
    required this.loadToWalletUseCase,
    required this.checkLoadWalletStatusUseCase,
  }) : super(const LoadWalletInitial()) {
    on<LoadToWalletRequested>(_onLoadToWalletRequested);
    on<CheckLoadWalletStatus>(_onCheckLoadWalletStatus);
  }
  final LoadToWalletUseCase loadToWalletUseCase;
  final CheckLoadWalletStatusUseCase checkLoadWalletStatusUseCase;

  Future<void> _onLoadToWalletRequested(
    LoadToWalletRequested event,
    Emitter<LoadWalletState> emit,
  ) async {
    emit(const LoadWalletLoading());
    try {
      final result = await loadToWalletUseCase.execute(event.amount);

      result.fold((failure) {
        emit(const LoadWalletFailure('Session expired. Please login again.'));
      }, (response) {
        emit(
          LoadWalletSuccess(
            sessionId: response.data.sessionID,
            billRefNo: response.data.billRefNo,
            redirectURL: response.data.redirectURL,
          ),
        );
      });

      // if (response.data.sessionID.isEmpty) {
      //   emit(const LoadWalletFailure('No payment URL received from server'));
      //   return;
      // }
    } on UnauthorizedException {
      emit(const LoadWalletFailure('Session expired. Please login again.'));
    } catch (e) {
      print('Error in LoadWalletBloc: $e');
      emit(LoadWalletFailure(e.toString()));
    }
  }

  Future<void> _onCheckLoadWalletStatus(
    CheckLoadWalletStatus event,
    Emitter<LoadWalletState> emit,
  ) async {
    try {
      final result = await checkLoadWalletStatusUseCase(event.billRefNo);

      result.fold((failure) {
        emit(LoadWalletFailure(failure.message ?? 'Payment failed'));
      }, (response) {
        emit(LoadWalletCompleted(loadWalletStatusData: response.data!));
      });

      // if (response.success &&
      //     response.statusCode == 200 &&
      //     response.data != null) {
      //   emit(LoadWalletCompleted(loadWalletStatusData: response.data!));

      // final data = response.data!;
      // final cardDetails = data.paymentDetails?.sourceOfFunds.provided;

      // emit(
      //   LoadWalletCompleted(
      //     amount: data.totalAmount.toString(),
      //     transactionType:
      //         data.transactionType.replaceAll('_', ' ').toUpperCase(),
      //     cardNumber: cardDetails?.number ?? '',
      //     cardHolderName: cardDetails?.nameOnCard ?? '',
      //     transactionDate: data.paidDate != null
      //         ? DateFormat('MMMM dd, yyyy').format(data.paidDate!)
      //         : DateFormat('MMMM dd, yyyy').format(DateTime.now()),
      //     transactionReference: data.billRefNo,
      //   ),
      // );
      // } else if (!response.success) {
      //   emit(LoadWalletFailure(response.message ?? 'Payment failed'));
      // }
    } catch (e) {
      print('Error checking status: $e');
      emit(LoadWalletFailure(e.toString()));
    }
  }
}
