import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/my_connect/presentation/views/add_connection_page.dart';
import 'package:cbrs/features/my_connect/presentation/views/scan_and_connect_with_qr_page.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/my_connect_btn_card.dart';
import 'package:flutter/material.dart';
import 'package:googleapis/dfareporting/v3_5.dart';

class EmptyConnectionCard extends StatelessWidget {
  const EmptyConnectionCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            MediaRes.connectionGroup,
            width: 120,
            height: 120,
          ),
          const CustomBuildText(
            text: 'Add Connection',
            fontSize: 18,
            fontWeight: FontWeight.w700,
          ),
          const CustomBuildText(
            text:
                'Find friends by phone or email, add them to your Connect List, and send money with ease.',
            fontSize: 13,
            color: Color(0xFF7C7C7C),
            textAlign: TextAlign.center,
          ),
          const SizedBox(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: MyConnectBtnCard(
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ScanAndConnectWithQrPage(),
                      ),
                    );
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.qr_code_scanner,
                        color: Theme.of(context).primaryColor,
                        size: 24,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      CustomBuildText(
                        text: 'Scan & Connect ',
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).primaryColor,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                width: 12,
              ),
              Expanded(
                child: MyConnectBtnCard(
                  bgColor: Theme.of(context).primaryColor,
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AddConnectionPage(),
                      ),
                    );
                  },
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.scanner,
                        color: Colors.white,
                        size: 24,
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      CustomBuildText(
                        text: 'Connect Now ',
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
