import 'dart:async';
import 'dart:io';
import 'package:cbrs/core/common/widgets/custom_app_bar.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_pin_input.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/form_validation.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/auth/presentation/views/widget/build_warning.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class OTPVerificationScreen extends StatefulWidget {
  const OTPVerificationScreen({
    required this.phoneNumber,
    required this.source,
    super.key,
  });
  final String phoneNumber;
  final String source;

  @override
  State<OTPVerificationScreen> createState() => _OTPVerificationScreenState();
}

class _OTPVerificationScreenState extends State<OTPVerificationScreen> {
  final _pinController = TextEditingController();
  bool _canResend = false;
  late StreamController<int> _timerController;
  StreamSubscription<int>? _timerSubscription;
  bool _isTimerActive = false;
  bool _isLoading = false;

  int maxAttempts = 3;
  int currentAttempt = 0;
  bool _shouldWait = false;
  int _timeLeft = 0;
  late DateTime _lastAttemptTime;
  Timer _timer = Timer.periodic(const Duration(), (value) {});

  @override
  void initState() {
    super.initState();
    _timerController = StreamController<int>.broadcast();
    _lastAttemptTime = DateTime.now();
    _startTimer();
  }

  void _startTimer() {
    if (_isTimerActive) return;
    _isTimerActive = true;
    _timerSubscription?.cancel();
    _timerSubscription = Stream.periodic(
      const Duration(seconds: 1),
      (count) => 60 - count,
    ).take(61).listen((time) {
      _timerController.add(time);
      if (time == 0) {
        setState(() {
          _canResend = true;
          _isTimerActive = false;
        });
      }
    });
  }

  void _resendCode() {
    // if (!_canResend) return;
    debugPrint('resned otp clicked');
    _pinController.clear();
    setState(() {
      _canResend = false;
    });
    _startTimer();
  

    context.read<AuthBloc>().add(
          ResendOtpEvent(phoneNumber: widget.phoneNumber),
        );
  }

  Future<void> _verifyOtp() async {
    if (_shouldWait) {
      CustomToastification(context, message: 'Please wait $_timeLeft seconds.');
      return;
    }

    final otp = _pinController.text;
    final validOTP =
        FormValidation.validateOTP(otp, currentAttempt, maxAttempts);

    if (currentAttempt >= maxAttempts) {
      setState(() {
        _shouldWait = true;
        setState(_pinController.clear);
      });

      // Start a countdown timer for 1 minute
      _startWaitTimer();

      CustomToastification(
        context,
        message: 'Too many attempts. Please try again in 1 minute.',
      );
      return;
    }

    if (currentAttempt < maxAttempts) {
      setState(() {
        currentAttempt = currentAttempt + 1;
      });
    }

    if (validOTP != null) {
      setState(_pinController.clear);
      CustomToastification(context, message: validOTP);
      return;
    }

    debugPrint('Verify OTP 1 currentAttempt $currentAttempt');

    setState(() => _isLoading = true);

    if (!mounted) return;

    setState(() => _isLoading = false);

    // Navigate based on source
    if (widget.source == 'forgot_password') {
      context.read<AuthBloc>().add(
            VerifyOtpEvent(
              phoneNumber: widget.phoneNumber,
              otp: otp,
              source: widget.source,
            ),
          );
    } else if (widget.source == 'device_signup') {
      context.read<AuthBloc>().add(
            VerifyOtpEvent(
              phoneNumber: widget.phoneNumber,
              otp: otp,
              source: widget.source,
            ),
          );
    } else if (widget.source == 'device_login') {
      context.read<AuthBloc>().add(
            VerifyOtpEvent(
              phoneNumber: widget.phoneNumber,
              otp: otp,
              source: widget.source,
            ),
          );
    }
  }

  void _startWaitTimer() {
    setState(() {
      _timeLeft = 60;
      _shouldWait = true;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeLeft > 0) {
        setState(() {
          _timeLeft--;
        });
      } else {
        _timer.cancel();
        setState(() {
          _shouldWait = false;
          currentAttempt = 0;
        });
      }
    });
  }

  @override
  void dispose() {
    _pinController.dispose();
    _timerSubscription?.cancel();
    _timerController.close();
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        debugPrint('stat ${state.runtimeType}');
        if (state is AuthError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              behavior: SnackBarBehavior.floating,
            ),
          );
          if (state.message.toLowerCase().contains('invalid') ||
              state.message.toLowerCase().contains('incorrect')) {
            _pinController.clear();
          }
        } else if (state is OtpVerifiedState) {
          if (!state.response.success)
            CustomToastification(
              context,
              message: state.response.message,
              isError: !state.response.success,
            );
          if (state.response.success) {
            debugPrint(
              'Email verified successfully, navigating to create PIN screen',
            );
            context.goNamed(
              'createPin',
              extra: {
                'source': widget.source,
              },
            );
          }
        } else if (state is OtpSentState) {
          CustomToastification(
            context,
            message: 'New verification code sent to your phone',
            isError: false,
          );
        } else {}
      },
      builder: (context, state) {
        return Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: CustomAppBar(context: context, title: 'Phone Verification'),
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus(); // Hide the keyboard
            },
            child: Stack(
              fit: StackFit.expand,
              children: [
                SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: SizedBox(
                    // color: Colors.green,
                    height: MediaQuery.sizeOf(context).height -
                        kToolbarHeight -
                        (Platform.isIOS ? 64 : 30),
                    child: Column(
                      children: [
                        SizedBox(height: 16.h),
                        const CustomPageHeader(
                          pageTitle: 'Phone Verification',
                          description:
                              "We've sent a verification code to your phone. Please enter the code to confirm your phone number.",
                        ),
                        SizedBox(height: 20.h),
                        _buildHeader(theme),
                        SizedBox(height: 10.h),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: IgnorePointer(
                            ignoring: _isLoading,
                            child: CustomPinInput(
                              controller: _pinController,
                              onChange: (value) {
                                setState(() {});
                              },
                            ),
                          ),
                        ),
                        SizedBox(height: 34.h),
                        _buildResendButton(theme),
                        // SizedBox(height: 350),
                        const Spacer(),
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 55.h),
                          child: CustomRoundedBtn(
                            btnText: 'Verify',
                            isLoading: state is AuthLoading,
                            onTap: _verifyOtp,
                            isBtnActive: _pinController.length == 6,
                          ),
                        ),
                       
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_shouldWait) ...[
          const BuildWarning(hasMargin: false),
          SizedBox(height: 16.h),
          Center(
            child: Text(
              '00:$_timeLeft Sec',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
          ),
        ] else if (!_canResend) ...[
          Center(
            child: StreamBuilder<int>(
              stream: _timerController.stream,
              builder: (context, snapshot) {
                return RichText(
                  text: TextSpan(
                    text: 'You can resend OTP after ',
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      color: Colors.grey.shade600,
                    ),
                    children: [
                      TextSpan(
                        text: '00:${snapshot.data ?? 60}',
                        style: GoogleFonts.outfit(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: theme.primaryColor,
                        ),
                      ),
                      TextSpan(
                        text: ' Sec',
                        style: GoogleFonts.outfit(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: theme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPinInput(ThemeData theme) {
    final screenSize = MediaQuery.sizeOf(context);
    return Pinput(
      length: 6,
      controller: _pinController,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      defaultPinTheme: PinTheme(
        width: 60.w,
        height: 56.h,
        textStyle: GoogleFonts.outfit(
          fontSize: 20.sp,
          fontWeight: FontWeight.w600,
          color: theme.primaryColor,
        ),
        decoration: BoxDecoration(
          color: theme.colorScheme.onTertiary,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Colors.grey.shade200),
        ),
      ),
      focusedPinTheme: PinTheme(
        // width: screenSize.width * 0.12,
        // height: screenSize.width * 0.12,
        width: 60.w,
        height: 56.h,
        textStyle: GoogleFonts.outfit(
          fontSize: 20.sp,
          fontWeight: FontWeight.w600,
          color: theme.primaryColor,
        ),
        decoration: BoxDecoration(
          color: theme.colorScheme.onTertiary,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.primaryColor),
          boxShadow: [
            BoxShadow(
              color: theme.primaryColor.withOpacity(0.1),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildResendButton(ThemeData theme) {
    return Column(
      children: [
        if (_canResend)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Didn't receive code?",
                style: GoogleFonts.outfit(
                  color: _shouldWait
                      ? const Color(0xFFAEAEB2)
                      : const Color(0xFF7A7A7A),
                  fontSize: 16.sp,
                ),
              ),
              SizedBox(width: 8.w),
              GestureDetector(
                onTap: _canResend ? _resendCode : null,
                child: Text(
                  'Resend OTP',
                  style: GoogleFonts.outfit(
                    color: _shouldWait
                        ? const Color(0xFFAEAEB2)
                        : theme.primaryColor,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
      ],
    ).animate().fadeIn(duration: 800.ms);
  }
}
