{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9857a4622b4af8cef08d73f7d388143b83", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987253cd6c9ce597ccb8a59ce60566aeb9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3f17ef595c5d218c53243fe28226403", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a55bac032690a1b1fa692639ce02cd13", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3f17ef595c5d218c53243fe28226403", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850f6e6faf835809a172d09a6509255a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9891433109a0fa15e829d21221ccfb57c3", "guid": "bfdfe7dc352907fc980b868725387e9862a8e0612d5e619563196be41526ddc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fa016034c07effd06c35f0abf0f587e", "guid": "bfdfe7dc352907fc980b868725387e98b67f2ba37631d2bfd78ae4055eafff2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1042eea0a2554ce21c21136b08831c4", "guid": "bfdfe7dc352907fc980b868725387e98e8925705712f4a567bc3081bc9741027", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d3cea730b1a44c50ef2887b301ea18a", "guid": "bfdfe7dc352907fc980b868725387e98e164e0c94bc2efd3425a5e692915dc6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c459ee1c5f619f1ec8541ab9904ebfb5", "guid": "bfdfe7dc352907fc980b868725387e98d6e7e07e484057a66ab8c5724af38ff1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b32b06e7416664d3cd9fcb60815b14e9", "guid": "bfdfe7dc352907fc980b868725387e9803130a3062c7f96df4bb3e57cafa3eee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a49bbe7bcaa9d496ff04be69e221e939", "guid": "bfdfe7dc352907fc980b868725387e98a3795b6c6e1d88893519bf59a3b26f76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98024026df8fe58542996572e14975447c", "guid": "bfdfe7dc352907fc980b868725387e98d2e81311c06a4e0a1c492a6130d2c985", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5508d897c5d783aa581a612b71eb33c", "guid": "bfdfe7dc352907fc980b868725387e985be07264af1cbe7b5e1d87f9680689fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba0b319ff3ed61f64e85eea0eb8eb0c0", "guid": "bfdfe7dc352907fc980b868725387e987614ec39498d4d551bcc97d05a7e4270", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db7b26b5914949c96d429791d564074f", "guid": "bfdfe7dc352907fc980b868725387e9806029c859fcf17db96c1079af015a68b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac6f156690e9ca32b91536f9d1afc939", "guid": "bfdfe7dc352907fc980b868725387e98b3d329e0c56be62e8cbe188e04cedce2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe4cc5b44ec829a98b0c548f6f1e6c06", "guid": "bfdfe7dc352907fc980b868725387e988a29cc41f42b3a5b2a724480865e6812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837fd33ebe6f71cb78e404f8d5414c987", "guid": "bfdfe7dc352907fc980b868725387e98ecd62fceb2ab53ce4f92e5b1e2c71235", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f054481ebda0e73aff36f65e4c4c751", "guid": "bfdfe7dc352907fc980b868725387e984d92911d796044a4af6134fbc0d505ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f646c0c08859197091df3d06f9665c3d", "guid": "bfdfe7dc352907fc980b868725387e983c59fd544fe70a58343f8a9ef6647c5e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988f781bad5cca121b273fdcc0df221dc9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98633048d6cc048bdfec479b9db440e35b", "guid": "bfdfe7dc352907fc980b868725387e98b3b67d1c082b37c43a28c963f775fd11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6297f9f482e7df21b4619c589043288", "guid": "bfdfe7dc352907fc980b868725387e98e0bfce400c8096f7207755b155e892c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865cb80cee65651778d869ba54d55f2c7", "guid": "bfdfe7dc352907fc980b868725387e98cf0c1c882d55bf980b203b31f0a9f6ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a35efb6466517d259a7579207b1bb8", "guid": "bfdfe7dc352907fc980b868725387e984aae220247f1d7fea8454c98f852f9e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828dcaf0fa6624bc0b5d77c787347556e", "guid": "bfdfe7dc352907fc980b868725387e98b4ecfde4a64cd3eade7df66e62178b91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98661d963d06484b8909df638e265f7a47", "guid": "bfdfe7dc352907fc980b868725387e9899e0bd8ab856ebcbcebefafa41f1edfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a10b63421d9c47ce7366371dd5deb9e4", "guid": "bfdfe7dc352907fc980b868725387e98ec58db4783e154ca701c16f6db701d91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e17b9f5c2bc7645c75b396a8a990e6ff", "guid": "bfdfe7dc352907fc980b868725387e98e66f0468e7067c32e819075b85d8ecdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef842b965826f072c4871a3561875e8d", "guid": "bfdfe7dc352907fc980b868725387e98762f11198fdfa845a52a85ba13574fe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98328d1c441e060dc13793032a990d4aab", "guid": "bfdfe7dc352907fc980b868725387e9855e1b2e698e9672f6ad854872608931c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896061cb2166ff0f0d91f211b0821b939", "guid": "bfdfe7dc352907fc980b868725387e984ab15adf21bfda6f3cf035289e3362be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98599c0cc2575180890edf67c3a39c7f9a", "guid": "bfdfe7dc352907fc980b868725387e98a0d0c67085d0769d6c52384a516cb6e5"}], "guid": "bfdfe7dc352907fc980b868725387e988b032901099fd277a3a3a54ac8115aee", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e98c37589c5341fd481fe01935709381f0f"}], "guid": "bfdfe7dc352907fc980b868725387e9891398e43858555e15da7ae8d2bd752fc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ddd1b777e9a66de6b660c8b1f07cd206", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e986fe241938c6846738da23f93e6dd9925", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}