import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/features/my_connect/presentation/views/connect_requested_page.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/my_connect_btn_card.dart';
import 'package:flutter/material.dart';

class ConnectRequestCard extends StatelessWidget {
  const ConnectRequestCard({required this.requestCount, super.key});

  final int requestCount;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            color: Color(0xFFF6F6F6),
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x0F000000),
            blurRadius: 24,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CustomBuildText(
                  text: 'Connect Requests',
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
                const SizedBox(
                  height: 4,
                ),
                CustomBuildText(
                  text: 'Your Sent  & Received Connection requests.',
                  color: Colors.black.withValues(alpha: 128),
                  fontSize: 11,
                ),
              ],
            ),
          ),
          const SizedBox(
            width: 30,
          ),
          MyConnectBtnCard(
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const ConnectRequestedPage(),
                ),
              );
            },
            child: Row(
              children: [
                Icon(
                  Icons.person,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                CustomBuildText(
                  text: 'Connect ',
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).primaryColor,
                ),
                Badge.count(
                  count: requestCount,
                  backgroundColor: Theme.of(context).primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
