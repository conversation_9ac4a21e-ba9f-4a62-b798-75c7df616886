import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_phone_field.dart';
import 'package:cbrs/core/common/widgets/custom_tab_bar_button.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen>
    with TickerProviderStateMixin {
  final bool _obscurePassword = true;
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  String _selectedCountryCode = '+251';
  final _phoneInputKey = GlobalKey<CustomPhoneInputState>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    debugPrint(' index tab${_tabController.index}');
    if (_tabController.indexIsChanging) {
      debugPrint(' inside if class tab${_tabController.index}');

      // _phoneInputKey.currentState?.closeBottomSheet();
      // FocusScope.of(context).unfocus();
      _phoneController.clear();
      _emailController.clear();

      // // if (_tabController.index == 0) {
      // //   _phoneController.clear();
      // // } else {
      // //   _emailController.clear();
      // // }
      // _passwordController.clear();
      setState(() {});
    }
  }

  // _phoneController.clear();
  // _emailController.clear();
  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar:
          //  BuildCustomAppBar(context: context, title: ''),
          AppBar(
        backgroundColor: theme.scaffoldBackgroundColor,
        leading: IconButton(
          onPressed: () => context.go(AppRouteName.guestHomePage),
          icon: const Icon(Icons.arrow_back),
        ),
        elevation: 0,
        scrolledUnderElevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.transparent,
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus(); // Hide the keyboard
        },
        child: BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(' ${state.message}')),
              );
            } else if (state is EmailVerificationSentState) {
              CustomToastification(
                context,
                message: state.response.message,
                isError: !state.response.success,
              );
              if (state.response.success) {
                context.go(
                  AppRouteName.verifyEmail,
                  extra: {
                    'email': _emailController.text,
                    'source': 'device_login',
                  },
                );
              }
            } else if (state is OtpSentState) {
              CustomToastification(
                context,
                message: state.response.message,
                isError: !state.response.success,
              );
              if (state.response.success) {
                final formattedNumber =
                    '$_selectedCountryCode${_phoneController.text}';

                context.goNamed(
                  'verifyOtp',
                  extra: {
                    'phoneNumber': formattedNumber,
                    'source': 'device_login',
                  },
                );
              }
            }
          },
          builder: (context, state) {
            return Center(
              child: Stack(
                fit: StackFit.expand,
                children: [
                  SingleChildScrollView(
                    child: Container(
                      // color: Colors.green,
                      padding: EdgeInsets.only(
                        top: 29.h,

                              left: 16.0.w,
                              right: 16.0.w,
                              // bottom: 16.0.w,
                            ),
                            margin: EdgeInsets.only(bottom: 16),
                            height: MediaQuery.sizeOf(context).height -
                                kToolbarHeight -
                                (Platform.isIOS ? 50 : 30.h),
                            child: Form(
                              key: _formKey,
                              child: Column(
                                // mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Center(
                                    child: Image.asset(
                                        MediaRes.connectBirrMainLogo,
                                        height: 130.h),
                                  ),
                                  SizedBox(height: 12.h),
                                  Text(
                                    'Login',
                                    style: GoogleFonts.outfit(
                                      fontSize: 24.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(height: 8.h),
                                  Text(
                                    'Welcome Back! Log in to your CBRS account to make transfers effortlessly.',
                                    style: GoogleFonts.outfit(
                                      fontSize: 16.sp,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  SizedBox(height: 24.sp),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.onTertiary,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: CustomButtonTabBar(
                                      controller: _tabController,
                                      labelColor: Colors.white,
                                      unselectedLabelColor: theme.primaryColor,
                                      backgroundColor: theme.primaryColor,
                                      unselectedBackgroundColor:
                                          theme.colorScheme.onTertiary,
                                      borderRadius: 12.r,
                                      labelStyle: GoogleFonts.outfit(
                                        fontSize: 15.sp,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      unselectedLabelStyle: GoogleFonts.outfit(
                                        fontSize: 15.sp,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      tabs: const [
                                        Tab(text: 'Phone Number'),
                                        Tab(text: 'Email'),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 16.h),
                                  IgnorePointer(
                                    ignoring: context.watch<AuthBloc>().state
                                        is AuthLoading,
                                    child: AnimatedSwitcher(
                                      duration:
                                          const Duration(milliseconds: 300),
                                      child: _tabController.index == 0
                                          ? _buildPhoneForm(theme)
                                          : _buildEmailForm(theme),
                                      transitionBuilder: (Widget child,
                                          Animation<double> animation) {
                                        return FadeTransition(
                                          opacity: animation,
                                          child: child,
                                        );
                                      },
                                    ),
                                  ),
                                  SizedBox(height: 250.h),
                                  Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      CustomButton(
                                        text: 'Continue',
                                        showLoadingIndicator: context
                                            .watch<AuthBloc>()
                                            .state is AuthLoading,
                                        onPressed: () {
                                          if (_formKey.currentState
                                                  ?.validate() ??
                                              false) {
                                            if (_tabController.index == 1) {
                                              debugPrint("email tab click");

                                        context.read<AuthBloc>().add(
                                              SignInWithEmailEvent(
                                                email: _emailController.text,
                                              ),
                                            );
                                      } else {
                                        final formattedNumber =
                                            '$_selectedCountryCode${_phoneController.text}';
                                        debugPrint(
                                          'phone tab click $formattedNumber',
                                        );

                                        context.read<AuthBloc>().add(
                                              SignInWithPhoneEvent(
                                                phoneNumber: formattedNumber,
                                              ),
                                            );
                                      }
                                    }
                                  },
                                  options: CustomButtonOptions(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 16.h,
                                    ),
                                    color: theme.primaryColor,
                                    textStyle: GoogleFonts.plusJakartaSans(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                    borderRadius: BorderRadius.circular(32),
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                SafeArea(
                                  child: TextButton(
                                    onPressed: () =>
                                        context.go(AppRouteName.signUp),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          "Don't have an account?",
                                          style: GoogleFonts.outfit(
                                            color: Colors.grey.shade800,
                                            fontSize: 16.sp,
                                          ),
                                        ),
                                        SizedBox(width: 4.w),
                                        Text(
                                          'Sign Up',
                                          style: GoogleFonts.outfit(
                                            color: theme.primaryColor,
                                            fontWeight: FontWeight.w800,
                                            fontSize: 16.sp,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmailForm(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8.h),
        _buildInputField(
          controller: _emailController,
          hintText: 'Email Address',
          validator: (value) {
            // email validation
            if (value != null && value.isNotEmpty) {
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                  .hasMatch(value.trim())) {
                return 'Please enter a valid email address!';
              }
              return null;
            }

            return null;
          },
          keyboardType: TextInputType.emailAddress,
          onSaved: (value) {},
          theme: theme,
          isRequired: true,
          prefixIcon: Icon(Icons.email_outlined, color: theme.primaryColor),
        ),
      ],
    );
  }

  Widget _buildPhoneForm(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormField<String>(
          validator: (value) {
            debugPrint('phone number');
            if (_phoneController.text.isEmpty) {
              return 'Phone number is required';
            }
            return null;
          },
          builder: (FormFieldState<String> field) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomPhoneInput(
                  key: _phoneInputKey,
                  // releasePlatform: ReleasePlatform.other,
                  initialCountryCode: _selectedCountryCode,
                  phoneController: _phoneController,
                  onCountrySelected: (dialCode, isoCode) {
                    setState(() {
                      _selectedCountryCode = dialCode;
                    });
                  },
                  validator: (value) => null,
                  theme: theme,
                  isRequired: true,
                ),
                if (field.hasError)
                  Padding(
                    padding: EdgeInsets.only(top: 8.h, left: 16),
                    child: Text(
                      field.errorText!,
                      style: GoogleFonts.outfit(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildInputField({
    required String hintText,
    required FormFieldValidator<String> validator,
    required ThemeData theme,
    TextEditingController? controller,
    bool isRequired = false,
    bool isPassword = false,
    bool isEmailField = false,
    Widget? suffixIcon,
    Widget? prefixIcon,
    TextInputType? keyboardType,
    void Function(String)? onSaved,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: hintText,
                style: GoogleFonts.plusJakartaSans(
                  color: Colors.grey.shade600,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: GoogleFonts.plusJakartaSans(
                    color: Colors.red,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
        SizedBox(height: 4.h),
        CustomTextFormField(
          controller: controller,
          validator: validator,
          onSaved: onSaved,
          isPassword: isPassword && _obscurePassword,
          textInputType: keyboardType,
          style: GoogleFonts.plusJakartaSans(
            color: theme.colorScheme.onSurface,
          ),
          fillColor: theme.colorScheme.onTertiary,
          borderRadius: 8.r,
          maxLines: 1,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: GoogleFonts.outfit(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: theme.colorScheme.onTertiary,
            contentPadding: EdgeInsets.symmetric(
              vertical: 16.h,
              horizontal: 20.w,
            ),
          ),
        ),
      ],
    );
  }
}
