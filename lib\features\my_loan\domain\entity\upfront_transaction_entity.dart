// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

class UpfrontTransactionEntity extends Equatable {
  final String elstRef;
  final String billRefNo;
  final String beneficiaryId;
  final String beneficiaryName;
  final String senderName;
  final String senderPhone;
  final String bankName;
  final String bankCode;
  final String senderId;
  final String transactionOwner;
  final String authorizationType;
  final String status;
  final double vat;
  final double serviceCharge;
  final String originalCurrency;
  final double billAmount;
  final double totalAmount;
  final double facilitationFee;

  
  final String createdAt;
  final String lastModifiedAt;
  final String transactionType;
  final String percentage;
  final String loanType;
  final String paidDate;
  final String paymentMethod;
  final double paidAmount;
  final UpfrontPaymentDetailsEntity paymentDetails;
  final String paymentReference;

  const UpfrontTransactionEntity({
    required this.elstRef,
    required this.billRefNo,
    required this.beneficiaryId,
    required this.beneficiaryName,
    required this.senderName,
    required this.senderPhone,
    required this.bankName,
    required this.bankCode,
    required this.senderId,
    required this.transactionOwner,
    required this.authorizationType,
    required this.status,
    required this.vat,
    required this.serviceCharge,
    required this.originalCurrency,
    required this.billAmount,
    required this.totalAmount,
    required this.facilitationFee,
    required this.createdAt,
    required this.lastModifiedAt,
    required this.transactionType,
    required this.percentage,
    required this.loanType,
    required this.paidDate,
    required this.paymentMethod,
    required this.paidAmount,
    required this.paymentDetails,
    required this.paymentReference,
  });

  @override
  // TODO: implement props
  List<Object?> get props => [];
}

class UpfrontPaymentDetailsEntity extends Equatable {
  final String walletId;
  final String currency;
  const UpfrontPaymentDetailsEntity({
    required this.walletId,
    required this.currency,
  });

  @override
  // TODO: implement props
  List<Object> get props => [walletId, currency];
}
