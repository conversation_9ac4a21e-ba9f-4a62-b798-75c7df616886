import 'dart:ui';

import 'package:cbrs/core/common/presentation/widgets/guest_bottom_nav_bar.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/guest/presentation/views/home/<USER>';
import 'package:cbrs/features/guest/presentation/views/guest_profile_screen.dart';
import 'package:cbrs/features/guest/presentation/views/home/<USER>';
import 'package:cbrs/features/guest/presentation/views/home/<USER>';
import 'package:cbrs/features/guest/presentation/widget/show_guest_mode_bottom_sheet.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class GuestRateScreen extends StatefulWidget {
  const GuestRateScreen({super.key});

  @override
  State<GuestRateScreen> createState() => _GuestRateScreenState();
}

class _GuestRateScreenState extends State<GuestRateScreen>
    with SingleTickerProviderStateMixin {
  int _currentIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<Widget> _pages = [
    BlocProvider(
      create: (context) => GetIt.I<HomeBloc>(),
      child: const GuestHomeScreen(),
    ),
    const TransactionsPage(),
    const TransactionsPage(),
    const GuestProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    if (index == 1 || index == 2 || index == 3) {
      showGuestModeBottomSheet(context);
      return;
    }

    if (index == _currentIndex) return;
    setState(() {
      _currentIndex = index;
      _animationController.reset();
      _animationController.forward();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: IndexedStack(
          index: _currentIndex,
          children: _pages,
        ),
      ),
      bottomNavigationBar: GuestBottomNavBar(
        currentIndex: _currentIndex,
        onTap: _onItemTapped,
      ),
    );
  }
}
