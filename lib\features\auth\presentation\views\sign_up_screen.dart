import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_country_picker.dart';
import 'package:cbrs/core/common/widgets/custom_date_input.dart';
import 'package:cbrs/core/common/widgets/custom_phone_field.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/form_validation.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _middleNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _cityController = TextEditingController();
  final _dateOfBirthController = TextEditingController();
  String _selectedGender = '';
  String _selectedCountryCode = '+251';
  String _selectedCountry = '';

  bool _isChecked = false;

  final FocusNode focusNode = FocusNode();

  final _phoneInputKey = GlobalKey<CustomPhoneInputState>();
  final _countryPickerKey = GlobalKey<CustomCountryPickerState>();

  String selectedTab = 'Phone';
  final List<String> tabList = ['Phone', 'Email'];

  void onTap(String tabName) {
    if (tabName != selectedTab) {
      setState(() {
        selectedTab = tabName;
        // Clear fields based on tab selection
        if (selectedTab == 'Email') {
          _phoneController.clear();
        } else {
          _emailController.clear();
        }
      });
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _middleNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _cityController.dispose();
    _dateOfBirthController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
        appBar: AppBar(
          backgroundColor: theme.scaffoldBackgroundColor,
          leading: IconButton(
            onPressed: () => context.go(AppRouteName.guestHomePage),
            icon: const Icon(Icons.arrow_back),
          ),
          elevation: 0,
          scrolledUnderElevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.dark.copyWith(
            statusBarColor: Colors.transparent,
          ),
        ),
        body: SafeArea(
          child: GestureDetector(
            onTap: () {
              // Hide keyboard when tapping outside
              // _formKey.currentState?.validate();
              FocusScope.of(context).unfocus();
            },
            // Ensure gesture detector doesn't interfere with scrolling/other interactions
            behavior: HitTestBehavior.translucent,
            child: BlocConsumer<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is AuthError) {
                  CustomToastification(context, message: state.message);
                } else if (state is SignedUpState) {
                  if (!state.success) {
                    CustomToastification(context, message: state.message);

                    return;
                  }

                  // Handle successful signup
                  // if (state.email != null) {
                  if (_emailController.text.isNotEmpty && state.email != null) {
                    context.goNamed(
                      'verifyEmail',
                      extra: {
                        'email': state.email,
                        'source': 'device_signup',
                        'otp': state.otp.toString(),
                      },
                    );
                  } else {
                    final formattedNumber =
                        '$_selectedCountryCode${_phoneController.text}';
                    context.goNamed(
                      'verifyOtp',
                      extra: {
                        'phoneNumber': formattedNumber,
                        'source': 'device_signup',
                      },
                    );
                  }
                }
              },
              builder: (context, state) {
                return Stack(
                  children: [
                    SingleChildScrollView(
                      padding: EdgeInsets.only(
                        left: 16.0.w,
                        right: 16.0.w,
                        bottom: 16.0.w,
                      ),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Center(
                              child: Image.asset(
                                MediaRes.connectBirrMainLogo,
                                height: 140.h,
                                width: 130.w,
                                fit: BoxFit.contain,
                              ),
                            ),
                            SizedBox(height: 20.h),
                            Text(
                              'Sign Up',
                              style: GoogleFonts.outfit(
                                fontSize: 24.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Create an account on CONNECT and make global money transfers simple and seamless.',
                              style: GoogleFonts.outfit(
                                fontSize: 16.sp,
                                color: Colors.grey,
                              ),
                            ),
                            SizedBox(height: 14.h),

                            // Add tab bar here
                            CustomRoundedTabs(
                              onTap: onTap,
                              selectedTab: selectedTab,
                              tabList: tabList,
                            ),
                            SizedBox(height: 16.h),

                            // Continue with name fields
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: _buildInputField(
                                    controller: _firstNameController,
                                    hintText: 'First Name',
                                    validator: (value) =>
                                        FormValidation.validateName(
                                      value,
                                      'First Name',
                                    ),
                                    theme: theme,
                                    isRequired: true,
                                    prefixIcon: Icon(
                                      Icons.person_outline,
                                      color: theme.primaryColor,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 14.w),
                                Expanded(
                                  child: _buildInputField(
                                    controller: _lastNameController,
                                    hintText: 'Last Name',
                                    validator: (value) =>
                                        FormValidation.validateName(
                                      value,
                                      'Last Name',
                                    ),
                                    theme: theme,
                                    isRequired: true,
                                    prefixIcon: Icon(
                                      Icons.person_outline,
                                      color: theme.primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 16.h),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: _buildGenderField(theme),
                                ),
                              ],
                            ),
                            SizedBox(height: 16.h),

                            // Show email or phone based on selected tab
                            if (selectedTab == 'Email')
                              _buildInputField(
                                isEmailField: true,
                                controller: _emailController,
                                hintText: 'Email',
                                validator:
                                    FormValidation.validateEmailWithoutPhone,
                                theme: theme,
                                isRequired: true,
                                prefixIcon: Icon(
                                  Icons.email_outlined,
                                  color: theme.primaryColor,
                                ),
                              )
                            else
                              FormField<String>(
                                validator: (value) {
                                  if (_phoneController.text.isEmpty) {
                                    return 'Phone number is required';
                                  }
                                  return null;
                                },
                                builder: (FormFieldState<String> field) {
                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text.rich(
                                        TextSpan(
                                          children: [
                                            TextSpan(
                                              text: 'Phone Number',
                                              style:
                                                  GoogleFonts.plusJakartaSans(
                                                color: Colors.grey.shade600,
                                                fontSize: 14.sp,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            TextSpan(
                                              text: ' *',
                                              style:
                                                  GoogleFonts.plusJakartaSans(
                                                color: Colors.red,
                                                fontSize: 14.sp,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      CustomPhoneInput(
                                        key: _phoneInputKey,
                                        initialCountryCode:
                                            _selectedCountryCode,
                                        phoneController: _phoneController,
                                        onCountrySelected: (dialCode, isoCode) {
                                          setState(() {
                                            _selectedCountryCode = dialCode;
                                          });
                                        },
                                        onChange: (value) {
                                          field.didChange(value);
                                          _formKey.currentState?.validate();
                                        },
                                        validator: (_) => null,
                                        theme: theme,
                                      ),
                                      if (field.hasError)
                                        Padding(
                                          padding: EdgeInsets.only(
                                              top: 8.h, left: 16),
                                          child: Text(
                                            field.errorText!,
                                            style: GoogleFonts.outfit(
                                              fontSize: 12,
                                              color: Colors.red,
                                            ),
                                          ),
                                        ),
                                    ],
                                  );
                                },
                              ),

                            // Continue with remaining fields
                            SizedBox(height: 16.h),
                            _buildCountryField(theme),
                            SizedBox(height: 16.h),
                            _buildInputField(
                              controller: _cityController,
                              hintText: 'City',
                              validator: FormValidation.validateCity,
                              //  (value) {
                              //   if (value == null || value.isEmpty) {
                              //     return 'City is required';
                              //   }
                              //   return null;
                              // },

                              theme: theme,
                              isRequired: true,
                              prefixIcon: Icon(
                                Icons.location_city,
                                color: theme.primaryColor,
                              ),
                              textCapitalization: TextCapitalization.words,
                              inputFormatters: [
                                // Capitalize first letter of each word
                                TextInputFormatter.withFunction(
                                    (oldValue, newValue) {
                                  if (newValue.text.isEmpty) return newValue;

                                  // Split text into words
                                  final words = newValue.text.split(' ');

                                  // Capitalize first letter of each word
                                  final capitalizedWords = words.map((word) {
                                    if (word.isEmpty) return '';
                                    return word[0].toUpperCase() +
                                        (word.length > 1
                                            ? word.substring(1)
                                            : '');
                                  }).join(' ');

                                  return TextEditingValue(
                                    text: capitalizedWords,
                                    selection: newValue.selection,
                                  );
                                }),
                              ],
                            ),
                            SizedBox(height: 14.h),
                            CustomDateInput(
                              controller: _dateOfBirthController,
                              theme: theme,
                              isRequired: true,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Date of birth is required';
                                }
                                return null;
                              },
                            ),
                            // SizedBox(height: 16.h),
                            _buildTerms(theme),
                            SizedBox(height: 48.h),

                            CustomButton(
                              text: 'Sign Up',
                              showLoadingIndicator: state is AuthLoading,
                              onPressed: () {
                                if (_formKey.currentState?.validate() ??
                                    false) {
                                  final formattedNumber = _phoneController
                                          .text.isNotEmpty
                                      ? '$_selectedCountryCode${_phoneController.text}'
                                      : '';

                                  if (!_isChecked) return;
                                  context.read<AuthBloc>().add(
                                        SignUpEvent(
                                          fullName:
                                              '${_firstNameController.text} ${_middleNameController.text} ${_lastNameController.text}'
                                                  .trim(),
                                          email: selectedTab == 'Email'
                                              ? _emailController.text
                                              : '',
                                          phoneNumber: selectedTab == 'Phone'
                                              ? formattedNumber
                                              : '',
                                          city: _cityController.text,
                                          country: _selectedCountry,
                                          dateOfBirth:
                                              _dateOfBirthController.text,
                                          gender: _selectedGender,
                                        ),
                                      );
                                }
                              },
                              options: CustomButtonOptions(
                                padding: EdgeInsets.symmetric(vertical: 16.h),
                                color: _isChecked
                                    ? theme.primaryColor
                                    : const Color(0xFFAEAEB2),
                                loadingColor: Colors.white,
                                loadingStrokeWidth: 2.5,
                                textStyle: GoogleFonts.plusJakartaSans(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                                borderRadius: BorderRadius.circular(32),
                              ),
                            ),
                            const SizedBox(height: 1),
/*
                        ElevatedButton(
                          onPressed: () {
                            showCustomBottomSheet(

                              context,
                              maxHeight:600,
                              Column(
                                mainAxisSize: MainAxisSize
                                    .min, // Important for content-based height
                                children: [
                                  Expanded(
                                    child: Row(
                                      children: [
                                        Expanded(child: const Text('This is a simple bottom sheet.')),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Row(
                                      children: [
                                        Expanded(child: const Text('This is a simple bottom sheet.')),
                                      ],
                                    ),
                                  ),
                                  ElevatedButton(
                                    onPressed: () => Navigator.pop(
                                        context), // Close the sheet
                                    child: const Text('Close'),
                                  ),
                                ],
                              ),
                            );
                          },
                          child: const Text('Show Bottom Sheet'),
                        ),


                    */

                            Center(
                              child: TextButton(
                                onPressed: () =>
                                    context.go(AppRouteName.signIn),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'Already have an account?',
                                      style: GoogleFonts.outfit(
                                        color: Colors.grey.shade800,
                                        fontSize: 16.sp,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Login',
                                      style: GoogleFonts.outfit(
                                        color: theme.primaryColor,
                                        fontWeight: FontWeight.w800,
                                        fontSize: 16.sp,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ));
  }

  Widget _buildInputField({
    required String hintText,
    required FormFieldValidator<String> validator,
    required ThemeData theme,
    TextEditingController? controller,
    bool isRequired = false,
    bool isNameField = false,
    bool isEmailField = false,
    Widget? prefixIcon,
    TextCapitalization textCapitalization = TextCapitalization.none,
    List<TextInputFormatter>? inputFormatters,
    final bool hasOptionalValidator = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: hintText,
                style: GoogleFonts.outfit(
                  color: Colors.grey.shade600,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: GoogleFonts.outfit(
                    color: Colors.red,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
            ],
          ),
        ),
        SizedBox(height: 4.h),
        CustomTextFormField(
          controller: controller,
          validator: validator,
          overrideValidator: true,
          textInputType:
              isEmailField ? TextInputType.emailAddress : TextInputType.text,
          textCapitalization: textCapitalization,
          style: GoogleFonts.plusJakartaSans(
            color: theme.colorScheme.onSurface,
          ),
          fillColor: theme.colorScheme.onTertiary,
          borderRadius: 8,
          maxLines: 1,
          prefixIcon: prefixIcon,
          onEditingComplete: () {
            _formKey.currentState?.validate();
            // debugPrint('onEditingComplete ⛑️');
          },
          onChange: (value) {
            // debugPrint('onChange ⛑️');
          },
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: GoogleFonts.outfit(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
              fontSize: 16.sp,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            filled: true,
            errorMaxLines: 2,
            errorStyle: GoogleFonts.outfit(color: Colors.red, fontSize: 14.sp),
            fillColor: theme.colorScheme.onTertiary,
            contentPadding: EdgeInsets.symmetric(
              vertical: 15.h,
              horizontal: 20.w,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCountryField(ThemeData theme) {
    return FormField<String>(
      validator: FormValidation.validateCountry,

      //  (value) {
      //   if (_selectedCountry.isEmpty) {
      //     return 'Please select a country';
      //   }
      //   return null;
      // },
      builder: (FormFieldState<String> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: 'Country',
                    style: GoogleFonts.plusJakartaSans(
                      color: Colors.grey.shade600,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextSpan(
                    text: ' *',
                    style: GoogleFonts.plusJakartaSans(
                      color: Colors.red,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 4.h),
            CustomCountryPicker(
              key: _countryPickerKey,
              theme: theme,
              initialCountry: _selectedCountry,
              onCountrySelected: (country) {
                setState(() {
                  _selectedCountry = country;
                });
                field.didChange(country);
                _formKey.currentState?.validate();
              },
            ),
            if (field.hasError)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  field.errorText!,
                  style: TextStyle(color: Colors.red, fontSize: 14.sp),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildGenderField(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: 'Gender',
                style: GoogleFonts.outfit(
                  color: Colors.grey.shade600,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: ' *',
                style: GoogleFonts.outfit(
                  color: Colors.red,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 4.h),
        Focus(
          skipTraversal: true,
          descendantsAreFocusable: false,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: AbsorbPointer(
              absorbing: MediaQuery.of(context).viewInsets.bottom > 0,
              child: Container(
                decoration: BoxDecoration(
                  // color: theme.colorScheme.onTertiary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonFormField<String>(
                  value: _selectedGender.isEmpty ? null : _selectedGender,
                  icon: AnimatedRotation(
                    duration: const Duration(milliseconds: 200),
                    turns: _selectedGender.isEmpty ? 0 : 0.5,
                    child: Icon(
                      Icons.keyboard_arrow_down_rounded,
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                      size: 20.h,
                    ),
                  ),
                  menuMaxHeight: 160.h,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: theme.colorScheme.onTertiary,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 20.h,
                      vertical: 15.w,
                    ),
                  ),
                  hint: Text(
                    'Select Gender',
                    style: GoogleFonts.outfit(
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                      fontSize: 16.sp,
                    ),
                  ),
                  items:
                      ['Male', 'Female', 'Prefer not to specify'].map((gender) {
                    return DropdownMenuItem<String>(
                      value: gender,
                      child: Text(
                        gender,
                        style: GoogleFonts.outfit(
                          color: theme.colorScheme.onSurface,
                          fontSize: 14.sp,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedGender = value ?? '';
                    });
                  },
                  validator: FormValidation.validateGender,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  dropdownColor: theme.colorScheme.onTertiary,
                  elevation: 3,
                  isExpanded: true,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _launchTermsAndConditions() async {
    const url =
        'https://cbrs-dashboard-git-50bdaf-eaglelion-system-technologys-projects.vercel.app/terms-and-condition';
    try {
      final uri = Uri.parse(url);
      if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
        if (!mounted) return;
        CustomToastification(
          context,
          message: 'Could not open Terms and Conditions',
        );
      }
    } catch (e) {
      if (!mounted) return;
      CustomToastification(
        context,
        message: 'Error opening Terms and Conditions',
      );
    }
  }

  Future<void> _launchPrivacyPolicy() async {
    const url =
        'https://cbrs-dashboard-git-50bdaf-eaglelion-system-technologys-projects.vercel.app/privacy-policy';
    try {
      final uri = Uri.parse(url);
      if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
        if (!mounted) return;
        CustomToastification(
          context,
          message: 'Could not open Privacy Policy',
        );
      }
    } catch (e) {
      if (!mounted) return;
      CustomToastification(
        context,
        message: 'Error opening Privacy Policy',
      );
    }
  }

  Widget _buildTerms(ThemeData theme) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isChecked = !_isChecked;
        });
      },
      child: Container(
        child: Row(
          children: [
            Checkbox(
              value: _isChecked,
              activeColor: theme.primaryColor,
              side: BorderSide(
                color: theme.primaryColor,
                width: 2,
              ),
              onChanged: (value) {
                setState(() {
                  _isChecked = value ?? false;
                });
              },
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: 'I have agreed to ',
                      style: GoogleFonts.outfit(fontSize: 14.sp),
                    ),
                    TextSpan(
                      text: 'Terms and Conditions',
                      style: GoogleFonts.outfit(
                        color: theme.primaryColor,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w800,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = _launchTermsAndConditions,
                    ),
                    TextSpan(
                      text: ' and ',
                      style: GoogleFonts.outfit(fontSize: 14.sp),
                    ),
                    TextSpan(
                      text: 'Privacy Policy',
                      style: GoogleFonts.outfit(
                        color: theme.primaryColor,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w800,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = _launchPrivacyPolicy,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
