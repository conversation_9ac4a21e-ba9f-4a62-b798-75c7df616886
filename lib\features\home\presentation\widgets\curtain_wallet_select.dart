import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:country_flags/country_flags.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CurtainWalletSelect extends StatefulWidget {
  const CurtainWalletSelect({super.key});

  @override
  _CurtainWalletSelectState createState() => _CurtainWalletSelectState();
}

class _CurtainWalletSelectState extends State<CurtainWalletSelect>
    with TickerProviderStateMixin {
  late AnimationController _curtainAnimationController;

  late Animation<double> curtainAnimation;

  @override
  void initState() {
    super.initState();
    _curtainAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    curtainAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _curtainAnimationController,
        curve: Curves.easeOut,
      ),
    );

    _curtainAnimationController.forward();
  }

  Future<void> closeSheet() async {
    await Future.wait([_curtainAnimationController.reverse()]);
    Navigator.of(context).pop();
  }

  @override
  void dispose() {
    _curtainAnimationController.dispose();

    super.dispose();
  }

  Widget _buildCurtain({required Animation<double> animation}) {
    return AnimatedBuilder(
      animation: animation,
      builder: (_, __) {
        return Align(
          alignment: Alignment.topLeft,
          child: ClipRect(
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              height: animation.value * MediaQuery.of(context).size.height,
              child: ColoredBox(
                color: Colors.white,
                child: SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  child: IntrinsicHeight(
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Select Wallet',
                                  style: GoogleFonts.outfit(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 18.sp,
                                    color: Colors.black,
                                  ),
                                ),
                                SizedBox(height: 2.h),
                                Text(
                                  'Choose from your ETB or USD wallet to make transfers and payments allowed for each wallet.',
                                  style: GoogleFonts.outfit(
                                    fontSize: 14.sp,
                                    color: Colors.black.withOpacity(0.4),
                                  ),
                                ),
                                SizedBox(height: 16.h),
                                _buildWalletCard(
                                  context: context,
                                  countryCode: 'US',
                                  walletName: 'USD Wallet',
                                  walletType: 'USD',
                                  balance: 400,
                                  // _usdBalance,
                                  phoneNumber: '094747474',
                                  //widget.phoneNumber,
                                  registrationDate: 'May 4, 2025',
                                  //widget.registrationDate,
                                  isSelected: false,
                                  //widget.selectedWallet == 'USD',
                                  isBalanceVisible: false,
                                  // _isUsdBalanceVisible,
                                  onToggleBalanceVisibility: () {},

                                  //  _toggleUsdBalanceVisibility,
                                  onTap: () {
                                    // widget.onWalletSelected('USD');
                                    // widget.controller.dismiss(widget.onDismiss);
                                  },
                                  onRefresh: () {},
                                  // _refreshBalances,
                                ),
                                SizedBox(height: 20.h),
                                _buildWalletCard(
                                  context: context,
                                  countryCode: 'ET',
                                  walletName: 'ETB Wallet',
                                  walletType: 'ETB',
                                  balance: 500,
                                  // _etbBalance,
                                  phoneNumber: '099999',
                                  // widget.phoneNumber,
                                  registrationDate: 'May 5, 2024',
                                  //widget.registrationDate,
                                  isSelected: false,
                                  // widget.selectedWallet == 'ETB',
                                  isBalanceVisible: false,
                                  //_isEtbBalanceVisible,
                                  onToggleBalanceVisibility: () {},
                                  // _toggleEtbBalanceVisibility,
                                  onTap: () {
                                    // widget.controller.dismiss(widget.onDismiss);
                                    // widget.onWalletSelected('ETB');
                                  },
                                  onRefresh: () {},
                                  // _refreshBalances,
                                ),

                                /*
                                          _buildWalletCard(
                                            context: context,
                                            countryCode: 'ET',
                                            walletName: 'ETB Wallet',
                                            walletType: 'ETB',
                                            balance: _etbBalance,
                                            phoneNumber: widget.phoneNumber,
                                            registrationDate: widget.registrationDate,
                                            isSelected: widget.selectedWallet == 'ETB',
                                            isBalanceVisible: _isEtbBalanceVisible,
                                            onToggleBalanceVisibility:
                                                _toggleEtbBalanceVisibility,
                                            onTap: () {
                                              widget.controller.dismiss(widget.onDismiss);
                                              widget.onWalletSelected('ETB');
                                            },
                                            onRefresh: _refreshBalances,
                                          ),
                                      */
                                const SizedBox(height: 50),
                              ],
                            ),
                            Center(
                              child: Container(
                                padding:
                                    const EdgeInsets.fromLTRB(10, 8, 17, 8),
                                decoration: BoxDecoration(
                                  color: Colors.black,
                                  borderRadius: BorderRadius.circular(32),
                                ),
                                child: InkWell(
                                  onTap: closeSheet,
                                  // () =>
                                  //     widget.controller.dismiss(widget.onDismiss),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.close,
                                        size: 20.r,
                                        color: Colors.white,
                                      ),
                                      SizedBox(width: 2.w),
                                      Text(
                                        'Close',
                                        style: GoogleFonts.outfit(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Add commas to format number with thousands separators
  String _addCommas(String value) {
    final buffer = StringBuffer();
    final chars = value.split('').reversed.toList();

    for (var i = 0; i < chars.length; i++) {
      if (i > 0 && i % 3 == 0) {
        buffer.write(',');
      }
      buffer.write(chars[i]);
    }

    return buffer.toString().split('').reversed.join();
  }

  Widget _buildWalletCard({
    required BuildContext context,
    required String countryCode,
    required String walletName,
    required String walletType,
    required double? balance,
    required String phoneNumber,
    required String registrationDate,
    required bool isSelected,
    required bool isBalanceVisible,
    required VoidCallback onToggleBalanceVisibility,
    required VoidCallback onTap,
    VoidCallback? onRefresh,
  }) {
    final primaryColor =
        walletType == 'USD' ? const Color(0xFF0D451B) : const Color(0xFF085905);
    final balancePrefix = walletType == 'USD' ? r'$' : '';

    // Check if balance is null before formatting
    final balanceStr = balance?.toStringAsFixed(2) ?? '--';
    final parts = balanceStr.split('.');
    final wholePart = parts[0];
    final decimalPart = parts.length > 1 ? parts[1] : '00';

    // Add commas only if we have a valid number
    final formattedWhole = balance != null ? _addCommas(wholePart) : '--';

    final backgroundImage = walletType == 'USD'
        ? const AssetImage(MediaRes.usdBackground)
        : const AssetImage(MediaRes.birrBackground);

    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: backgroundImage,
            fit: BoxFit.cover,
            colorFilter: ColorFilter.mode(
              Colors.black.withOpacity(0.1),
              BlendMode.darken,
            ),
          ),
          color: primaryColor,
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Wallet Balance',
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.24),
                    borderRadius: BorderRadius.circular(60.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12.r),
                        child: CountryFlag.fromCountryCode(
                          countryCode,
                          height: 19.h,
                          width: 19.w,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        walletName,
                        style: GoogleFonts.outfit(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),

                /*
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(40.r),
                ),
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12.r),
                      child: CountryFlag.fromCountryCode(
                        countryCode,
                        height: 19.h,
                        width: 19.w,
                      ),
                    ),
           
                    SizedBox(width: 8.w),
                    Text(
                      walletName,
                      style: GoogleFonts.outfit(
                        color: Colors.white,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
          */
              ],
            ),
            // SizedBox(height: 12.h),
            Row(
              children: [
                /*
              Expanded(
                child: isBalanceVisible
                    ? RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: balancePrefix,
                              style: GoogleFonts.outfit(
                                fontSize: 24.sp,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            TextSpan(
                              text: formattedWhole,
                              style: GoogleFonts.outfit(
                                fontSize: 28.sp,
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                              ),
                            ),
                            TextSpan(
                              text: '.$decimalPart',
                              style: GoogleFonts.outfit(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            TextSpan(
                              text: walletType == 'ETB' ? ' ETB' : '',
                              style: GoogleFonts.outfit(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      )
                    
                    
                    : Text(
                        '************',
                        style: GoogleFonts.outfit(
                          fontSize: 32.sp,
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                        ),
                      ),
              ),
             */
                if (isBalanceVisible)
                  Row(
                    children: [
                      CustomBuildText(
                        text: balancePrefix,
                        style: GoogleFonts.outfit(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      CustomBuildText(
                        text: formattedWhole,
                        style: GoogleFonts.outfit(
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                        ),
                      ),
                      CustomBuildText(
                        text: '.$decimalPart',
                        style: GoogleFonts.outfit(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      CustomBuildText(
                        text: walletType == 'ETB' ? ' ETB' : '',
                        style: GoogleFonts.outfit(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  )
                else
                  Padding(
                    padding: const EdgeInsets.only(bottom: 6),
                    child: Text(
                      '************',
                      style: GoogleFonts.outfit(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    ),
                  ),
                SizedBox(
                  width: 8.w,
                ),
                InkWell(
                  onTap: onToggleBalanceVisibility,
                  borderRadius: BorderRadius.circular(20.r),
                  child: Padding(
                    padding: EdgeInsets.all(4.r),
                    child: Image.asset(
                      isBalanceVisible ? MediaRes.eyeOpen : MediaRes.eyeClose,
                      color: Colors.white,
                      width: 20.h,
                      height: 20.h,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 32.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Phone Number',
                      style: GoogleFonts.outfit(
                        fontSize: 12.sp,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      phoneNumber,
                      style: GoogleFonts.outfit(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),

                Row(
                  children: [
                    Text(
                      'Registered On:',
                      style: GoogleFonts.outfit(
                        fontSize: 10.sp,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 4.h),
                    Text(
                      registrationDate,
                      style: GoogleFonts.outfit(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                // Column(
                //   crossAxisAlignment: CrossAxisAlignment.end,
                //   children: [
                //     Text(
                //       'Registered On:',
                //       style: GoogleFonts.outfit(
                //         fontSize: 12.sp,
                //         color: Colors.white.withOpacity(0.7),
                //       ),
                //     ),
                //     SizedBox(height: 4.h),
                //     Text(
                //       registrationDate,
                //       style: GoogleFonts.outfit(
                //         fontSize: 14.sp,
                //         fontWeight: FontWeight.w600,
                //         color: Colors.white,
                //       ),
                //     ),
                //   ],
                // ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [_buildCurtain(animation: _curtainAnimationController)],
      ),
    );
  }
}
