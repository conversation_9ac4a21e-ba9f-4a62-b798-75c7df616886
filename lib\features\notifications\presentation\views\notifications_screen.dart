import 'package:cbrs/core/common/widgets/custom_error_retry.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_bloc.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_event.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_state.dart';
import 'package:cbrs/features/notifications/presentation/widgets/notification_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/features/notifications/presentation/widgets/empty_notifications.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NotificationShimmer extends StatelessWidget {
  const NotificationShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...List.generate(3, (sectionIndex) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Section Header Shimmer
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 100.w,
                        height: 20.h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                    ),
                  ),
                  // Notification Container
                  Container(
                    padding:
                        EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: List.generate(
                        sectionIndex == 1 ? 3 : 2,
                        (index) => Column(
                          children: [
                            _buildNotificationTileShimmer(),
                            if (index != (sectionIndex == 1 ? 2 : 1))
                              Padding(
                                padding: EdgeInsets.symmetric(vertical: 8.h),
                                child: Container(
                                  height: 1,
                                  color: Colors.grey[200],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),
                ],
              );
            }),
          ],
        ).animate().fadeIn(duration: 600.ms),
      ),
    );
  }

  Widget _buildNotificationTileShimmer() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: 46.w,
              height: 46.h,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.grey[200]!),
              ),
              child:
                  const Icon(Icons.notifications_outlined, color: Colors.grey),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    width: double.infinity,
                    height: 16.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                ),
                SizedBox(height: 8.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 100.w,
                        height: 12.h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                    ),
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 80.w,
                        height: 12.h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    )
        .animate(delay: 200.ms)
        .fadeIn(duration: 400.ms)
        .slideY(begin: 0.2, end: 0);
  }
}

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<NotificationBloc>().add(FetchNotifications());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, state) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle.dark,
          child: Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              // leading: IconButton(
              //   icon: const Icon(Icons.arrow_back_ios,
              //       color: Colors.black87, size: 20),
              //   onPressed: () => Navigator.pop(context),
              // ),
              
              title: Text(
                'Notifications',
                style: GoogleFonts.outfit(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),

              actions: [
                GestureDetector(
                    onTap: () {
                      CustomToastification(context,
                          message: "We are working on ite");
                    },
                    child: Container(
                      padding: EdgeInsets.all(6),
                      margin: EdgeInsets.only(right: 16),
                      decoration: BoxDecoration(color: Colors.black.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(Icons.check)))
              ],
            ),
            body: _buildBody(context, state),
          ),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, NotificationState state) {
    final theme = Theme.of(context);

    if (state is NotificationLoading) {
      return const NotificationShimmer();
    }

    if (state is NotificationLoaded) {
      debugPrint("Notification loaded");
      if (state.todayNotifications.isEmpty &&
          state.yesterdayNotifications.isEmpty &&
          state.allNotifications.isEmpty) {
        return const EmptyNotifications();
      }

      return RefreshIndicator(
        color: theme.primaryColor,
        onRefresh: () async {
          context.read<NotificationBloc>().add(FetchNotifications());
        },
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            if (scrollInfo is ScrollEndNotification &&
                scrollInfo.metrics.pixels >=
                    scrollInfo.metrics.maxScrollExtent - 200 &&
                !state.hasReachedMax) {
              context.read<NotificationBloc>().add(LoadMoreNotifications());
            }
            return true;
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (state.todayNotifications.isNotEmpty) ...[
                  _buildSectionHeader('Today'),
                  NotificationGroup(notifications: state.todayNotifications),
                ],
                if (state.yesterdayNotifications.isNotEmpty) ...[
                  _buildSectionHeader('Yesterday'),
                  NotificationGroup(
                      notifications: state.yesterdayNotifications),
                ],
                if (state.allNotifications.isNotEmpty) ...[
                  _buildSectionHeader('All Notifications'),
                  NotificationGroup(notifications: state.allNotifications),
                ],
                if (!state.hasReachedMax)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(
                      child: CircularProgressIndicator(
                        color: theme.primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      );
    }

    if (state is NotificationError) {
      return CustomErrorRetry(
        onTap: () {
          context.read<NotificationBloc>().add(FetchNotifications());
        },
      );
    }

    return const NotificationShimmer();
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 12),
      child: Text(
        title,
        style: GoogleFonts.outfit(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
          color: Color(0xFFAAAAAA),
          letterSpacing: 0.3,
        ),
      ),
    );
  }
}
