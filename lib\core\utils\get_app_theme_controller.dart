import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cbrs/core/utils/get_app_theme.dart';
import 'package:shared_preferences/shared_preferences.dart';

class GetAppThemeController extends GetxController {
  RxBool isBirrTheme =
      false.obs; // Default: Dollar Theme (false = Dollar, true = Birr)
  RxBool isInitializing = true.obs;
  RxBool isThemeChanging =
      false.obs; // Flag to prevent multiple theme changes at once

  // Lock to prevent concurrent wallet changes
  final _lock = RxBool(false);

  // Flag to prevent automatic theme changes during initialization
  bool _initializing = true;

  @override
  void onInit() {
    super.onInit();
    // Initialize the theme based on saved wallet type
    _initializeTheme();
  }

  Future<void> _initializeTheme() async {
    try {
      // Always initialize with USD wallet
      debugPrint('GetAppThemeController - Initializing theme with USD wallet');

      // Set initial values for USD wallet
      isBirrTheme.value = false;
      categories = dollarCategories.obs;

      // Apply the Dollar theme
      Get.changeTheme(LightModeTheme().dollarThemeData);

      // Save USD as the wallet type
      await saveWalletType('USD');

      debugPrint('GetAppThemeController - Theme initialized to Dollar theme');
    } catch (e) {
      debugPrint('Error initializing theme: $e');
    } finally {
      isInitializing.value = false;
    }
  }

// TODO
  final List<String> dollarCategories = [
    'All',
    'BANK_TRANSFER',
    'WALLET_TRANSFER',
    'LOAD_TO_WALLET',
    'CHANGE_TO_BIRR',
    'MONEY_REQUEST',
  ];

// TODO

  final List<String> birrCategories = [
    'All',
    'BANK_TRANSFER',
    'WALLET_TRANSFER',
    'CHANGE_TO_BIRR',
    'ADD_MONEY',
    'CASH_IN',
    'CASH_OUT',
    'MONEY_REQUEST',
    'MERCHANT_PAYMENT',
    'Money_REQUEST',

    'TOP_UP',
  ];
// TODO

  RxList<String> categories = RxList<String>([]);

  Future<void> toggleTheme() async {
    // If a theme change is already in progress or the lock is acquired, don't allow another one
    if (isThemeChanging.value || _lock.value) {
      debugPrint('toggleTheme skipped - theme change already in progress');
      return;
    }

    // Acquire the lock
    _lock.value = true;
    isThemeChanging.value = true;

    try {
      debugPrint('toggleTheme starting - current theme: ${isBirrTheme.value}');

      // Toggle the theme synchronously
      final newThemeIsBirr = !isBirrTheme.value;
      final walletType = newThemeIsBirr ? 'ETB' : 'USD';

      // First save the wallet type to ensure persistence
      await saveWalletType(walletType);

      // Then update the UI state
      isBirrTheme.value = newThemeIsBirr;
      categories = newThemeIsBirr ? birrCategories.obs : dollarCategories.obs;

      // Finally apply the theme
      Get.changeTheme(
        newThemeIsBirr
            ? LightModeTheme().themeData
            : LightModeTheme().dollarThemeData,
      );

      debugPrint(
          'toggleTheme completed - new theme: ${isBirrTheme.value}, wallet: $walletType');
    } catch (e) {
      debugPrint('Error in toggleTheme: $e');
    } finally {
      // Release the lock with a small delay to prevent rapid consecutive changes
      await Future<void>.delayed(const Duration(milliseconds: 300));
      isThemeChanging.value = false;
      _lock.value = false;
    }
  }

  Future<void> toggleThemeToBirr() async {
    // If already in Birr theme and not initializing, or if the lock is acquired, don't do anything
    if ((isBirrTheme.value && !isInitializing.value) ||
        isThemeChanging.value ||
        _lock.value) {
      debugPrint(
        'toggleThemeToBirr skipped - already in Birr theme or theme changing',
      );
      return;
    }

    // Acquire the lock
    _lock.value = true;
    isThemeChanging.value = true;

    try {
      debugPrint(
        'toggleThemeToBirr starting - current theme: ${isBirrTheme.value}',
      );

      // First save the wallet type to ensure persistence
      await saveWalletType('ETB');

      // Then update the UI state
      categories = birrCategories.obs;
      isBirrTheme.value = true;

      // Finally apply the theme
      Get.changeTheme(LightModeTheme().themeData);

      debugPrint(
        'toggleThemeToBirr completed - new theme: ${isBirrTheme.value}',
      );
    } catch (e) {
      debugPrint('Error in toggleThemeToBirr: $e');
    } finally {
      isInitializing.value = false;
      // Release the lock with a small delay to prevent rapid consecutive changes
      await Future<void>.delayed(const Duration(milliseconds: 300));
      isThemeChanging.value = false;
      _lock.value = false;
    }
  }

  Future<void> toggleThemeToDollar() async {
    // If already in Dollar theme and not initializing, or if the lock is acquired, don't do anything
    if ((!isBirrTheme.value && !isInitializing.value) ||
        isThemeChanging.value ||
        _lock.value) {
      debugPrint(
        'toggleThemeToDollar skipped - already in Dollar theme or theme changing',
      );
      return;
    }

    // Acquire the lock
    _lock.value = true;
    isThemeChanging.value = true;

    try {
      debugPrint(
        'toggleThemeToDollar starting - current theme: ${isBirrTheme.value}',
      );

      // First save the wallet type to ensure persistence
      await saveWalletType('USD');

      // Then update the UI state
      categories = dollarCategories.obs;
      isBirrTheme.value = false;

      // Finally apply the theme
      Get.changeTheme(LightModeTheme().dollarThemeData);

      debugPrint(
        'toggleThemeToDollar completed - new theme: ${isBirrTheme.value}',
      );
    } catch (e) {
      debugPrint('Error in toggleThemeToDollar: $e');
    } finally {
      isInitializing.value = false;
      // Release the lock with a small delay to prevent rapid consecutive changes
      await Future<void>.delayed(const Duration(milliseconds: 300));
      isThemeChanging.value = false;
      _lock.value = false;
    }
  }

  Future<void> saveWalletType(String walletType) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('wallet_type', walletType);
  }

  // Force set theme based on wallet type without any checks
  Future<void> forceSetTheme(String walletType) async {
    debugPrint(
        'GetAppThemeController - Force setting theme for wallet: $walletType');

    // First save the wallet type
    await saveWalletType(walletType);

    // Then set the theme directly
    if (walletType == 'ETB') {
      categories = birrCategories.obs;
      isBirrTheme.value = true;
      Get.changeTheme(LightModeTheme().themeData);
    } else {
      categories = dollarCategories.obs;
      isBirrTheme.value = false;
      Get.changeTheme(LightModeTheme().dollarThemeData);
    }

    debugPrint(
        'GetAppThemeController - Theme force set to: ${isBirrTheme.value}');
  }

  Future<String> getSavedWalletType() async {
    return 'USD';
  }
}
