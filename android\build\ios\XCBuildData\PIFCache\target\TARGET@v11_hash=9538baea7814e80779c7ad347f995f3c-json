{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984414c5266849560c067a8ad5f3f0e14c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98325a40164b62ddba9ddc87fe4ddd1e91", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98325a40164b62ddba9ddc87fe4ddd1e91", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a5800121c43720df6bcf9d0e935428ef", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98587b5c0bfffa2796c700f96054128b67", "guid": "bfdfe7dc352907fc980b868725387e98421ae3c3425f12162bb95608984e2dab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983da5d2be1cd95a76835aec1a4c99f7cb", "guid": "bfdfe7dc352907fc980b868725387e98b1129dd415ba4f06f295293352794aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e569e2197d76df1179b5bab8cb493da4", "guid": "bfdfe7dc352907fc980b868725387e98c4d6312cb67f31d7f6f6831dd281e8b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b28fcd8dc0de12fe5f5222767519d7b2", "guid": "bfdfe7dc352907fc980b868725387e98f04a5c9a9cc81bfcfa6aaed0b00b6c3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981435cddad4875fd95354612b977966bf", "guid": "bfdfe7dc352907fc980b868725387e9843b79fcddec6fa700bb0007202549f98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981862eef01a26e79c5700e8c7c7a5ef45", "guid": "bfdfe7dc352907fc980b868725387e98c352e5d7dbfbcf376cece1780e77fb1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a83301a86e33a8e883d33ee91fa7298f", "guid": "bfdfe7dc352907fc980b868725387e980e3bf0955a353ef83519ce8948a63fc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98199775225c29cad2a2ec4e1ebfc1fd29", "guid": "bfdfe7dc352907fc980b868725387e982fba501f5a0df4030b4a47f567f7966d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981417ddd1d4318ec997a9743ae926cbfc", "guid": "bfdfe7dc352907fc980b868725387e985f0b6bcef15399d61fa1794fdb818de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b934c7396693d6b42cb01b513829f28b", "guid": "bfdfe7dc352907fc980b868725387e98abea775179281a469ff2dd1142cb783c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd2d71b4a380957e8ba4d255e858e7e", "guid": "bfdfe7dc352907fc980b868725387e988990c98018a3c452a437cf64f7a7b3fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b69578f651d458ad0750e8d10499d0a3", "guid": "bfdfe7dc352907fc980b868725387e98b63102342437207e7dcb551a084a95bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882211740ca467bfeec660aee9b3fc66b", "guid": "bfdfe7dc352907fc980b868725387e98630bd2be35e33853378d0faf1591cb1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828b6eff2b209a17d7d626b560491bf8e", "guid": "bfdfe7dc352907fc980b868725387e9864d155bcb5d32ec1f6dcd38e047c4c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa15a044dded981702bdcebbd810ed11", "guid": "bfdfe7dc352907fc980b868725387e98a43972be6129f885eea405f17f739dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98264b89bdef599d5a6e9ba5b7774eb5ce", "guid": "bfdfe7dc352907fc980b868725387e98c448602647b7c43f19fc7f2b0a19882c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad802337c4e2b998837bb1086da57d69", "guid": "bfdfe7dc352907fc980b868725387e98dfaef89466e79b28a06fbf0d65f3fbaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b528bba61cb04bf53311c752121fb22", "guid": "bfdfe7dc352907fc980b868725387e9838606c9f15f987693f19420c77bf380a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a0a3f779c6b12a4a58ca51ba56e5150", "guid": "bfdfe7dc352907fc980b868725387e988957ceb19237fd9d69b8a1d6b4cf5425"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860be87a7a04a5578d0328adb53e111fd", "guid": "bfdfe7dc352907fc980b868725387e98e15821a1a930d75a5f3933f083e375fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989609c7e2a938c44a968a7f766bb2b3f2", "guid": "bfdfe7dc352907fc980b868725387e9834028e19953e0b1ed245f3913ae35704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed33bb175fe30d5d3cde4020cca1957", "guid": "bfdfe7dc352907fc980b868725387e9834aef25fb16e03a76b8bbf16d245c607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807c168f009052de93ad7cdf1dbe314d0", "guid": "bfdfe7dc352907fc980b868725387e985546f740b510dd60f5bc27de71daf43a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fea33ce5f8aa71d6745f8c1311b1aee5", "guid": "bfdfe7dc352907fc980b868725387e9837f0f173397389d1816969e4e270046a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bd38b32fa2f3a96e1711bb6c9f80346", "guid": "bfdfe7dc352907fc980b868725387e98610dd3c4324e4d24978b8cd7e06bebc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecd228f6c870eacdf452860339d47bd0", "guid": "bfdfe7dc352907fc980b868725387e9847c83ba16e50506728ebf97d34be80b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c550cd872839b74f4bb8878df867164", "guid": "bfdfe7dc352907fc980b868725387e9894d77dc19091448c9de21434a0e30f0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a7c6285af759c629dfe68011fa67926", "guid": "bfdfe7dc352907fc980b868725387e983180877e88a88e10d1a87c3cbf76b203"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f60ee60adf9660d5e2abde429f212f", "guid": "bfdfe7dc352907fc980b868725387e98a0247398fbdb89c3988b46155d18c067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc349e4c43da0549833988984f3a9468", "guid": "bfdfe7dc352907fc980b868725387e989a4819ab88a56141908e0669a1910c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9d93a2698d3033300c62d90cb307943", "guid": "bfdfe7dc352907fc980b868725387e982e29b240ad071385cb99642f4151739c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874ded0f9687802d83c3b52a003429833", "guid": "bfdfe7dc352907fc980b868725387e988c2c14f49e3b26c79b06e7b8b6101921"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981356f56717ee873fa3a4b7ee3df72a69", "guid": "bfdfe7dc352907fc980b868725387e98922e57193746f7f5fdd4494b90d735bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f58a7afdca2248d0782ec4c7e11ee5", "guid": "bfdfe7dc352907fc980b868725387e98ba3fb8295e51139e9cce240a1e715d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2d2ab0450acc78116ff11c8b8a13edf", "guid": "bfdfe7dc352907fc980b868725387e9874987f8de6c5b4c86bf766cadd18dedb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98950f52e56927ca279c6ace30934b64f0", "guid": "bfdfe7dc352907fc980b868725387e9853de38235b70c71345bc88a3f70fb6ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e3f8b6c80a49a8568f7cdb16f57cd1", "guid": "bfdfe7dc352907fc980b868725387e98576d77e91596b9da0d88acba8efaf0c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98648c3af8213a0efbac4355167e939586", "guid": "bfdfe7dc352907fc980b868725387e989f90dd5960fdec2d95b170b382f26e6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804673c91cc24da093e1d23a5f48ec417", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870dfddc621070e66fac3f6685750a8ba", "guid": "bfdfe7dc352907fc980b868725387e98f907c5a490d4761e6ce9b01cae1c968a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98951f7b3af5cd01249ed3b1640848f7eb", "guid": "bfdfe7dc352907fc980b868725387e980d3613196654dfcf6a896937bb373434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98215d71afd3425bb7252c75f43be625b0", "guid": "bfdfe7dc352907fc980b868725387e9818cf0c36a9406704d29f6d45ba79c104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848cd961d9cff024f1eff4426a2f81a4c", "guid": "bfdfe7dc352907fc980b868725387e98d09bffc616c2cc7ff369d082d9774c93"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}