import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/mini_apps/domain/entities/create_order_miniapp_entity.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp_success_entity.dart';
import 'package:dartz/dartz.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:cbrs/core/error/failures.dart';

abstract class MiniappRepository {
  ///1. fetch utitlity
  ///2. create order
  ///3. confirm payment
  ///
  ///1. fetch utitlity
  ResultFuture<MiniappEntity> getMiniapps({
    required int page,
    required int perPage,
    required String stage,
  });

  ResultFuture<CreateOrderMiniappEntity> createOrder({
    required dynamic data,
  });

  ResultFuture<MiniappSuccessEntity> submitPin({
    required String transactionType,
    required String billRefNo,
    required String pin,
  });

  ResultFuture<MiniappEntity> submitOtp({
    required String transactionType,
    required String billRefNo,
    required String otp,
  });
}
