part of 'injection_container.dart';

final sl = GetIt.instance;

Future<void> init() async {
  final appDocumentDir = await getApplicationDocumentsDirectory();
  await Hive.initFlutter(appDocumentDir.path);

  final hiveManager = HiveBoxManager();
  await hiveManager.init();

  sl.registerLazySingleton(() => hiveManager);

  await _registerCoreDependencies();

  await initAuth();
  await _registerDeviceCheck();
  await _onBoardingInit();
  await _initLoadWallet();
  await _initHome();
  await _initWalletBalance();

  await _initBankTransfer();
  await _initRecentTransactions();

  await _initTransactions();
  await _initProfile();
  await _initTransferToWallet();
  await _initRecentWalletTransactions();
  await _initChangeToBirr();
  await _initNotifications();

  await _initFcm();
  await _initRepaymentLoan();
  await _initGiftPackages();
  await _initOrders();
  await _initForceUpdate();
  await _initSyncContacts();
  await _initAccountLink();
  await _initAddMoney();
  await _initBalanceCheck();
  await _initTopUp();
  await _initUtility();
  await _initMiniapp();
  await _initQrParsing();
  await _initIdentityVerification();
  await _initMyConnect();

  _initMoneyRequest();

  sl.registerLazySingleton(
    () => SessionTimeoutService(
      navigationService: sl(),
      authLocalDataSource: sl(),
    ),
  );

  Get.put<SessionTimeoutService>(
    SessionTimeoutService(
      navigationService: sl<NavigationService>(),
      authLocalDataSource: sl<AuthLocalDataSource>(),
    ),
    permanent: true,
  );
  await _initMerchantPayment();
  await _initGuest();
  await _initVoucher();
  await _initLoans();
  await _initMiniStatements();
}

Future<void> _registerCoreDependencies() async {
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  sl
    ..registerLazySingleton(LocalAuthentication.new)
    ..registerLazySingleton(
      () => DeviceService(hiveManager: sl()),
    )
    ..registerLazySingleton(
      () => ApiConfig(localDataSource: sl()),
    )
    ..registerLazySingleton(() => sl<ApiConfig>().client)
    ..registerLazySingleton(ConnectivityService.new)
    ..registerLazySingleton(ConnectivityController.new)
    ..registerLazySingleton(
      () => ApiService(localDataSource: sl<AuthLocalDataSource>()),
    )
    ..registerLazySingleton(
      () => NavigationService(
        hiveBoxManager: sl(),
        authLocalDataSource: sl(),
      ),
    );

  final connectivityController = sl<ConnectivityController>();
  Get.put(connectivityController, permanent: true);
  connectivityController.onInit();
}

Future<void> _initHome() async {
  sl.registerFactory<HomeBloc>(
    () => HomeBloc(
      profileRepository: sl(),
      transactionRepository: sl(),
      bankTransferRepository: sl(),
    ),
  );
}

Future<void> _initWalletBalance() async {
  sl.registerFactory<WalletBalanceBloc>(
    () => WalletBalanceBloc(
      bankTransferRepository: sl(),
    ),
  );
}

Future<void> _initMerchantPayment() async {
  sl
    // Bloc
    ..registerFactory(
      () => MerchantBloc(
        getMerchantByTillNumber: sl(),
        merchantRepository: sl(),
      ),
    )
    ..registerFactory(
      () => MerchantPaymentBloc(
        lookupContacts: sl(),
        lookupMember: sl(),
        transferToWallet: sl(),
        validateTransferAmount: sl(),
        checkTransferStatus: sl(),
        checkMerchantPaymentRulesUseCase: sl(),
      ),
    )

    // Use cases
    ..registerLazySingleton(() => GetMerchantByTillNumber(sl()))
    ..registerLazySingleton(() => MerchantPayment(sl()))
    ..registerLazySingleton(() => CheckMerchantPaymentRules(sl()))

    // Repository
    ..registerLazySingleton<MerchantRepository>(
      () => MerchantRepositoryImpl(
        dio: sl(),
        remoteDataSource: sl(),
      ),
    )
    ..registerLazySingleton<MerchantPaymentRepository>(
      () => MerchantPaymentRepositoryImpl(sl()),
    )

    // Data sources
    ..registerLazySingleton<MerchantPaymentRemoteDataSource>(
      () => MerchantPaymentRemoteDataSourceImpl(
        apiService: sl(),
        authLocalDataSource: sl(),
      ),
    );
}

Future<void> _initVoucher() async {
  sl
    ..registerLazySingleton<VoucherRepository>(
      () => VoucherRepositoryImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton(
      () => GenerateVoucherUseCase(sl()),
    );
}

Future<void> _initAgent() async {
  sl
    ..registerLazySingleton<BillRepository>(
      () => BillRepositoryImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<AgentRepository>(
      () => AgentRepositoryImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<TransferRulesRepository>(
      () => TransferRulesRepositoryImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<PaymentConfirmationRepository>(
      () => PaymentConfirmationRepositoryImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<OtpVerificationRepository>(
      () => OtpVerificationRepositoryImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton(
      () => GenerateBillUseCase(sl()),
    )
    ..registerLazySingleton(
      () => SearchAgentUseCase(sl()),
    )
    ..registerLazySingleton(
      () => CashInCashInCheckTransferRulesUseCase(sl()),
    )
    ..registerLazySingleton(
      () => ConfirmPaymentUseCase(sl()),
    )
    ..registerLazySingleton(
      () => VerifyOtpUseCase(sl()),
    )
    ..registerLazySingleton(
      () => CashOutResendOtpUseCase(sl()),
    )
    ..registerLazySingleton(
      () => ParseAgentQrUseCase(sl()),
    )
    ..registerFactory(
      () => CashInCashOutBloc(
        generateVoucherUseCase: sl(),
        searchAgentUseCase: sl(),
        generateBillUseCase: sl(),
        checkTransferRulesUseCase: sl(),
        confirmPaymentUseCase: sl(),
        verifyOtpUseCase: sl(),
        resendOtpUseCase: sl(),
        parseAgentQrUseCase: sl(),
      ),
    );
}

// // in app update
// Future<void> _initForceUpdate() async {
//   sl
//     ..registerLazySingleton<InAppRemoteDatasource>(
//       () => InAppRemoteDatasourceImpl(
//         dio: sl(),
//       ),
//     )
//     ..registerLazySingleton(() => CheckForUpdateUseCase(sl()))
//     ..registerLazySingleton<InAppUpdateRepository>(
//       () => InAppUpdateRepositoriesImpl(
//         remoteDataSource: sl(),
//       ),
//     )
//     ..registerFactory(
//         () => UpdateBloc(repository: sl<InAppUpdateRepository>()));
// }

Future<void> _initForceUpdate() async {
  sl
    ..registerLazySingleton(
      () => InAppUpdateRepository(
        dio: sl(),
      ),
    )
    ..registerLazySingleton(
      () => InAppUpdateController(
        inAppUpdateRepositories: sl(),
        navigationService: sl(),
      ),
    );

  final inAppUpdateController = sl<InAppUpdateController>();
  Get.put(inAppUpdateController, permanent: true);
}

Future<void> _registerDeviceCheck() async {
  sl
    ..registerLazySingleton(
      () => DeviceCheckRepository(
        dio: sl(),
        deviceService: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton(
      () => DeviceCheckController(
        deviceCheckRepository: sl(),
        navigationService: sl(),
      ),
    );

  final deviceCheckController = sl<DeviceCheckController>();
  Get.put(deviceCheckController, permanent: true);
}

Future<void> _initGiftPackages() async {
  sl
    // Blocs
    ..registerFactory(
      () => PackagesBloc(
        getGiftPackages: sl(),
        getBannerPackages: sl(),
      ),
    )

    // Use Cases
    ..registerLazySingleton(() => GetGiftPackages(sl()))
    ..registerLazySingleton(() => GetBannerPackages(sl()))
    ..registerLazySingleton(() => GetPackageDetail(sl()))
    ..registerLazySingleton(() => GetCategories(sl()))
    ..registerLazySingleton(() => GetBanners(sl()))
    ..registerLazySingleton(() => MemberLookup(sl()))
    // ..registerLazySingleton(() => PurchaseGiftPackage(sl()))
    ..registerLazySingleton(() => GetBannerCategories(sl()))

    // Repository
    ..registerLazySingleton<GiftPackageRepository>(
      () => GiftPackageRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
        connectivityService: sl(),
      ),
    )

    // Data Sources
    ..registerLazySingleton<GiftPackageRemoteDataSource>(
      () => GiftPackageRemoteDataSourceImpl(
        apiService: sl(),
      ),
    )
    ..registerLazySingleton<GiftPackageLocalDataSource>(
      () => GiftPackageLocalDataSourceImpl(
        sharedPreferences: sl(),
      ),
    );
}

Future<void> _initFcm() async {
  debugPrint('FCM token initialised');
  sl.registerLazySingleton(() => FcmService(authLocalDataSource: sl()));
}

Future<void> _initNotifications() async {
  sl
    ..registerLazySingleton<NotificationRemoteDataSource>(
      () => NotificationRemoteDataSourceImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<NotificationRepository>(
      () => NotificationRepositoryImpl(sl()),
    )
    ..registerLazySingleton(() => FetchNotifications(sl()))
    ..registerLazySingleton(() => FetchUnseenCount(sl()))
    ..registerLazySingleton(() => MarkNotificationAsRead(sl()))
    ..registerFactory(
      () => NotificationBloc(
        notificationRepository: sl(),
      ),
    );
}

Future<void> _initTransferToWallet() async {
  sl
    ..registerLazySingleton<WalletTransferRemoteDataSource>(
      () => WalletTransferRemoteDataSourceImpl(
        apiService: sl(),
        authBox: sl<HiveBoxManager>().authBox,
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<WalletTransferRepository>(
      () => WalletTransferRepositoryImpl(sl()),
    )
    ..registerLazySingleton(() => LookupContacts(sl()))
    ..registerLazySingleton(() => LookupMember(sl()))
    ..registerLazySingleton(() => TransferToWallet(sl()))
    ..registerLazySingleton(() => ValidateWalletTransferAmount(sl()))
    ..registerLazySingleton(() => CheckWalletTransferStatus(sl()))
    ..registerLazySingleton(() => CheckWalletTransferRules(sl()))
    ..registerLazySingleton(() => GenerateRecieptUsecase(sl()))
    ..registerFactory(
      () => WalletTransferBloc(
        lookupContacts: sl(),
        lookupMember: sl(),
        transferToWallet: sl(),
        validateTransferAmount: sl(),
        checkTransferStatus: sl(),
        checkTransferRulesUseCase: sl(),
        generateRecieptUsecase: sl(),
      ),
    );
}

Future<void> _initLoadWallet() async {
  sl
    ..registerLazySingleton<LoadWalletRepository>(
      () => LoadWalletRepositoryImpl(
        sl(),
      ),
    )
    ..registerLazySingleton<LoadTowalletRemoteDataSource>(
      () => LoadTowalletRemoteDataSourceImpl(
        apiService: sl(),
      ),
    )
    ..registerLazySingleton(() => LoadToWalletUseCase(sl()))
    ..registerLazySingleton(() => CheckLoadWalletStatusUseCase(sl()))
    ..registerFactory(
      () => LoadWalletBloc(
        loadToWalletUseCase: sl(),
        checkLoadWalletStatusUseCase: sl(),
      ),
    );
}

Future<void> _initProfile() async {
  sl
    ..registerLazySingleton<ProfileRemoteDataSource>(
      () => ProfileRemoteDataSourceImpl(sl()),
    )
    ..registerLazySingleton<ProfileLocalDataSource>(
      () => SecureProfileLocalDataSourceImpl(
        profileBox: sl<HiveBoxManager>().authBox,
      ),
    )
    ..registerLazySingleton<ProfileRepository>(
      () =>
          ProfileRepositoryImpl(remoteDataSource: sl(), localDataSource: sl()),
    )
    ..registerLazySingleton(() => UpdateAvatarUsecase(sl()))
    ..registerLazySingleton(() => DeleteAvatarUseCase(sl()))
    ..registerFactory(
      () => ProfileBloc(
        repository: sl<ProfileRepository>(),
        deleteAvatarUseCase: sl(),
        updateAvatarUsecase: sl(),
      ),
    );
}

Future<void> _onBoardingInit() async {
  sl
    ..registerFactory(
      () => OnBoardingCubit(
        cacheFirstTimer: sl(),
        checkIfUserFirstTimer: sl(),
      ),
    )
    ..registerLazySingleton(() => CacheFirstTimer(sl()))
    ..registerLazySingleton(() => CheckIfUserFirstTimer(sl()))
    ..registerLazySingleton<OnBoardingRepo>(() => OnBoardingRepoImpl(sl()))
    ..registerLazySingleton<OnBoardingLocalDataSource>(
      () => OnBoardingLocalDataSourceImpl(hiveManager: sl()),
    );
}

Future<void> initAuth() async {
  sl
    ..registerLazySingleton<AuthRemoteDataSource>(
      () => AuthRemoteDataSourceImpl(
        apiService: sl(),
        deviceService: sl(),
        localDataSource: sl(),
      ),
    )
    ..registerLazySingleton<AuthLocalDataSource>(
      () => SecureAuthLocalDataSourceImpl(
        authBox: sl<HiveBoxManager>().authBox,
        localAuth: sl(),
      ),
    )
    ..registerLazySingleton<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
      ),
    )
    ..registerLazySingleton(() => SignInWithEmail(sl()))
    ..registerLazySingleton(() => SignInWithPhone(sl()))
    ..registerLazySingleton(() => VerifyOtp(sl()))
    ..registerLazySingleton(() => VerifyEmail(sl()))
    ..registerLazySingleton(() => ResendOtp(sl()))
    ..registerLazySingleton(() => ResendEmailVerification(sl()))
    ..registerLazySingleton(() => SignUp(sl()))
    ..registerLazySingleton(() => CreatePin(sl()))
    ..registerLazySingleton(() => LoginWithPin(sl()))
    ..registerLazySingleton(() => ForgotPin(sl()))
    ..registerLazySingleton(() => ForgotPinWithPhone(sl()))
    ..registerLazySingleton(() => ResetPin(sl()))
    ..registerLazySingleton(() => UnlinkDevice(sl()))
    ..registerLazySingleton(() => Logout(sl()))
    ..registerFactory(
      () => AuthBloc(
        signInWithEmail: sl(),
        signInWithPhone: sl(),
        verifyOtp: sl(),
        verifyEmail: sl(),
        resendOtp: sl(),
        resendEmailVerification: sl(),
        signUp: sl(),
        createPin: sl(),
        loginWithPin: sl(),
        forgotPin: sl(),
        forgotPinWithPhone: sl(),
        resetPin: sl(),
        unlinkDevice: sl(),
        logout: sl(),
        localDataSource: sl(),
      ),
    );
}

Future<void> _initBankTransfer() async {
  if (!Hive.isAdapterRegistered(2)) {
    Hive.registerAdapter(BankModelAdapter());
  }

  final banksBox = await Hive.openBox<banks.BankModel>('banks');
  final timestampBox = await Hive.openBox<DateTime>('banks_timestamp');
  sl
    ..registerLazySingleton<BankLocalDataSource>(
      () => BankLocalDataSourceImpl(
        banksBox: banksBox,
        timestampBox: timestampBox,
      ),
    )
    ..registerLazySingleton<BankTransferRemoteDataSource>(
      () => BankTransferRemoteDataSourceImpl(
        apiService: sl(),
        localDataSource: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<BankTransferRepository>(
      () => BankTransferRepositoryImpl(sl()),
    )
    ..registerLazySingleton(() => LookupAccount(sl()))
    ..registerLazySingleton(() => TransferToBank(sl()))
    ..registerLazySingleton(() => GetWalletDetails(sl()))
    ..registerLazySingleton(() => GetSupportedBanks(sl()))
    ..registerLazySingleton(() => ValidateTransferAmount(sl()))
    ..registerLazySingleton(() => CheckTransferStatus(sl()))
    ..registerLazySingleton(() => GetExchangeRate(sl()))
    ..registerLazySingleton(() => CheckBankTransferRules(sl()))
    ..registerLazySingleton(() => GetSupportedPaginatedBanks(sl()))
    ..registerFactory(
      () => BankTransferBloc(
        checkTransferRules: sl(),
        lookupAccount: sl(),
        transferToBank: sl(),
        getSupportedBanks: sl(),
        validateTransferAmount: sl(),
        checkTransferStatus: sl(),
        getWalletDetails: sl(),
        getExchangeRate: sl(),
        getSupportedPaginatedBanks: sl(),
        authLocalDataSource: sl(),
      ),
    );
}

Future<void> _initRecentTransactions() async {
  if (!Hive.isAdapterRegistered(3)) {
    Hive.registerAdapter(RecentTransactionHiveAdapter());
  }

  await Hive.openBox<RecentTransactionHive>('recentTransactionsBox');
  sl.registerFactory(BankTransferRecentTransactionBloc.new);
}

Future<void> _initRecentWalletTransactions() async {
  if (!Hive.isAdapterRegistered(4)) {
    Hive.registerAdapter(RecentWalletTransferHiveAdapter());
  }
  // await Hive.deleteBoxFromDisk("recentWalletTransactionsBox");
  await Hive.openBox<RecentWalletTransferHive>('recentWalletTransactionsBox');

  sl
    ..registerLazySingleton(() => GetUsersAvatarUseCase(sl()))
    ..registerFactory(
      () => RecentWalletTransferBloc(
        getUsersAvatarUseCase: sl(),
      ),
    );
}

Future<void> _initChangeToBirr() async {
  sl
    ..registerLazySingleton<ChangeToBirrRemoteDataSource>(
      () => ChangeToBirrRemoteDataSourceImpl(
        apiService: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<ChangeToBirrRepository>(
      () => ChangeToBirrRepositoryImpl(
        sl(),
      ),
    )
    ..registerLazySingleton(() => ChangeToBirrUseCase(sl()))
    ..registerLazySingleton(() => CheckTransferRulesUseCase(sl()))
    ..registerFactory(
      () => ChangeToBirrBloc(
        changeToBirrUseCase: sl(),
        checkTransferRulesUseCase: sl(),
      ),
    );
}

Future<void> _initRepaymentLoan() async {
  sl
    ..registerLazySingleton<RepaymentRemoteDataSource>(
      () => RepaymentRemoteDataSourceImpl(
        apiService: sl(),
        authBox: sl<HiveBoxManager>().authBox,
      ),
    )
    ..registerLazySingleton(() => FetchLoanRepaymentUsecase(sl()))
    ..registerLazySingleton<LoanRepaymentRepository>(
      () => LoanRepaymentRepositoryImpl(
        remoteDataSource: sl(),
      ),
    )
    ..registerLazySingleton(() => ConfirmMonthlyRepaymentWithPinUsecase(sl()))
    ..registerLazySingleton(() => GenerateMonthlyRepaymentUseCase(sl()))
    ..registerLazySingleton(() => GenerateUpfrontPaymentUsecase(sl()))
    ..registerLazySingleton(() => ConfirmUpfrontWithPinUsecase(sl()))
    ..registerLazySingleton(() => MyLoanResendOtpUsecase(sl()))
    ..registerLazySingleton(() => MyLoanVerifyOtpUseCase(sl()))
    ..registerFactory(
      () => UpfrontPaymentBloc(
        generateUpfrontPaymentUsecase: sl(),
        confirmUpfrontWithPinUsecase: sl(),
        myLoanResendOtpUsecase: sl(),
        myLoanVerifyOtpUseCase: sl(),
        confirmMonthlyRepaymentWithPinUsecase: sl(),
        generateMonthlyRepaymentUseCase: sl(),
      ),
    )
    ..registerFactory(
      () => RepaymentBloc(
        repository: sl<LoanRepaymentRepository>(),
      ),
    );
}

Future<void> _initGuest() async {
  sl
    ..registerLazySingleton<GuestRemoteDataSource>(
      () => GuestRemoteDataSourceImpl(
        dio: sl(),
      ),
    )
    ..registerLazySingleton<GuestRepository>(
      () => GuestRepositoryImpl(sl()),
    )
    ..registerLazySingleton(() => GuestAccountLookup(sl()))
    ..registerLazySingleton(() => GuestTransferInitiate(sl()))
    ..registerLazySingleton(() => GuestTransferStatusCheck(sl()))
    ..registerLazySingleton(
      () => GetExchangeRatesUsecase(exchangeRateRepository: sl()),
    )
    ..registerLazySingleton<ExchangeRateRemoteDataSource>(
      () => ExchangeRateRemoteDataSourceImpl(
        authLocalDataSource: sl(),
        dio: sl(),
      ),
    )
    ..registerLazySingleton<ExchangeRateRepository>(
      () => ExchangeRateImp(exchangeRateRemoteDataSource: sl()),
    )
    ..registerFactory(() => ExchangeRateBloc(getExchangeRatesUsecase: sl()))
    ..registerFactory(
      () => GuestBloc(
        accountLookup: sl(),
        transferInitiate: sl(),
        transferStatusCheck: sl(),
      ),
    );
}

Future<void> _initSyncContacts() async {
  sl.registerLazySingleton(
    () => ContactSyncService(
      dio: sl(),
      contactsBox: sl<HiveBoxManager>().contactsBox,
    ),
  );

  final contactSyncService = sl<ContactSyncService>();

  Get.put(contactSyncService, permanent: true);
}

Future<void> _initTransactions() async {
  sl
    // Blocs
    ..registerFactory(
      () => TransactionBloc(
        getTransactions: sl(),
        getTransferLimit: sl(),
        getRecentWalletTransfers: sl(),
        getLoanInvoice: sl(),
        authLocalDataSource: sl(),
        confirmTransfer: sl(),
        verifyTransactionOtp: sl(),
        resendTransactionOtp: sl(),
        getInvoice: sl(),
      ),
    )
    ..registerFactory(
      () => TransactionDetailsBloc(
        validateTransactionUsecase: sl(),
        getTransactionDetails: sl(),
        getInvoice: sl(),
      ),
    )

    // Use cases
    ..registerLazySingleton(() => ValidateTransactionUsecase(sl()))
    ..registerLazySingleton(() => GetTransactions(sl()))
    ..registerLazySingleton(() => GetTransactionDetails(sl()))
    ..registerLazySingleton(() => GetInvoice(sl()))
    ..registerLazySingleton(() => GetRecentWalletTransfers(sl()))
    ..registerLazySingleton(() => GetTransferLimit(sl()))
    ..registerLazySingleton(() => GetLoanInvoice(sl()))
    ..registerLazySingleton(() => ConfirmTransfer(sl()))
    ..registerLazySingleton(() => VerifyTransactionOtp(sl()))
    ..registerLazySingleton(() => ResendTransactionOtp(sl()))

    // Repository
    ..registerLazySingleton<TransactionRepository>(
      () => TransactionRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
      ),
    )

    // Data sources
    ..registerLazySingleton<TransactionRemoteDataSource>(
      () => TransactionRemoteDataSourceImpl(
        apiService: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<TransactionLocalDataSource>(
      () => TransactionLocalDataSourceImpl(
        sharedPreferences: sl(),
      ),
    );
}

Future<void> _initOrders() async {
  sl
    // Data Sources
    ..registerLazySingleton<OrderRemoteDataSource>(
      () => OrderRemoteDataSourceImpl(
        apiService: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerLazySingleton<OrderLocalDataSource>(
      () => OrderLocalDataSourceImpl(
        sharedPreferences: sl(),
      ),
    )
    ..registerFactory(
      () => OrderBloc(repository: sl()),
    )

    // Repository
    ..registerLazySingleton<OrderRepository>(
      () => OrderRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
        connectivityService: sl(),
      ),
    );
}

Future<void> _initAccountLink() async {
  sl
    ..registerFactory(
      () => AccountLinkBloc(
        generateLinkAccountUsecase: sl(),
        pendedLinkedAccountUseCase: sl(),
      ),
    )
    ..registerLazySingleton<LinkAccountRemoteDataSource>(
      () => LinkAccountRemoteDataSourceImpl(
        dio: sl(),
        authBox: sl<HiveBoxManager>().authBox,
      ),
    )
    ..registerLazySingleton(() => GenerateLinkAccountUsecase(sl()))
    ..registerLazySingleton(() => PendedLinkedAccountUseCase(sl()))
    ..registerLazySingleton<LinkAccountRepositories>(
      () => LinkAccountRepositoriesImpl(
        remoteDataSource: sl(),
      ),
    );
}

Future<void> _initAddMoney() async {
  sl
    // Blocs
    ..registerFactory(
      () => AddMoneyBloc(
        getLinkedAccounts: sl(),
        checkAccountBalance: sl(),
        checkTransferRules: sl(),
        addMoney: sl(),
      ),
    )

    // Use cases
    ..registerLazySingleton(() => add_money.GetLinkedAccounts(sl()))
    ..registerLazySingleton(() => CheckAccountBalance(sl()))
    ..registerLazySingleton(() => CheckTransferRules(sl()))
    ..registerLazySingleton(() => AddMoney(sl()))

    // Repository
    ..registerLazySingleton<AddMoneyRepository>(
      () => AddMoneyRepositoryImpl(
        sl(),
      ),
    )

    // Data sources
    ..registerLazySingleton<AddMoneyRemoteDataSource>(
      () => AddMoneyRemoteDataSourceImpl(
        apiService: sl(),
      ),
    );
}

Future<void> _initBalanceCheck() async {
  sl
    // Blocs
    ..registerFactory(
      () => BalanceCheckBloc(
        getAccountBalance: sl(),
        getLinkedAccounts: sl(),
        getRecentTransactions: sl(),
        getAccountTransactions: sl(),
      ),
    )
    ..registerFactory(
      () => RecentLinkedTransactionBloc(
        getRecentTransactions: sl(),
        getAccountTransactions: sl(),
        getAllTransactionsUseCase: sl(),
      ),
    )

    // Use cases
    ..registerLazySingleton(() => GetAccountBalance(sl()))
    ..registerLazySingleton(() => balance.GetLinkedAccounts(sl()))
    ..registerLazySingleton(() => GetAllTransactionsUseCase(sl()))
    ..registerLazySingleton(() => GetRecentTransactions(sl()))
    ..registerLazySingleton(() => GetAccountTransactions(sl()))
    ..registerLazySingleton<BalanceCheckRepository>(
      () => BalanceCheckRepositoryImpl(
        sl(),
      ),
    )

    // Data sources
    ..registerLazySingleton<BalanceCheckRemoteDataSource>(
      () => BalanceCheckRemoteDataSourceImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    );
}

Future<void> _initTopUp() async {
  sl
    // Blocs
    ..registerFactory(
      () => TopUpBloc(
        topUpCreateTopUpUsecase: sl(),
        topUpGetProviders: sl(),
      ),
    )

    // Use cases
    ..registerLazySingleton(() => TopUpGetProviders(sl()))
    ..registerLazySingleton(() => TopUpCreateTopUpUsecase(sl()))
    ..registerLazySingleton<TopUpRepositories>(
      () => TopUpRepositoriesImpl(
        sl(), // Just pass remoteDataSource
      ),
    )

    // Data sources
    ..registerLazySingleton<TopUpRemoteDataSource>(
      () => TopUpRemoteDataSourceImpl(
        apiService: sl(),
      ),
    );
}

void _initMoneyRequest() {
  sl
    ..registerLazySingleton<addMoney.MemberLookup>(
      () => addMoney.MemberLookup(
        sl(),
      ),
      instanceName: 'AddMoney_MemberLookup',
    )
    ..registerFactory<SendMoneyRequestMemberLookupBloc>(
      () => SendMoneyRequestMemberLookupBloc(
        sl(instanceName: 'AddMoney_MemberLookup'),
      ),
    )
    ..registerLazySingleton<MoneyRequestRepository>(
      () => MoneyRequestRepositoryImpl(
        sl(),
      ),
    )
    ..registerLazySingleton<MoneyRequestRemoteDataSource>(
      () => MoneyRequestRemoteDataSourceImpl(
        apiService: sl(),
        authLocalDataSource: sl(),
      ),
    )
    ..registerFactory<SendMoneyRequestAddMoneyBloc>(
      SendMoneyRequestAddMoneyBloc.new,
    )
    ..registerLazySingleton<SendMoneyRequest>(() => SendMoneyRequest(sl()))
    ..registerFactory<SendRequestMoneyConfirmRequestBloc>(
      () => SendRequestMoneyConfirmRequestBloc(sl()),
    )
    ..registerLazySingleton<GetMoneyRequestList>(
      () => GetMoneyRequestList(sl()),
    )
    ..registerLazySingleton<AcceptRequest>(() => AcceptRequest(sl()))
    ..registerLazySingleton<RejectRequest>(() => RejectRequest(sl()))
    ..registerLazySingleton<CancelRequest>(() => CancelRequest(sl()))
    ..registerLazySingleton<ConfirmRequest>(() => ConfirmRequest(sl()))
    ..registerLazySingleton<GetWalletDetail>(() => GetWalletDetail(sl()))
    ..registerFactory<MoneyRequestListBloc>(
      () => MoneyRequestListBloc(sl()),
    )
    ..registerFactory<MoneyRequestDetailBloc>(
      () => MoneyRequestDetailBloc(
        sl(),
        sl(),
        sl(),
        sl(),
        sl(),
      ),
    );
}

Future<void> _initLoans() async {
  sl
    // Repository and Data Sources first
    ..registerLazySingleton<LoanRemoteDataSource>(
      () => LoanRemoteDataSourceImpl(
        apiService: sl(),
      ),
    )
    ..registerLazySingleton<LoanRepository>(
      () => LoanRepositoryImpl(
        remoteDataSource: sl(),
      ),
    )
    // Use cases
    ..registerLazySingleton(() => GetPaginatedLoanItems(sl()))
    ..registerLazySingleton(() => GetAllLoanCategories(sl()))
    ..registerLazySingleton(() => GetLoanCategories(sl()))
    ..registerLazySingleton(() => GetLoanCategoryById(sl()))
    ..registerLazySingleton(() => GetLoanBanks(sl()))
    ..registerLazySingleton(() => loans.CalculateLoanPayment(sl()))
    ..registerLazySingleton(() => ApplyForLoan(sl()))
    ..registerLazySingleton(
      () => loans_generate.GenerateApplicationTransaction(sl()),
    )
    ..registerLazySingleton(() => LoanConfirmWithOtpUsecase(sl()))
    ..registerLazySingleton(() => LoanConfirmResendOtpUsecase(sl()))
    ..registerLazySingleton(() => LoanConfirmWithPinUsecase(sl()))
    ..registerLazySingleton(() => ConfirmLoanPayment(sl()))
    // Cubit
    ..registerFactory(
      () => LoanCategoriesCubit(
        getAllLoanCategories: sl(),
        getLoanCategories: sl(),
        getLoanCategoryById: sl(),
      ),
    )
    ..registerFactory(() => LoanTermsCubit(loanRepository: sl()))
    ..registerFactory(
      () => LoanBanksCubit(
        getLoanBanks: sl(),
      ),
    )
    ..registerFactory(
      () => LoanPaymentInfoCubit(
        calculateLoanPayment: sl(),
      ),
    )
    // Bloc (last, after all dependencies)
    ..registerFactory(
      () => LoanItemsBloc(
        getPaginatedLoanItems: sl(),
        loanConfirmWithOtpUsecase: sl(),
        loanConfirmResendOtpUsecase: sl(),
        loanConfirmWithPinUsecase: sl(),
        applyForLoan: sl(),
        generateApplicationTransaction:
            sl<loans_generate.GenerateApplicationTransaction>(),
        confirmLoanPayment: sl(),
      ),
    );
}

// utility
Future<void> _initUtility() async {
  sl
    // Bloc
    ..registerFactory(
      () => UtilityBloc(
        createOrderUseCase: sl(),
        getUtilitiesUseCase: sl(),
        submitOtpUtilityUseCase: sl(),
        submitPinUtilityUseCase: sl(),
      ),
    )
    // Use cases
    ..registerLazySingleton(() => CreateOrderUseCase(sl()))
    ..registerLazySingleton(() => GetUtilitiesUseCase(sl()))
    ..registerLazySingleton(() => SubmitOtpUtilityUseCase(sl()))
    ..registerLazySingleton(() => SubmitPinUtilityUseCase(sl()))

    // Repository
    ..registerLazySingleton<UtilityRepository>(
      () => UtilityRepositoryImpl(
        sl(),
      ),
    )

    // Data sources
    ..registerLazySingleton<UtilityRemoteDataSource>(
      () => UtilityRemoteDataSourceImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    );
}

//MiniApp
Future<void> _initMiniapp() async {
  sl
    // Bloc
    ..registerFactory(
      () => MiniappBloc(
        miniappcreateOrderUseCase: sl(),
        getMiniappsUseCase: sl(),
        submitOtpMiniappUseCase: sl(),
        submitPinMiniappUseCase: sl(),
      ),
    )
    // Use cases
    ..registerLazySingleton(() => MiniappCreateOrderUseCase(sl()))
    ..registerLazySingleton(() => GetMiniappsUseCase(sl()))
    ..registerLazySingleton(() => SubmitOtpMiniappUseCase(sl()))
    ..registerLazySingleton(() => SubmitPinMiniappUseCase(sl()))

    // Repository
    ..registerLazySingleton<MiniappRepository>(
      () => MiniappRepositoryImpl(
        sl(),
      ),
    )

    // Data sources
    ..registerLazySingleton<MiniappRemoteDataSource>(
      () => MiniappRemoteDataSourceImpl(
        dio: sl(),
        authLocalDataSource: sl(),
      ),
    );
  // ..registerLazySingleton<MiniappPaymentDataSource>(
  //   () => MiniappPaymentDataSourceImpl(
  //     dio: sl(),
  //     authLocalDataSource: sl(),
  //   ),
  // );
}

Future<void> _initQrParsing() async {
  sl
    ..registerLazySingleton(http.Client.new)
    ..registerLazySingleton<QrLocalDataSource>(
      () => QrLocalDataSourceImpl(
        hiveManager: sl(),
      ),
    )
    ..registerLazySingleton<QrRemoteDataSource>(
      () => QrRemoteDataSourceImpl(
        apiService: sl(),
      ),
    )
    ..registerLazySingleton<QrRepository>(
      () => QrRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
      ),
    )
    ..registerLazySingleton(() => ParseQrCodeUsecase(sl()))
    ..registerLazySingleton(() => GenerateQrCodeUsecase(sl()))
    ..registerFactory(
      () => ParseQrBloc(
        parseQrCodeUsecase: sl(),
        generateQrCodeUsecase: sl(),
      ),
    );
}

Future<void> _initIdentityVerification() async {
  sl
    // Bloc
    ..registerFactory(
      () => IdentityVerificationBloc(
        uploadDocumentUseCase: sl(),
      ),
    )

    // Use cases
    ..registerLazySingleton(() => UploadDocumentUseCase(sl()))

    // Repository
    ..registerLazySingleton<IdentityVerificationRepository>(
      () => IdentityVerificationRepositoryImpl(
        remoteDataSource: sl(),
      ),
    )

    // Data sources
    ..registerLazySingleton<IdentityVerificationRemoteDataSource>(
      () => IdentityVerificationRemoteDataSourceImpl(
        apiService: sl(),
      ),
    );
}

Future<void> _initMiniStatements() async {
  sl
    // Bloc
    ..registerFactory(
      () => MiniStatementBloc(
        generateMiniStatementUsecase: sl(),
      ),
    )

    // Use cases
    ..registerLazySingleton(() => GenerateMiniStatementUsecase(sl()))

    // Repository
    ..registerLazySingleton<MiniStatementRepository>(
      () => MiniStatementRepositoryImpl(
        remoteDataSource: sl(),
      ),
    )

    // Data sources
    ..registerLazySingleton<MiniStatementRemoteDataSource>(
      () => MiniStatementRemoteDataSourceImpl(
        apiService: sl(),
      ),
    );
}

// MyConnect
Future<void> _initMyConnect() async {
  // Data sources
  sl.registerLazySingleton<MyConnectRemoteDataSource>(
    () => MyConnectRemoteDataSourceImpl(
      apiService: sl(),
    ),
  );

  // Repositories
  sl.registerLazySingleton<MyConnectRepository>(
    () => MyConnectRepositoryImpl(sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => MyConnectSendRequestUseCase(sl()));
  sl.registerLazySingleton(() => MyConnectFetchConnectsUseCase(sl()));
  sl.registerLazySingleton(() => MyConnectAcceptRequestUseCase(sl()));

  // BLoC
  sl.registerFactory(
    () => MyConnectBloc(
      sendRequestUseCase: sl(),
      fetchConnectsUseCase: sl(),
      acceptRequestUseCase: sl(),
    ),
  );
}
