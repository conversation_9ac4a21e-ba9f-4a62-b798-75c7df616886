{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984fbb5e8ac21d3940187cbfd803a6e10b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c07a807df647e2e5f86a3555bebd468e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e82974096e1f8118580b89799f448bb6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9862c321359c6aa3133aa2d12fef3fa8a1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e82974096e1f8118580b89799f448bb6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cd02a0ae3dc869af483cdf186eca9111", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981ade1683f5947a064b2374bcf17e6b77", "guid": "bfdfe7dc352907fc980b868725387e989922e1a0d5b5af1b17a3257580dffb15", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986e38199cee37c395b09b6ea76ee346c6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c72ff202b52979a44136fa4581205ecf", "guid": "bfdfe7dc352907fc980b868725387e98455b982bd84d3f41bd8e6241583aebaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c197fe875d430aaeb4a1d08e8540546", "guid": "bfdfe7dc352907fc980b868725387e98fbf2d538fd713137c884005d32e4e3c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d971bb1995a9c21c3727938f574ba6a4", "guid": "bfdfe7dc352907fc980b868725387e9860036a3c42b1a335642ac362124d076f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985663d63d5f3f257a732a3f0d00c84cff", "guid": "bfdfe7dc352907fc980b868725387e985526787beee1d3b4a7b51ff675fad834"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838178000a9e4416ee14540b93a752c0b", "guid": "bfdfe7dc352907fc980b868725387e98f0936d8eb63b9dd105e9dc6c92d5ce67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8ed651aa87b6c705ef759be267e9ecc", "guid": "bfdfe7dc352907fc980b868725387e98677faa0b7221d197d42e8804a9249fbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885681f87c972d348d77e3d3272e185b9", "guid": "bfdfe7dc352907fc980b868725387e98c5c0615dc1b33157b01c21a523b8760a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817a335a0283e0c4bf51dc829e5863445", "guid": "bfdfe7dc352907fc980b868725387e98f6dcddf8cabe19885151cab19295e27b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b283df28fee7f5954384df1d0f74ec77", "guid": "bfdfe7dc352907fc980b868725387e98ba7ca7c86d8b35c5ec924e3376934260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8d098414106bf45dd350985dbba347b", "guid": "bfdfe7dc352907fc980b868725387e980074c3bb2dcf3b4067995c2804203ab4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d0669e2d3004ad7edbb0bd8a4a3bd5f", "guid": "bfdfe7dc352907fc980b868725387e98913b25d34e4a492f6d7fe4d0a8833922"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982343e523b69128a842155ef83c92dbec", "guid": "bfdfe7dc352907fc980b868725387e98229eca5517f521b7a843b53185477bab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d656dacca27057c3aef3f4f7ded056f", "guid": "bfdfe7dc352907fc980b868725387e98a7c60d33fe70b9e806e4c88791cb22b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db263c3aa13a8cd0315223fd3966122d", "guid": "bfdfe7dc352907fc980b868725387e9833b14decbae3895170662dfd7e31105c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806d308bf325eecf20a23ba6450e2fad4", "guid": "bfdfe7dc352907fc980b868725387e98b6ceae79174d6e95b9fcd6ee2a931ebe"}], "guid": "bfdfe7dc352907fc980b868725387e98e1dd260d5043de6ca17262772235c75b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e981cf99422f2396ccfdab650f429fb3a3b"}], "guid": "bfdfe7dc352907fc980b868725387e9817982674003190dcc2c76c558acbc80d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983082e0828474e3c48a4f5bc9400904f6", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98dc2b64301982105ed3a4461902781fa8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}