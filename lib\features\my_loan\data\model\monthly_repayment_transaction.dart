import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_transaction.dart';
import 'package:equatable/equatable.dart';
import 'package:cbrs/core/utils/app_mapper.dart';

// for success transaction
class MonthlyRepaymentTransactionModel extends MonthlyRepaymentTransactionEntity {
  const MonthlyRepaymentTransactionModel({
    required super.vat,
    required super.status,
    required super.elstRef,
    required super.bankCode,
    required super.bankName,
    required super.senderId,
    required super.billRefNo,
    required super.createdAt,
    required super.billAmount,
    required super.totalAmount,

    required super.connectRef,
    required super.senderName,
    required super.senderPhone,
    required super.beneficiaryId,
    required super.penaltyAmount,
    required super.serviceCharge,
    required super.interestAmount,
    required super.lastModifiedAt,
    required super.beneficiaryName,
    required super.facilitationFee,
    required super.transactionType,
    required super.transactionOwner,
    required super.originalCurrency,
    required super.authorizationType,
  });

  factory MonthlyRepaymentTransactionModel.fromJson(Map<String, dynamic> json) {
    return MonthlyRepaymentTransactionModel(
      vat: AppMapper.safeDouble(json['VAT']),
      status: AppMapper.safeString(json['status']),
      elstRef: AppMapper.safeString(json['ELSTRef']),
      bankCode: AppMapper.safeString(json['bankCode']),
      bankName: AppMapper.safeString(json['bankName']),
      senderId: AppMapper.safeString(json['senderId']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      createdAt: AppMapper.safeDateTime(json['createdAt']),
      billAmount: AppMapper.safeDouble(json['billAmount']),
      totalAmount: AppMapper.safeDouble(json['totalAmount']),

      connectRef: AppMapper.safeString(json['connectRef']),
      senderName: AppMapper.safeString(json['senderName']),
      senderPhone: AppMapper.safeString(json['senderPhone']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      penaltyAmount: AppMapper.safeDouble(json['penaltyAmount']),
      serviceCharge: AppMapper.safeDouble(json['serviceCharge']),
      interestAmount: AppMapper.safeDouble(json['interestAmount']),
      lastModifiedAt: AppMapper.safeDateTime(json['lastModifiedAt']),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      facilitationFee: AppMapper.safeDouble(json['facilitationFee']),
      transactionType: AppMapper.safeString(json['transactionType']),
      transactionOwner: AppMapper.safeString(json['TransactionOwner']),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      authorizationType: AppMapper.safeString(json['authorization_type']),
    );
  }


}
