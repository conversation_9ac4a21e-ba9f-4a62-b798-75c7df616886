import 'dart:io';

import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/extensions/string_extensions.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/domain/entities/user.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_event.dart';
import 'package:cbrs/features/profile/presentation/widgets/build_member_level.dart';
import 'package:cbrs/features/profile/presentation/widgets/show_image_source_bottomsheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/instance_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shimmer/shimmer.dart';

class UserProfileContainer extends StatefulWidget {
  const UserProfileContainer({required this.userDto, super.key});
  final LocalUser userDto;
  @override
  State<UserProfileContainer> createState() => _UserProfileContainerState();
}

class _UserProfileContainerState extends State<UserProfileContainer> {
  final bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();

  String avatarUrl = '';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    setState(() {
      avatarUrl = widget.userDto.avatar;
    });
  }

  Future<void> _handleDelete() async {
    try {
      context.read<ProfileBloc>().add(const DeleteAvatarEvent());
      Navigator.pop(context);
    } catch (e) {
      CustomToastification(
        context,
        message: 'Failed to update profile picture. Please try again later.',
      );
    } finally {}
  }

// untouch
  Future<void> _showImageSourceBottomSheet() async {
    final theme = Theme.of(context);
    final parentContext = context;
    await showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return HandleProfileBottomSheet.showImageSourceBottomSheet(
          context,
          avatarUrl: avatarUrl,
          onDelete: _handleDelete,
          onCameraSelected: () {
            Navigator.pop(context);
            _pickImage(ImageSource.camera);
          },
          onGallerySelected: () {
            Navigator.pop(context);
            _pickImage(ImageSource.gallery);
          },
        );
      },
    );
  }

// untouch
  Future<void> _pickImage(ImageSource source) async {
    // Initialize status with a default value
    var status = PermissionStatus.denied;
    var permissionMessage = 'Permission Denied';

    // Check permission based on source (camera or gallery)
    if (source == ImageSource.camera) {
      // Request Camera Permission
      status = await Permission.camera.request();
      permissionMessage = 'Camera permission is required to take photos';

      if (status.isDenied) {
        // Request permission again if it was initially denied
        status = await Permission.camera.request();
      }
    } else {
      // Gallery Permission (Photo Library)
      if (Platform.isAndroid) {
        // Android 13+ requires specific photo permission, not just storage
        if (await Permission.photos.status.isDenied) {
          status = await Permission.photos.request();
        } else {
          status = await Permission.photos.status;
        }

        // If photo permission is denied, fall back to storage permission for older versions
        if (status.isDenied) {
          status = await Permission.storage.request();
        }

        permissionMessage = 'Gallery permission is required to pick images';
      } else if (Platform.isIOS) {
        // iOS (photos library access)
        status = await Permission.photos.request();
        debugPrint('REQUESTING GALLERY PERMISSION ON IOS $status');

        // iOS-specific message
        permissionMessage = 'Photos permission is required to pick images';
      }
    }

    try {
      // Handle cases when permission is denied or permanently denied
      if (status.isDenied) {
        // Handle the denial here (e.g., show a message to the user)
        // print(permissionMessage);

        CustomToastification(context, message: permissionMessage);

        return;
      } else if (status.isPermanentlyDenied) {
        // Permission has been permanently denied, prompt the user to go to settings
        print(
          'Permission has been permanently denied. Please go to app settings and enable it.',
        );

        // Open app settings to allow the user to manually enable the permission
        await openAppSettings();
      } else {
        // Handle other cases (granted, etc.)
        print('Permission granted.');
      }

      if (status.isPermanentlyDenied) {
        if (!context.mounted) return;
        final shouldOpenSettings = await showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Permission Required'),
                content: Text(permissionMessage),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Open Settings'),
                  ),
                ],
              ),
            ) ??
            false;

        if (shouldOpenSettings as bool) {
          await openAppSettings();
        }
        return;
      }

      final image = await _picker.pickImage(
        source: source,
        imageQuality: 70,
      );

      debugPrint('avatar');
      if (image != null && context.mounted) {
        await _uploadImage(File(image.path));
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      if (context.mounted) {
        CustomToastification(
          context,
          message: 'Failed to pick image. Please try again.',
        );
      }
    }
  }

  // choose gallery method

  File? image;

  /// send image file to bloc
  Future<void> _uploadImage(File imageFile) async {
    try {
      context.read<ProfileBloc>().add(UpdateAvatarEvent(avatar: imageFile));
    } catch (e) {
      CustomToastification(
        context,
        message: 'Failed to update profile picture. Please try again later.',
      );
    } finally {}
  }

  Future<void> updateLocalAvatar(String imageUrl) async {
    final user = await sl<AuthLocalDataSource>().getCachedUserData();

    if (user != null) {
      debugPrint('unupdated avatar ${user.avatar}');
      final updatedUser = user.copyWith(avatar: imageUrl);

      debugPrint('Updated avatar ${updatedUser.avatar}');
      await sl<AuthLocalDataSource>().saveUserData(updatedUser);

      final newUser = await sl<AuthLocalDataSource>().getCachedUserData();
      debugPrint('✅ Updated local imageUrl: ${newUser?.avatar}');

    context.read<HomeBloc>().add(const HomeProfileFetchingEvent());

      // context.read<HomeBloc>().add(
      //       LoadEssentialDataEvent(
      //         walletType: GlobalVariable.currentlySelectedWallet,
      //       ),
      //     );
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final name = widget.userDto.fullName.split(' ');

    final profileTitle = name.length > 1
        ? "${name[0][0]}${name.last[0]}".toUpperCase()
        : name[0][0];
    final isEmailVerified = widget.userDto.isEmailVerified;
    final isPhoneVerified = widget.userDto.isPhoneVerified;

    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(vertical: 12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.04),
            blurRadius: 24,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              BlocConsumer<ProfileBloc, ProfileState>(
                listener: (context, state) {
                  if (state is UpdatedAvatarState) {
                    setState(() {
                      avatarUrl = state.imageUrl;
                    });
                    updateLocalAvatar(state.imageUrl);
                  }
                  if (state is DeleteAvatarState) {
                    setState(() {
                      avatarUrl = '';
                    });
                    updateLocalAvatar('');
                  }
                },
                builder: (context, state) {
                  return Container(
                    padding: const EdgeInsets.all(3),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                    child: state is ProfileLoading
                        ? Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: const CircleAvatar(radius: 50),
                          )
                        : ClipRRect(
                            borderRadius: BorderRadius.circular(50),
                            child: avatarUrl.isNotEmpty
                                ? GestureDetector(
                                    onTap: () {
                                      context.pushNamed(
                                        AppRouteName.profilePhoto,
                                        extra: {'photoUrl': avatarUrl},
                                      );
                                    },
                                    child: Hero(
                                      tag: avatarUrl,
                                      child: CustomCachedImage(
                                        url: avatarUrl,
                                        width: 100,
                                        height: 100,
                                      ),
                                    ),
                                  )
                                : Container(
                                    height: 100,
                                    width: 100,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient:
                                          Get.find<GetAppThemeController>()
                                                  .isBirrTheme
                                                  .value
                                              ? LightModeTheme().primaryGradient
                                              : LightModeTheme().usdGradient,
                                    ),
                                    child: Center(
                                      child: Text(
                                        profileTitle,
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                          ),
                  );
                },
              ),
              Positioned(
                right: 0,
                top: 0,
                child: GestureDetector(
                  onTap: _showImageSourceBottomSheet,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      // color: const Color(0xFF5B9B8D),
                      gradient:
                          Get.find<GetAppThemeController>().isBirrTheme.value
                              ? LightModeTheme().primaryGradient
                              : LightModeTheme().usdGradient,

                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            widget.userDto.fullName.toCapitalized(),
            style: GoogleFonts.outfit(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                isEmailVerified
                    ? (widget.userDto.email?.toLowerCase() ?? '')
                    : (widget.userDto.phoneNumber ?? ''),
                style: GoogleFonts.outfit(
                  fontSize: 14,
                  color: Colors.grey,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(
                width: 6,
              ),
              if (isEmailVerified)
                Icon(
                  Icons.verified,
                  color: Theme.of(context).primaryColor,
                  size: 15,
                )
              else
                isPhoneVerified
                    ? Icon(
                        Icons.verified,
                        color: Theme.of(context).primaryColor,
                        size: 15,
                      )
                    : const SizedBox.shrink(),
            ],
          ),

          SizedBox(height: 4,),

         BuildMemberLevel(memberLevel: widget.userDto.memberLevel.level,)
        ],
      ),
    );
  }
}
