import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';

class MyConnectBtnCard extends StatelessWidget {
  const MyConnectBtnCard({
    super.key,
    this.child,
    this.bgColor,
    this.textColor,
    this.borderColor,
    this.horizontalPadding,
    this.onTap,
    this.hasBorder = true,
    this.padding,
  });

  final Widget? child;
  final Color? bgColor;
  final Color? textColor;
  final Color? borderColor;
  final double? horizontalPadding;
  final EdgeInsetsGeometry? padding;
  final Function()? onTap;
  final bool hasBorder;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ??
          EdgeInsets.symmetric(
            horizontal: horizontalPadding ?? 12,
            vertical: 8,
          ),
      decoration: BoxDecoration(
        color: bgColor,
        border: hasBorder
            ? Border.all(color: borderColor ?? Theme.of(context).primaryColor)
            : null,
        borderRadius: BorderRadius.circular(32),
      ),
      child: InkWell(
        onTap: onTap,
        child: child ??
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                CustomBuildText(
                  text: 'Connect ',
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
      ),
    );
  }
}
