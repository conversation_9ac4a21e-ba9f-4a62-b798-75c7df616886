import 'package:cbrs/core/common/widgets/confirm/custom_transaction_confirm_screen.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_error_retry.dart';
import 'package:cbrs/core/common/widgets/custom_row_transaction.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/download_reciept_url.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_details_bloc.dart';
import 'package:cbrs/features/transactions/presentation/widgets/transaction_map_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';

class TransactionDetailsScreen extends StatefulWidget {
  const TransactionDetailsScreen({
    required this.transactionId,
    super.key,
  });
  final String transactionId;

  @override
  State<TransactionDetailsScreen> createState() =>
      _TransactionDetailsScreenState();
}

class _TransactionDetailsScreenState extends State<TransactionDetailsScreen>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadTransactionDetails();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _loadTransactionDetails();
    }
  }

  String _formatTransactionType(String type) {
    // Replace underscores with spaces
    final formatted = type.replaceAll('_', ' ');
    // Capitalize each word
    var words = formatted.split(' ');
    words = words
        .map(
          (word) => word.isNotEmpty
              ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
              : '',
        )
        .toList();
    return words.join(' ');
  }

  void _loadTransactionDetails() {
    context.read<TransactionDetailsBloc>().add(
          FetchTransactionDetailsEvent(widget.transactionId),
        );
  }

  Widget _buildShimmerEffect() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Column(
                children: [
                  // Amount Section
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Container(
                          width: 180,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Title
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Container(
                            width: 150,
                            height: 24,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Transaction Details Rows
                        for (int i = 0; i < 8; i++) ...[
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Row(
                              children: [
                                Container(
                                  width: 100,
                                  height: 16,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                const Spacer(),
                                if (i == 7) // Status pill for last item
                                  Container(
                                    width: 80,
                                    height: 32,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                  )
                                else
                                  Container(
                                    width: 140,
                                    height: 16,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          if (i < 7) // Add divider except for last item
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Container(
                                height: 1,
                                color: Colors.grey[200],
                              ),
                            ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          // Download Button Shimmer
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(32),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isRecieptLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transaction Details'),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<TransactionDetailsBloc, TransactionDetailsState>(
            listener: (context, state) async {
              if (state is TransactionReceiptLoaded) {
                setState(() {
                  _isRecieptLoading = false;
                });
                await HandleDownloadReciept.downloadReceipt(
                  context,
                  state.receiptUrl,
                );
              }

              if (state is TransactionDetailsError) {
                CustomToastification(context, message: state.message);
              }
            },
          ),
        ],
        child: BlocBuilder<TransactionDetailsBloc, TransactionDetailsState>(
          builder: (context, state) {
            if (state is TransactionDetailsLoading) {
              return _buildShimmerEffect();
            }

            if (state is TransactionDetailsError) {
              return CustomErrorRetry(
                errorMessage: state.message,
                onTap: _loadTransactionDetails,
              );
            }

            if (state is TransactionDetailsLoaded) {
              final transaction = state.transaction;
              final transactionMap = TransactionMapData(transaction);
              
              final transactionUI = transactionMap.toWidgetList();

              return Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: CustomTransactionConfirmScreen(
                        totalAmount: transaction.billAmount,
                        isUsdTransfer: transaction.originalCurrency == 'USD',
                        description: 'Transaction Amount',
                        customImage: Container(
                          width: 96.w,
                          height: 96.h,
                          padding: EdgeInsets.symmetric(
                            horizontal: 16.w,
                            vertical: 16.h,
                          ),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Theme.of(context).secondaryHeaderColor,
                          ),
                          child: Center(
                            child: Image.asset(
                              'assets/images/empty_transaction_screen_img.png',
                              width: 56.w,
                              height: 56.h,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                        child: Column(
                          children: [
                            ...transactionUI,
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 16.h),
                              child: CustomPaint(
                                painter: DottedLinePainter(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.4),
                                ),
                                size: const Size(double.infinity, 1),
                              ),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Total Amount',
                                  style: GoogleFonts.outfit(
                                    fontSize: 18.sp,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.black,
                                  ),
                                ),
                                Text(

                                  transactionMap.totalAmount,
                                  style: GoogleFonts.outfit(
                                    fontSize: 18.sp,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Container(
                    width: double.infinity,
                    padding:
                        EdgeInsets.symmetric(horizontal: 24.w, vertical: 10.h),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                    ),
                    child: SafeArea(
                      top: false,
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(32.r),
                                border: Border.all(
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              child: TextButton(
                                onPressed: () => context.go(AppRouteName.home),
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: 16.h),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(32.r),
                                  ),
                                ),
                                child: Text(
                                  'Back',
                                  style: GoogleFonts.outfit(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 16.w),
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                borderRadius: BorderRadius.circular(32.r),
                              ),
                              child: TextButton(
                                onPressed: _isRecieptLoading
                                    ? null
                                    : () {
                                        setState(() {
                                          _isRecieptLoading = true;
                                        });

                                        context
                                            .read<TransactionDetailsBloc>()
                                            .add(
                                              GetTransactionReceiptEvent(
                                                transaction.billRefNo,
                                              ),
                                            );
                                      },
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: 16.h),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(32.r),
                                  ),
                                ),
                                child: _isRecieptLoading
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                        ),
                                      )
                                    : Text(
                                        'Get Receipt',
                                        style: GoogleFonts.outfit(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.white,
                                        ),
                                      ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, bool isStatus) {
    return !isStatus
        ? CustomRowTransaction(
            label: label,
            value: value,
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.outfit(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 16),
              if (isStatus)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFFD4FECB),
                    borderRadius: BorderRadius.circular(22),
                  ),
                  child: CustomBuildText(
                    text: value,
                    style: GoogleFonts.outfit(
                      color: const Color(0xFF3EA100),
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
            ],
          );
  }
}
