import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/features/orders/application/blocs/order_bloc.dart';
import 'package:cbrs/features/orders/application/blocs/order_state.dart';
import 'package:cbrs/features/orders/presentation/widgets/sent_order_details_shimmer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cbrs/features/orders/application/blocs/order_event.dart';
import 'package:cbrs/core/utils/price_formatter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io' show Platform;

class SentOrderDetailsScreen extends StatefulWidget {
  const SentOrderDetailsScreen({super.key, required this.orderId});
  final String orderId;

  @override
  State<SentOrderDetailsScreen> createState() => _SentOrderDetailsScreenState();
}

class _SentOrderDetailsScreenState extends State<SentOrderDetailsScreen> {
  Future<void> _downloadReceipt(String url) async {
    debugPrint('=== Download Receipt Debug ===');
    debugPrint('URL: $url');

    try {
      final Uri uri = Uri.parse(url);
      debugPrint('URI parsed successfully: $uri');

      // For PDF files, we should use platform-specific handling
      if (Platform.isAndroid || Platform.isIOS) {
        final launched = await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
          webViewConfiguration: const WebViewConfiguration(
            enableJavaScript: true,
            enableDomStorage: true,
          ),
        );

        debugPrint('Launch attempt result: $launched');

        if (!launched) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not open PDF viewer'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        // For web or other platforms
        final launched = await launchUrl(uri);
        debugPrint('Launch attempt result: $launched');

        if (!launched) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not open download link'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error opening download link: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
    debugPrint('=== End Download Receipt Debug ===');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return BlocConsumer<OrderBloc, OrderState>(
      listenWhen: (previous, current) {
        debugPrint('=== ListenWhen Debug ===');
        debugPrint('Previous state: $previous');
        debugPrint('Current state: $current');
        final shouldListen =
            current is OrderReceiptLoaded || current is OrderError;
        debugPrint('Should listen: $shouldListen');
        debugPrint('=== End ListenWhen Debug ===');
        return shouldListen;
      },
      listener: (context, state) {
        debugPrint('=== Listener Debug ===');
        debugPrint('Current state in listener: $state');
        if (state is OrderReceiptLoaded) {
          debugPrint('OrderReceiptLoaded detected, URL: ${state.receiptUrl}');
          _downloadReceipt(state.receiptUrl);
        } else if (state is OrderError) {
          debugPrint('OrderError detected: ${state.message}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        debugPrint('=== End Listener Debug ===');
      },
      builder: (context, state) {
        if (state is OrderDetailLoading) {
          return const SentOrderDetailsShimmer();
        }

        if (state is OrderDetailLoaded) {
          final orderDetail = state.orderDetail;
          final giftPackage = orderDetail.giftPackage;

          return Scaffold(
            backgroundColor: Colors.grey[50],
            appBar: AppBar(
              title: const Text(
                'Order Details',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
              ),
            ),
            body: SafeArea(
              child: Column(
                children: [
                  const SizedBox(height: 12),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Package Information',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Package Card
                            Container(
                              padding: EdgeInsets.all(
                                  MediaQuery.of(context).size.width *
                                      0.02), // 2% padding
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  final maxWidth = constraints.maxWidth;
                                  final imageSize = (maxWidth * 0.25).clamp(
                                    80.0,
                                    100.0,
                                  ); // 25% of width, clamped between 80-100

                                  return Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        padding:
                                            EdgeInsets.all(maxWidth * 0.01),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[50],
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: CachedNetworkImage(
                                            imageUrl: orderDetail
                                                .giftPackage!.featureImage,
                                            width: imageSize,
                                            height: imageSize,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) =>
                                                Container(
                                              color: Colors.grey[200],
                                              child: Shimmer.fromColors(
                                                baseColor: Colors.grey[300]!,
                                                highlightColor:
                                                    Colors.grey[100]!,
                                                child: Container(
                                                  width: imageSize,
                                                  height: imageSize,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            errorWidget:
                                                (context, url, error) =>
                                                    Container(
                                              color: Colors.grey[200],
                                              child: const Icon(Icons.error),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                          width: maxWidth * 0.03), // 3% spacing
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              // '\$${orderDetail.billAmount}',
                                              PriceFormatter.formatPrice(
                                                  orderDetail.billAmount
                                                      .toString()),
                                              style: TextStyle(
                                                fontSize: (maxWidth * 0.045).clamp(
                                                    14.0,
                                                    18.0), // Responsive font size
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                            Text(
                                              orderDetail.giftPackage!.name,
                                              style: TextStyle(
                                                fontSize: (maxWidth * 0.045)
                                                    .clamp(14.0, 18.0),
                                                fontWeight: FontWeight.w700,
                                              ),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            Text(
                                              orderDetail
                                                  .giftPackage!.category.name,
                                              style: TextStyle(
                                                color: Colors.grey,
                                                fontSize: (maxWidth * 0.035)
                                                    .clamp(12.0, 14.0),
                                                fontWeight: FontWeight.w400,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            SizedBox(
                                                height: maxWidth *
                                                    0.02), // 2% spacing
                                            Wrap(
                                              crossAxisAlignment:
                                                  WrapCrossAlignment.center,
                                              children: [
                                                Text(
                                                  'Purchased at: ',
                                                  style: TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: (maxWidth * 0.035)
                                                        .clamp(12.0, 14.0),
                                                  ),
                                                ),
                                                Text(
                                                  DateFormat('MMM dd, yyyy')
                                                      .format(orderDetail
                                                          .createdAt),
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                    fontSize: (maxWidth * 0.035)
                                                        .clamp(12.0, 14.0),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Payment Details
                            const Text(
                              'Payment Details',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const SizedBox(height: 16),

                            _buildDetailRow(
                                'Order Code:',
                                orderDetail.orderId ??
                                    orderDetail.billRefNo ??
                                    'N/A'),
                            _buildDetailRow(
                                'Customer name:', orderDetail.senderName),
                            _buildDetailRow(
                                'Recipient name:', orderDetail.beneficiaryName),
                            if (giftPackage?.merchant != null)
                              _buildDetailRow(
                                  'Merchant:', giftPackage!.merchant.name),
                            _buildDetailRow('Recipient Phone:',
                                orderDetail.beneficiaryPhone ?? 'N/A'),
                            _buildDetailRow(
                                'Package name:', giftPackage?.name ?? 'N/A'),
                            if (orderDetail.FTNumber != null)
                              _buildDetailRow(
                                  'Payment reference:', orderDetail.FTNumber!),
                            _buildDetailRow(
                              'Amount:',
                              // '${orderDetail.originalCurrency} ${orderDetail.billAmount.toStringAsFixed(2)}',

                              PriceFormatter.formatPrice(
                                  orderDetail.billAmount.toStringAsFixed(2)),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Delivery Status:',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 16,
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: orderDetail.status == 'COMPLETED'
                                        ? LightModeTheme()
                                            .primaryColorUSD
                                            .withOpacity(0.2)
                                        : const Color(0xFFFFF1C2),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    orderDetail.status == 'COMPLETED'
                                        ? 'Redeemed'
                                        : 'Ready to Pickup',
                                    style: TextStyle(
                                      color: orderDetail.status == 'COMPLETED'
                                          ? LightModeTheme().primaryColorUSD
                                          : Color(0xFFDD9000),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 14),
                            Divider(
                              color: Colors.grey.shade200,
                            ),
                            const SizedBox(height: 10),

                            // Total
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Total',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Text(
                                  // '\$${orderDetail.billAmount.toStringAsFixed(2)}',
                                  PriceFormatter.formatPrice(orderDetail
                                      .billAmount
                                      .toStringAsFixed(2)),
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: state is OrderReceiptLoading ||
                                orderDetail.billRefNo == null
                            ? null
                            : () {
                                debugPrint(
                                    'Getting receipt for billRefNo: ${orderDetail.billRefNo}');
                                context.read<OrderBloc>().add(
                                      GetOrderReceiptEvent(
                                          orderDetail.billRefNo!),
                                    );
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: LightModeTheme().primaryColorUSD,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child: state is OrderReceiptLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : Text(
                                state is OrderReceiptLoaded
                                    ? 'Download Again'
                                    : 'Get Receipt',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        if (state is OrderDetailError) {
          return Scaffold(
            body: Center(child: Text(state.message)),
          );
        }

        return const Scaffold(
          body: Center(child: CircularProgressIndicator()),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.grey,
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}
