import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_recent_transaction_bloc.dart';
import 'package:cbrs/features/send_money/domain/entities/bank.dart';
import 'package:cbrs/features/send_money/domain/entities/bank_transfer_response.dart';
import 'package:cbrs/features/send_money/domain/entities/recent_transaction_hive.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class OtherBankAddAmountScreen extends StatefulWidget {
  const OtherBankAddAmountScreen({
    required this.senderName,
    required this.recipientName,
    required this.recipientAccount,
    required this.isBirrTransfer,
    required this.bank,
    this.exchangeRate,
    super.key,
  });

  final String senderName;
  final String recipientName;
  final String recipientAccount;
  final bool isBirrTransfer;
  final Bank bank;
  final double? exchangeRate;

  @override
  State<OtherBankAddAmountScreen> createState() =>
      _OtherBankAddAmountScreenState();
}

class _OtherBankAddAmountScreenState extends State<OtherBankAddAmountScreen> {
  late final CurrencyInputController _currencyController;
  bool _isLoading = false;
  final TextEditingController _pinController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;
  double walletBalance = 0;

  final bool _ignoreWalletAmountCheck = true;
  @override
  void initState() {
    super.initState();

    _currencyController = CurrencyInputController(
      currencyType: widget.isBirrTransfer ? CurrencyType.etb : CurrencyType.usd,
      maxBalance: 0,
      exchangeRate: widget.exchangeRate,
      ignoreWalletAmountCheck: _ignoreWalletAmountCheck,
    );

    context.read<BankTransferBloc>().add(const GetWalletDetailsEvent());

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.bankTransfer,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        context.read<TransactionBloc>().add(
              ConfirmTransferEvent(
                pin: pin,
                billRefNo: (context.read<BankTransferBloc>().state
                        as BankTransferPinRequired)
                    .response!
                    .billRefNo,
                transactionType: tx_type.TransactionType.bankTransfer,
              ),
            );
      },
      onTransactionSuccess: (response) {
        final recentTransaction = RecentTransactionHive(
          bankId: response.transaction.bankName ?? '',
          accountNumber: widget.recipientAccount,
          recipientName: widget.recipientName,
          createdAt: DateTime.now(),
        );

        context.read<BankTransferRecentTransactionBloc>().add(
              SaveRecentTransactionsEvent(
                transaction: recentTransaction,
              ),
            );

        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
      },
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  void _onContinue() {
    final amount = _currencyController.numericAmount;

    if (amount <= 0) {
      CustomToastification(
        context,
        message: 'Amount must be greater than 0',
      );
      return;
    }

    context.read<BankTransferBloc>().add(
          CheckTransferRulesEvent(
            amount: amount,
            currency: widget.isBirrTransfer ? 'ETB' : 'USD',
            productType: 'bank_transfer',
          ),
        );
  }

  void _showConfirmScreenBottomSheet(BankTransferResponse response) {
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: <String, dynamic>{
        'Transaction Type': 'Bank Transfer',
        'Amount in USD': response.billAmount,
        'Amount in ETB': response.paidAmount,
        if (response.originalCurrency == 'USD')
          'Exchange Rate': '\$1 =${response.exchangeRate} ETB',
        'Beneficiary Name': response.beneficiaryName,
        'Beneficiary Account No': response.beneficiaryAccountNo,
        'Bank Name': response.bankName,
        'BillRefNo': response.billRefNo,
        'Date': AppMapper.safeFormattedDate(response.createdAt),
        'Service Charge':
            '${response.serviceCharge} ${response.originalCurrency}',
        'VAT': '${response.vat} ${response.originalCurrency}',
      },
      requiresOtp: response.requiresOtp,
      billRefNo: response.billRefNo,
      billAmount: response.billAmount,
      totalAmount: response.totalAmount,
      originalCurrency: response.originalCurrency,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
        originalCurrency: transaction.originalCurrency,
        totalAmount: transaction.totalAmount,
        billAmount: transaction.billAmount,
        status: transaction.status,
        transactionId: transaction.id,
        billRefNo: transaction.billRefNo,
        {
          'Beneficiary Name':
              transaction.beneficiaryName ?? widget.recipientName,
          'Beneficiary  Account No':
              transaction.beneficiaryAccountNo ?? widget.recipientAccount,
          if (response.transaction.originalCurrency == 'USD')
            'Exchange Rate': '\$1 =${transaction.exchangeRate} ETB',
          'Bank Name': transaction.bankName ?? widget.bank.name,
          'BillRefNo': transaction.billRefNo,
          'Date': AppMapper.safeFormattedDate(transaction.createdAt),
          'Service Charge':
              '${transaction.serviceCharge} ${transaction.originalCurrency}',
          'VAT': '${transaction.vat} ${transaction.originalCurrency}',
          'Amount': '${transaction.billAmount} ${transaction.originalCurrency}',
        });
  }

  @override
  Widget build(
    BuildContext context,
  ) {
    return BlocConsumer<BankTransferBloc, BankTransferState>(
      listenWhen: (previous, current) =>
          current is TransferRulesChecked ||
          current is BankTransferLoading ||
          current is BankTransferPinRequired ||
          current is WalletBalanceLoadingState ||
          current is WalletDetailsLoaded ||
          current is BankTransferError,
      listener: (context, state) {
        if (state is BankTransferLoading) {
          setState(() => _isLoading = true);
        } else if (state is BankTransferError) {
          setState(() => _isLoading = false);
          CustomToastification(
            context,
            message: state.message,
          );
        } else if (state is TransferRulesChecked) {
          setState(() => _isLoading = false);
          debugPrint('state is TransferRulesChecked');
          debugPrint(state.rules.toString());
          context.read<BankTransferBloc>().add(
                TransferToBankEvent(
                  beneficiaryAccountNo: widget.recipientAccount,
                  beneficiaryName: widget.recipientName,
                  amount: _currencyController.numericAmount,
                  currency: widget.isBirrTransfer ? 'ETB' : 'USD',
                  bankName: widget.bank.name,
                  bankCode: widget.bank.etSwitchCode,
                  senderName: widget.senderName,
                ),
              );
        } else if (state is BankTransferPinRequired) {
          setState(() => _isLoading = false);
          _showConfirmScreenBottomSheet(state.response!);
        } else if (state is BankTransferError) {
          setState(() => _isLoading = false);
          CustomToastification(context, message: state.message);
        }
        if (state is WalletDetailsLoaded) {
          setState(() {
            walletBalance = widget.isBirrTransfer
                ? state.wallet.etbBalance
                : state.wallet.usdBalance;
          });
        }
      },
      buildWhen: (previous, current) =>
          current is BankTransferInitial ||
          current is BankTransferLoading ||
          current is WalletBalanceLoadingState ||
          current is WalletDetailsLoaded,
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Bank Transfer'),
          ),
          body: SafeArea(
            bottom: false,
            child: MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (context) => sl<BankTransferRecentTransactionBloc>(),
                ),
              ],
              child: BlocListener<TransactionBloc, TransactionState>(
                listenWhen: (previous, current) =>
                    current is ConfirmTransferError,
                listener: (context, state) {
                  if (state is ConfirmTransferError) {
                    CustomToastification(
                      context,
                      message: state.message,
                    );
                  }
                },
                child: state is WalletBalanceLoadingState
                    ? const CustomConnectLoader()
                    : CurrencyInputWidget(
                        controller: _currencyController,
                        title: widget.isBirrTransfer
                            ? 'Add Birr Amount'
                            : 'Add Amount',
                        subtitle:
                            'Enter the amount you wish to send to the recipient and submit.',
                        balanceLabel: widget.isBirrTransfer
                            ? '${AppMapper.safeFormattedNumberWithDecimal(walletBalance)} ETB'
                            : '\$${walletBalance.toStringAsFixed(2)}',
                        onContinue: _onContinue,
                        isLoading: _isLoading,
                        transactionType:
                            tx_type.TransactionType.bankTransfer.value,
                        showExchangeAmount: !widget.isBirrTransfer,
                        exchangeAmount: widget.exchangeRate,
                      ),
              ),
            ),
          ),
        );
      },
    );
  }
}
