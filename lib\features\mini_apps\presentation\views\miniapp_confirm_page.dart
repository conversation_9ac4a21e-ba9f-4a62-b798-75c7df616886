import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/mini_apps/domain/entities/create_order_miniapp_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart';
import 'package:intl/intl.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_row_transaction.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_transaction_confirm_screen.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/common/widgets/custom_pin_screen.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/mini_apps/application/bloc/miniapp_bloc.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp_transaction.dart';
import 'package:cbrs/features/mini_apps/presentation/views/miniapp_success_screen.dart';

class MiniappConfirmationScreen extends StatefulWidget {
  const MiniappConfirmationScreen({
    required this.data,
    super.key,
  });
  final CreateOrderMiniappDataEntity data;

  @override
  State<MiniappConfirmationScreen> createState() =>
      _MiniappConfirmationScreenState();
}

class _MiniappConfirmationScreenState extends State<MiniappConfirmationScreen> {
  final bool _isLoading = false;
  final TextEditingController _pinController = TextEditingController();
  bool _isCheckingPin = false;
  MiniappTransaction? _transaction;

  void _onConfirmPayment() {
    /*
    final cleanAmount = widget.amount;
    final numericAmount = double.tryParse(cleanAmount) ?? 0.0;
    debugPrint('sASasAS');

    final request = <String, dynamic>{
      'billRefNo': widget.billRefNo,
      'transactionType': widget.transactionType,
    };

*/
    // context.read<MiniappBloc>().add(
    //       ProcessMiniappPaymentEvent(request: request),
    //     );

    debugPrint('authorization  ${widget.data.authorizationType}');
    if (widget.data.authorizationType == 'PIN') {
      _handleSubmitPin(widget.data.billRefNo ?? '');
    }
    // context.read<MiniappBloc>().add(
    //       SubmitPinMiniappEvent(
    //         transactionType: widget.data.transactionType ?? '',
    //         billRefNo: widget.data.billRefNo ?? '',
    //         pin: 'UAT',
    //       ),
    //     );
  }

  Future<void> _resetPinState(StateSetter setModalState) async {
    setModalState(() {
      _isCheckingPin = false;
      _pinController.clear();
    });
  }

  void _handleSubmitPin(String billRefNo) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => BlocProvider(
        create: (context) => sl<MiniappBloc>(),
        child: StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return BlocConsumer<MiniappBloc, MiniappState>(
              listener: (context, state) {
                if (state is MiniappErrorState) {
                  _resetPinState(setModalState);
                  CustomToastification(context, message: state.message);
                } else if (state is PinSubmittedState) {
                  Navigator.pop(context);

                  debugPrint('state.miniapp ${state.miniapp}  ');
                  context.pushNamed(
                    AppRouteName.miniappSuccess,
                    extra: {
                      'success': state.miniapp,
                    },
                  );
                }
              },
              builder: (context, state) => CustomPinScreen(
                isLoading: _isCheckingPin,
                controller: _pinController,
                onSubmitted: (value) async {
                  if (_isCheckingPin) return;

                  setModalState(() => _isCheckingPin = true);

                  context.read<MiniappBloc>().add(
                    SubmitPinMiniappEvent(
                      pin: value,
                      billRefNo: billRefNo,
                      transactionType: widget.data.transactionType ?? '',
                    ),
                  );
                },
                onChanged: (keys, isKey) {
                  if (_isCheckingPin) return;

                  setModalState(() {
                    if (!isKey) {
                      _pinController.text = _pinController.text.isNotEmpty
                          ? _pinController.text
                          .substring(0, _pinController.text.length - 1)
                          : '';
                      _pinController.selection = TextSelection.fromPosition(
                        TextPosition(offset: _pinController.text.length),
                      );
                    }
                    _pinController.text = "${_pinController.text}$keys";
                    _pinController.selection = TextSelection.fromPosition(
                      TextPosition(offset: _pinController.text.length),
                    );
                  });
                },
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeColor = Theme.of(context).primaryColor;

    return BlocConsumer<MiniappBloc, MiniappState>(
      listener: (context, state) {},
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            title: Text(
              '${widget.data.beneficiaryName} Payment',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding:
                    EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const CustomPageHeader(
                          pageTitle: 'Confirm Payment',
                          description:
                          'Review the payment details before confirming your utility payment.',
                        ),
                        SizedBox(height: 24.h),
                        CustomTransactionConfirmScreen(
                          totalAmount:
                          double.tryParse(widget.data.billAmount ?? '0') ??
                              0.0,
                          child: Column(
                            children: [
                              CustomRowTransaction(
                                label: 'Transaction Type ',
                                value: widget.data.merchantType,
                              ),
                              // const CustomRowTransaction(
                              //   label: 'Service Provider',
                              //   value: ' widget.data.serviceProvider',
                              // ),
                              CustomRowTransaction(
                                label: 'Customer Name',
                                value: widget.data.senderName,
                              ),
                              CustomRowTransaction(
                                label: 'Recipient Name',
                                value: widget.data.beneficiaryName,
                              ),
                              CustomRowTransaction(
                                label: 'Recipient Account',
                                value: widget.data.beneficiaryAccountNo,
                              ),
                              if (widget.data.billRefNo != null)
                                CustomRowTransaction(
                                  label: 'Reference Number',
                                  value: widget.data.billRefNo,
                                ),
                              CustomRowTransaction(
                                label: 'Date',
                                value: widget.data.lastModified,
                              ),
                              CustomRowTransaction(
                                label: 'Amount',
                                value: widget.data.billAmount,
                              ),
                              CustomRowTransaction(
                                label: 'ServiceFee',
                                value: widget.data.serviceCharge,
                              ),
                              CustomRowTransaction(
                                label: 'Vat',
                                value: widget.data.vat,
                              ),
                              // CustomRowTransaction(
                              //   label: 'Exchange Rate',
                              //   value:
                              //       '1 USD = ${widget.exchangeRate.toStringAsFixed(2)} ETB',
                              // ),
                              // CustomRowTransaction(
                              //   label: 'Amount in USD',
                              //   value: formattedUsdAmount,
                              // ),

                              CustomRowTransaction(
                                label: 'Total',
                                value: widget.data.billAmount,
                              ),
                            ],
                          ),
                        ),
                        // _buildTransactionDetails(),
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      offset: const Offset(0, -2),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: CustomRoundedBtn(
                    btnText: 'Confirm',
                    onTap: () {
                      if (widget.data.authorizationType == 'PIN') {
                        _handleSubmitPin(widget.data.billRefNo ?? '');
                      }
                    },

                    //_onConfirmPayment,
                    isLoading: state is MiniappLoadingState,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
