import 'dart:io';
import 'package:cbrs/core/common/widgets/custom_menu_screen_cards.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/domain/entities/user.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/identity_verification_bloc.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/identity_verification_state.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/identity_verification_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class IdentityVerificationPage extends StatefulWidget {
  const IdentityVerificationPage({super.key});

  @override
  State<IdentityVerificationPage> createState() =>
      _IdentityVerificationPageState();
}

class _IdentityVerificationPageState extends State<IdentityVerificationPage> {
  bool _isSelfieCompleted = false;
  bool _isIdScanCompleted = false;
  File? _selfieImage;
  File? _frontIdImage;
  File? _backIdImage;

  Future<void> updateLocalAvatar() async {
    final user = await sl<AuthLocalDataSource>().getCachedUserData();

    if (user != null) {
      debugPrint('unupdated avatar ${user.avatar}');
      final updatedUser = user.copyWith(
        memberLevel: MemberLevel(
          level: user.memberLevel.level,
          levelStatus: 'inReview',
        ),
      );

      debugPrint('Updated avatar ${updatedUser.avatar}');
      await sl<AuthLocalDataSource>().saveUserData(updatedUser);

      context.read<HomeBloc>().add(const HomeProfileFetchingEvent());
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Upgrade Account',
        ),
      ),
      body: SafeArea(
        child:
            BlocListener<IdentityVerificationBloc, IdentityVerificationState>(
          listener: (context, state) {
            if (state is IdentityVerificationSuccess) {
              // Handle successful upload
              CustomToastification(
                context,
                message: 'Documents uploaded successfully!',
                isError: false,
              );
              updateLocalAvatar();

              // Navigate back or to success page
              Navigator.pop(context);
            } else if (state is IdentityVerificationError) {
              CustomToastification(
                context,
                message: state.message,
              );
            }
          },
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const CustomPageHeader(
                          pageTitle: 'Upgrade your Account',
                          description:
                              'Choose an option to upgrade your wallet.',
                        ),
                        SizedBox(height: 24.h),
                        CustomMenuScreenCards(
                          onTap: _handleSelfieCapture,
                          title: 'Take Selfie',
                          description:
                              'Take a selfie for identity verification',
                          containerIcon: MediaRes.idCardIcon,
                          trailingWidget: Icon(
                            Icons.check_circle,
                            color: _isSelfieCompleted
                                ? Colors.green
                                : Colors.grey[500],
                            size: 24.sp,
                          ),
                        ),
                        SizedBox(height: 16.h),
                        CustomMenuScreenCards(
                          onTap: _handleIdScanning,
                          title: 'Scan Your ID',
                          description:
                              'Scan your identification card using camera',
                          containerIcon: MediaRes.idCardIcon,
                          trailingWidget: Icon(
                            Icons.check_circle,
                            color: _isIdScanCompleted
                                ? Colors.green
                                : Colors.grey[500],
                            size: 24.sp,
                          ),
                        ),
                        SizedBox(height: 400.h),
                      ],
                    ),
                  ),
                ),
              ),
              // Upload Documents Button - only show when both steps are completed
              if (_isSelfieCompleted && _isIdScanCompleted)
                Padding(
                  padding: EdgeInsets.fromLTRB(20.w, 16.h, 20.w, 24.h),
                  child: BlocBuilder<IdentityVerificationBloc,
                      IdentityVerificationState>(
                    builder: (context, state) {
                      return CustomRoundedBtn(
                        btnText: 'Upload Documents',
                        onTap: _handleUploadDocuments,
                        isLoading: state is IdentityVerificationLoading,
                      );
                    },
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleSelfieCapture() async {
    final result = await context.pushNamed(AppRouteName.faceScanCamera);
    if (result is File) {
      setState(() {
        _selfieImage = result;
        _isSelfieCompleted = true;
      });
    }
  }

  Future<void> _handleIdScanning() async {
    if (_selfieImage == null) {
      CustomToastification(
        context,
        message: 'Please complete selfie capture first',
      );
      return;
    }

    final result = await context.pushNamed(
      AppRouteName.documentScan,
      extra: _selfieImage,
    );

    if (result is Map<String, dynamic>) {
      final frontImage = result['frontImage'] as File?;
      final backImage = result['backImage'] as File?;

      if (frontImage != null && backImage != null) {
        setState(() {
          _frontIdImage = frontImage;
          _backIdImage = backImage;
          _isIdScanCompleted = true;
        });
      }
    }
  }

  void _handleUploadDocuments() {
    if (_selfieImage != null && _frontIdImage != null && _backIdImage != null) {
      context.read<IdentityVerificationBloc>().add(
            UploadDocumentEvent(
              frontPhoto: _frontIdImage!,
              backPhoto: _backIdImage!,
              selfiePhoto: _selfieImage!,
            ),
          );
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Listen for navigation results to update completion states
    _checkNavigationResults();
  }

  void _checkNavigationResults() {
    // This method will be called to check if we returned from camera pages
    // with captured images and update the completion states accordingly
    // Implementation will depend on how the navigation results are passed back
  }
}
