import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_list_response_entity.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';

abstract class MyConnectRepository {
  ResultFuture<ConnectionRequestEntity> sendConnectionRequest({
    required String recipientId,
    required String recipientName,
    String? recipientEmail,
    required String recipientPhone,
    String? recipientAvatar,
  });

  ResultFuture<ConnectionListResponseEntity> getConnections({
    String? status,
    String? scope,
    int? page,
    int? limit,
  });

  ResultFuture<void> acceptConnectionRequest(String requestId);

  ResultFuture<void> rejectConnectionRequest(String requestId);
}
