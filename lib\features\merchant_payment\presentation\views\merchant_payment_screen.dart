import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_bloc.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_event.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_state.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';

import 'package:shimmer/shimmer.dart';

class MerchantPaymentScreen extends StatefulWidget {
  const MerchantPaymentScreen({super.key});

  @override
  State<MerchantPaymentScreen> createState() => _MerchantPaymentScreenState();
}

class _MerchantPaymentScreenState extends State<MerchantPaymentScreen> {
  final TextEditingController _controller = TextEditingController();
  Timer? _debounce;

  // Add this to track if button should be enabled
  bool get _isButtonEnabled {
    return _controller.text.isNotEmpty;
  }

  @override
  void initState() {
    super.initState();
    // Reset merchant state when screen initializes
    context.read<MerchantBloc>().add(ResetMerchantState());

    // Add listener to controller to update button state
    _controller.addListener(() {
      setState(() {
        // Reset merchant state when the text changes
        context.read<MerchantBloc>().add(ResetMerchantState());
      });
    });
  }

  String get _buttonText {
    final state = context.read<MerchantBloc>().state;
    return state is MerchantLoaded ? 'Continue' : 'Check Account';
  }

  Future<void> _handleQRScan() async {
    final result = await context.pushNamed(AppRouteName.merchantPaymentByQr);
    if (result != null && mounted) {
      setState(() {
        _controller.text = result.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<MerchantBloc, MerchantState>(
      listener: (context, state) {
        // Force rebuild to update button state when merchant state changes
        setState(() {});
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Merchant Payment'),
        ),
        body: SafeArea(
          bottom: false,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              children: [
                // Header

                // Main Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const CustomPageHeader(
                        pageTitle: 'Merchant Payment',
                        description:
                            'Please enter the merchant ID or scan their QR code & make your payment quickly.',
                      ),
                      SizedBox(height: 16.h),
                      const CustomBuildText(
                        text: 'Merchant ID Number',
                        caseType: '',
                      ),
                      SizedBox(height: 4.h),
                      Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFF8F8F8),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _controller,
                                keyboardType: TextInputType.number,
                                onChanged: (value) {
                                  setState(() {
                                    // Reset merchant state when the text changes
                                    context
                                        .read<MerchantBloc>()
                                        .add(ResetMerchantState());
                                  });
                                },
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                  errorBorder: InputBorder.none,
                                  focusedErrorBorder: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 8.w,
                                    vertical: 7.h,
                                  ),
                                  hintText: '00000000000000',
                                  hintStyle:
                                      const TextStyle(color: Colors.grey),
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: _handleQRScan,
                              child: Image.asset(
                                MediaRes.qrIconFinder,
                                color: Theme.of(context).primaryColor,
                                width: 36,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 4.h),
                      BlocBuilder<MerchantBloc, MerchantState>(
                        builder: (context, state) {
                          if (state is MerchantLoading) {
                            return Container(
                              margin: EdgeInsets.only(bottom: 24.h),
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  padding: EdgeInsets.all(14.w),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.4),
                                    ),
                                    borderRadius: BorderRadius.circular(12.r),
                                  ),
                                  child: Row(
                                    children: [
                                      CircleAvatar(
                                        backgroundColor: Colors.white,
                                        radius: 24.r,
                                      ),
                                      SizedBox(width: 12.w),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: 150.w,
                                              height: 16.h,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                            ),
                                            SizedBox(height: 8.h),
                                            Container(
                                              width: 120.w,
                                              height: 14.h,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          } else if (state is MerchantLoaded) {
                            return GestureDetector(
                              onTap: () {
                                final state = context.read<MerchantBloc>().state
                                    as MerchantLoaded;
                                final walletBalance = 0.0;

                                if (state.merchant.code.isEmpty ||
                                    state.merchant.name.isEmpty) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Invalid merchant information',
                                        style: GoogleFonts.outfit(),
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                context.pushNamed(
                                  AppRouteName.merchantPaymentAddAmount,
                                  extra: {
                                    'merchantId': state.merchant.id,
                                    'merchantName': state.merchant.name,
                                    // 'walletBalance': walletBalance,
                                    'merchantCode': _controller.text,
                                  },
                                );
                              },
                              child: RecipientCard(
                                name: state.merchant.name,
                                accountNumber:
                                    'Merchant ID: ${state.merchant.code}',
                                isBirrTransfer: true,
                              ),

                              /*
                              
                              Container(
                                margin: EdgeInsets.only(bottom: 24.h),
                                padding: EdgeInsets.all(14.w),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withOpacity(0.4),
                                  ),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      backgroundColor:
                                          Theme.of(context).primaryColor,
                                      radius: 24.r,
                                      child: Text(
                                        state.merchant.name
                                            .substring(0, 2)
                                            .toUpperCase(),
                                        style: GoogleFonts.outfit(
                                          color: Colors.white,
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 12.w),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            state.merchant.name,
                                            style: GoogleFonts.outfit(
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          SizedBox(height: 4.h),
                                          Text(
                                            'Merchant ID: ${state.merchant.code}',
                                            style: GoogleFonts.outfit(
                                              fontSize: 14.sp,
                                              color: Theme.of(context)
                                                  .primaryColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          
                          */
                            );
                          } else if (state is MerchantError) {
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                vertical: 16.h,
                                horizontal: 4.w,
                              ),
                              child: Text(
                                'No Merchant found with this merchant ID. Please check and try again.',
                                style: GoogleFonts.outfit(
                                  fontSize: 14.sp,
                                  color: Colors.red,
                                ),
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                      const Spacer(),
                      Padding(
                        padding: EdgeInsets.only(bottom: 8),
                        child: CustomRoundedBtn(
                          btnText: _buttonText,
                          onTap: _isButtonEnabled
                              ? () {
                                  final state =
                                      context.read<MerchantBloc>().state;
                                  if (state is MerchantLoaded) {
                                    if (state.merchant.code.isEmpty ||
                                        state.merchant.name.isEmpty) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'Invalid merchant information',
                                            style: GoogleFonts.outfit(),
                                          ),
                                          backgroundColor: Colors.red,
                                        ),
                                      );
                                      return;
                                    }

                                    context.pushNamed(
                                      AppRouteName.merchantPaymentAddAmount,
                                      extra: {
                                        'merchantId': state.merchant.id,
                                        'merchantName': state.merchant.name,
                                        'merchantCode': _controller.text,
                                      },
                                    );
                                  } else {
                                    context.read<MerchantBloc>().add(
                                          FetchMerchantByTillNumber(
                                            tillNumber: _controller.text,
                                          ),
                                        );
                                  }
                                }
                              : null,
                          isLoading: false,
                          isBtnActive: _isButtonEnabled,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _controller.dispose();
    super.dispose();
  }
}
