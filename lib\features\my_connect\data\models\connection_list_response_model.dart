import 'package:cbrs/features/my_connect/data/models/connection_request_model.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_list_response_entity.dart';
import 'package:flutter/foundation.dart';

class ConnectionListResponseModel extends ConnectionListResponseEntity {
  const ConnectionListResponseModel({
    required List<ConnectionRequestModel> data,
    required PaginationMetaModel meta,
  }) : super(
          data: data,
          meta: meta,
        );

  factory ConnectionListResponseModel.fromJson(Map<String, dynamic> json) {
    final List<dynamic> dataJson = json['docs'] as List<dynamic>? ?? [];

    debugPrint("runtii ${dataJson.length}");

    final data = dataJson
        .map((item) =>
            ConnectionRequestModel.fromJson(item as Map<String, dynamic>))
        .toList();

    return ConnectionListResponseModel(
      data: data,
      meta: PaginationMetaModel(
        currentPage: json['page'] as int? ?? 1,
        totalPages: json['totalPages'] as int? ?? 1,
        itemsPerPage: json['limit'] as int? ?? 10,
        totalItems: json['totalDocs'] as int? ?? 0,
        hasNextPage: json['hasNextPage'] as bool? ?? false,
        hasPreviousPage: json['hasPrevPage'] as bool? ?? false,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': (data as List<ConnectionRequestModel>)
          .map((item) => item.toJson())
          .toList(),
      'meta': (meta as PaginationMetaModel).toJson(),
    };
  }
}

class PaginationMetaModel extends PaginationMetaEntity {
  const PaginationMetaModel({
    required int currentPage,
    required int totalPages,
    required int itemsPerPage,
    required int totalItems,
    required bool hasNextPage,
    required bool hasPreviousPage,
  }) : super(
          currentPage: currentPage,
          totalPages: totalPages,
          itemsPerPage: itemsPerPage,
          totalItems: totalItems,
          hasNextPage: hasNextPage,
          hasPreviousPage: hasPreviousPage,
        );

  factory PaginationMetaModel.fromJson(Map<String, dynamic> json) {
    return PaginationMetaModel(
      currentPage: json['currentPage'] as int,
      totalPages: json['totalPages'] as int,
      itemsPerPage: json['itemsPerPage'] as int,
      totalItems: json['totalItems'] as int,
      hasNextPage: json['hasNextPage'] as bool,
      hasPreviousPage: json['hasPreviousPage'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentPage': currentPage,
      'totalPages': totalPages,
      'itemsPerPage': itemsPerPage,
      'totalItems': totalItems,
      'hasNextPage': hasNextPage,
      'hasPreviousPage': hasPreviousPage,
    };
  }
}
