// ignore_for_file: public_member_api_docs, sort_constructors_first
class Transaction {
  const Transaction({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.transactionOwner,
    required this.transactionType,
    required this.billAmount,
    required this.paidAmount,
    required this.billRefNo,
    required this.status,
    required this.createdAt,
    required this.lastModified,
    this.senderPhone,
    this.senderEmail,
    this.beneficiaryId,
    this.beneficiaryName,
    this.beneficiaryEmail,
    this.beneficiaryAccountNo,
    this.beneficiaryPhone,
    this.bankName,
    this.bankCode,
    this.bankLogo,
    this.orderId,
    this.giftPackageId,
    this.totalGiftPackageQty,
    this.originalCurrency,
    this.changedCurrency,
    this.exchangeRate,
    this.billReason,
    this.paidDate,
    this.walletFTNumber,
    this.serviceCharge,
    this.vat,
    this.totalAmount,
    this.cardNumber,
    this.connectRefNo,
    this.beneficiaryConnectCode,
    this.senderConnectCode,
    this.mpgsReference,
    this.FTNumber,
  });
  final String id;
  final String senderId;
  final String senderName;

  final String? senderPhone;
  final String? senderEmail;
  final String transactionOwner;
  final String? beneficiaryId;
  final String? beneficiaryName;
  final String? beneficiaryEmail;
  final String? beneficiaryAccountNo;
  final String? beneficiaryPhone;
  final String? bankName;
  final String? bankCode;
  final String? bankLogo;

  final String transactionType;
  final String? orderId;
  final String? giftPackageId;
  final int? totalGiftPackageQty;
  final double billAmount;
  final String? originalCurrency;
  final String? changedCurrency;
  final double? exchangeRate;
  final double paidAmount;
  final String billRefNo;
  final String? billReason;
  final String? paidDate;
  final String? walletFTNumber;
  final String status;
  final DateTime createdAt;
  final DateTime lastModified;
  final double? serviceCharge;
  final double? vat;
  final double? totalAmount;
  final String? cardNumber;

  final String? connectRefNo;
  final String? beneficiaryConnectCode;
  final String? senderConnectCode;

  final String? mpgsReference;

  /// description FTNumber = ft Reference
  final String? FTNumber;

  /*
  @Column({ type: "varchar", nullable: true })
  cardNumber: string;


@Column({ type: 'varchar', nullable: true })
  mpgsReference?: string;


@Column({ type: "varchar", nullable: true })
  senderConnectCode?: string;

  @Column({ type: "varchar", nullable: true })
  beneficiaryConnectCode?: string;


@Column({ type: "varchar", nullable: true })
  FTNumber: string;  
  */
}
