part of 'my_connect_bloc.dart';

abstract class MyConnectState extends Equatable {
  const MyConnectState();

  @override
  List<Object?> get props => [];
}

class MyConnectInitialState extends MyConnectState {
  const MyConnectInitialState();
}

class MyConnectLoadingState extends MyConnectState {
  const MyConnectLoadingState();
}

class MyConnectErrorState extends MyConnectState {
  final String message;

  const MyConnectErrorState({required this.message});

  @override
  List<Object?> get props => [message];
}

class MyConnectLoadedState extends MyConnectState {
  final dynamic data; // TODO: Replace with proper data model

  const MyConnectLoadedState({required this.data});

  @override
  List<Object?> get props => [data];
}

class MyConnectUpdatedState extends MyConnectState {
  final dynamic data; // TODO: Replace with proper data model

  const MyConnectUpdatedState({required this.data});

  @override
  List<Object?> get props => [data];
}

class MyConnectDeletedState extends MyConnectState {
  const MyConnectDeletedState();
}

class ConnectionRequestSentState extends MyConnectState {
  final ConnectionRequestEntity connectionRequest;

  const ConnectionRequestSentState({required this.connectionRequest});

  @override
  List<Object?> get props => [connectionRequest];
}

class ConnectionsLoadedState extends MyConnectState {
  final List<ConnectionRequestEntity> connections;
  final PaginationMetaEntity meta;

  const ConnectionsLoadedState({
    required this.connections,
    required this.meta,
  });

  @override
  List<Object?> get props => [connections, meta];
}

class ConnectionRequestAcceptedState extends MyConnectState {
  const ConnectionRequestAcceptedState();
}

class ConnectionRequestRejectedState extends MyConnectState {
  const ConnectionRequestRejectedState();
}
