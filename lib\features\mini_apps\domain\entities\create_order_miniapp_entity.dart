import 'package:equatable/equatable.dart';

class CreateOrderMiniappEntity extends Equatable {
  const CreateOrderMiniappEntity({
    required this.statusCode,
    required this.success,
    required this.data,
  });
  final int statusCode;
  final bool success;
  final CreateOrderMiniappDataEntity data;

  @override
  List<Object?> get props => [statusCode, success, data];


}

class CreateOrderMiniappDataEntity extends Equatable {
  const CreateOrderMiniappDataEntity({
    this.senderId,
    this.senderName,
    this.senderPhone,
    this.transactionOwner,
    this.transactionType,
    this.merchantId,
    this.merchantType,
    this.billAmount,
    this.originalCurrency,
    this.serviceCharge,
    this.vat,
    this.paymentDetails,
    this.billRefNo,
    this.billReason,
    this.authorizationType,
    this.status,
    this.createdAt,
    this.lastModified,
    this.typeId,
    this.customerName,
    this.senderEmail,
    this.sessionID,
    this.cardNumber,
    this.redirectURL,
    this.mpgsReference,
    this.beneficiaryId,
    this.beneficiaryName,
    this.beneficiaryPhone,
    this.beneficiaryAccountNo,
    this.beneficiaryEmail,
    this.bankName,
    this.bankCode,
    this.bankID,
  });
  final String? senderId;
  final String? senderName;
  final String? senderPhone;
  final String? transactionOwner;
  final String? transactionType;
  final String? merchantId;
  final String? merchantType;
  final String? billAmount;
  final String? originalCurrency;
  final String? serviceCharge;
  final String? vat;
  final PaymentDetailsEntity? paymentDetails;
  final String? billRefNo;
  final String? billReason;
  final String? authorizationType;
  final String? status;
  final String? createdAt;
  final String? lastModified;
  final String? typeId;
  final String? customerName;
  final String? senderEmail;
  final String? sessionID;
  final String? cardNumber;
  final String? redirectURL;
  final String? mpgsReference;
  final String? beneficiaryId;
  final String? beneficiaryName;
  final String? beneficiaryPhone;
  final String? beneficiaryAccountNo;
  final String? beneficiaryEmail;
  final String? bankName;
  final String? bankCode;
  final String? bankID;

  @override
  List<Object?> get props => [
    senderId,
    senderName,
    senderPhone,
    senderEmail,
    billAmount,
    status,
    bankID
  ];
}

class PaymentDetailsEntity extends Equatable {
  const PaymentDetailsEntity({
    this.violationReference,
    this.ticketNo,
    this.driverFullName,
    this.issueDate,
    this.request,
  });
  final String? violationReference;
  final String? ticketNo;
  final String? driverFullName;
  final String? issueDate;
  final RequestEntity? request;

  @override
  List<Object?> get props => [violationReference, ticketNo, driverFullName];
}

class RequestEntity extends Equatable {
  const RequestEntity({this.scope});
  final String? scope;

  @override
  List<Object?> get props => [scope];
}
