// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:math';

import 'package:cbrs/core/common/widgets/custom_transaction_card.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/my_connect/presentation/views/connect_requested_page.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/my_connect_btn_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';

class MyConnectDetailPage extends StatefulWidget {
  const MyConnectDetailPage({super.key});

  @override
  State<MyConnectDetailPage> createState() => _MyConnectDetailPageState();
}

class _MyConnectDetailPageState extends State<MyConnectDetailPage> {
  @override
  Widget build(BuildContext context) {
    final blocValue = sl<HomeBloc>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Connect Details'),
      ),
      body: SafeArea(
        child: BlocProvider.value(
          value: blocValue,
          child: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _MyConnectDetailHeader(
                  recipientName: 'Robera insarmu',
                  recipientEmail: 'roba',
                  recipientAvatar: 'dd',
                ),
                SizedBox(
                  height: 16,
                ),
                _MyConnectTransactions(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _MyConnectDetailHeader extends StatelessWidget {
  const _MyConnectDetailHeader({
    required this.recipientEmail,
    required this.recipientName,
    super.key,
    this.recipientAvatar,
  });

  final String? recipientAvatar;
  final String recipientName;
  final String recipientEmail;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            color: Color(0xFFF6F6F6),
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x0F000000),
            blurRadius: 24,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: const MyConnectPersonDetail(
                  name: 'Robera insarmu',
                  phoneOrEmail: 'roba',
                  avatar: 'dd',
                ),
              ),
              GestureDetector(
                onTap: () {
                  showDeleteDialog(context);
                },
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(color: const Color(0xFFF1F1F1)),
                  ),
                  child: const Icon(Icons.delete, color: Colors.red),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          Row(
            children: [
              const Expanded(
                child: MyConnectBtnCard(
                  child: CustomBuildText(
                    text: 'Request Money',
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(
                width: 12,
              ),
              Expanded(
                child: MyConnectBtnCard(
                  hasBorder: false,
                  bgColor: Theme.of(context).primaryColor,
                  child: const CustomBuildText(
                    text: 'Send Money',
                    textAlign: TextAlign.center,
                    caseType: '',
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _menuCards({
    required IconData icons,
    required String title,
    required String description,
    required VoidCallback onTap,
    required Color titleColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Icon(
              icons,
              color: titleColor,
            ),
            const SizedBox(
              width: 6,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBuildText(
                    text: title,
                    fontWeight: FontWeight.w500,
                    color: titleColor,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: CustomBuildText(
                          text: description,
                          // 'Edit the the connects information and Save changes.',
                          color: Colors.black.withOpacity(0.4),
                          softWrap: true,
                          fontSize: 12,
                          caseType: '',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Image.asset(
              MediaRes.navigationArrowRight,
              width: 20,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> showDeleteDialog(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (_) => Dialog(
        insetPadding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomPagePadding(
              bottom: 16,
              decoration: const BoxDecoration(
                color: Color(0xFFF3F3F3),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  const CustomBuildText(text: 'Delete Connectio'),
                  const Spacer(),
                  InkWell(
                    onTap: () {
                      Navigator.of(context, rootNavigator: true).pop(true);
                    },
                    child: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            CustomPagePadding(
              child: Column(
                children: [
                  const CustomBuildText(
                    text:
                        'Are you sure you want to delete this connect? This action can’t be un done.',
                  ),
                  const SizedBox(
                    height: 36,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: MyConnectBtnCard(
                          onTap: () {
                            Navigator.of(context, rootNavigator: true)
                                .pop(true);
                          },
                          borderColor: Theme.of(context).primaryColor,
                          child: CustomBuildText(
                            text: 'Back',
                            textAlign: TextAlign.center,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 16,
                      ),
                      Expanded(
                        child: MyConnectBtnCard(
                          hasBorder: false,
                          bgColor: Colors.red,
                          onTap: () {
                            CustomToastification(
                              context,
                              message:
                                  'Successfully delete from connection list',
                            );
                            Navigator.of(context, rootNavigator: true)
                                .pop(true);
                          },
                          child: const CustomBuildText(
                            text: 'Delete',
                            textAlign: TextAlign.center,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }
}

class _MyConnectTransactions extends StatelessWidget {
  const _MyConnectTransactions({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            color: Color(0xFFF6F6F6),
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        shadows: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 24,
          ),
        ],
      ),
      child: ListView.separated(
        itemCount: 10,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        separatorBuilder: (context, index) => Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            width: double.infinity,
            height: 1,
            child: CustomPaint(
              painter: DottedLinePainter(),
            ),
          ),
        ),
        itemBuilder: (context, index) => Container(
          margin: const EdgeInsets.symmetric(vertical: 6),
          child: index == 10
              ? const CustomBuildText(text: 'See More')
              : CustomTransactionCard(
                  title: 'title',
                  transactionType: 'transactionType',
                  onTap: () {},
                  amount: '400',
                  date: 'date',
                  priceColor: Colors.red,
                ),
        ),
      ),
    );
  }
}
