part of 'my_connect_bloc.dart';

abstract class MyConnectEvent extends Equatable {
  const MyConnectEvent();

  @override
  List<Object?> get props => [];
}

class LoadMyConnectDataEvent extends MyConnectEvent {
  const LoadMyConnectDataEvent();
}

class UpdateMyConnectDataEvent extends MyConnect<PERSON>vent {
  final dynamic data; // TODO: Replace with proper data model

  const UpdateMyConnectDataEvent({
    required this.data,
  });

  @override
  List<Object?> get props => [data];
}

class DeleteMyConnectDataEvent extends MyConnectEvent {
  final String id;

  const DeleteMyConnectDataEvent({
    required this.id,
  });

  @override
  List<Object?> get props => [id];
}

class SendConnectionRequestEvent extends MyConnectEvent {
  final String recipientId;
  final String recipientName;
  final String? recipientEmail;
  final String recipientPhone;
  final String? recipientAvatar;

  const SendConnectionRequestEvent({
    required this.recipientId,
    required this.recipientName,
    this.recipientEmail,
    required this.recipientPhone,
    this.recipientAvatar,
  });

  @override
  List<Object?> get props => [
        recipientId,
        recipientName,
        recipientEmail,
        recipientPhone,
        recipientAvatar,
      ];
}

class FetchConnectionsEvent extends MyConnectEvent {
  final String? status;
  final String? scope;
  final int? page;
  final int? limit;

  const FetchConnectionsEvent({
    this.status,
    this.scope,
    this.page,
    this.limit,
  });

  @override
  List<Object?> get props => [status, scope, page, limit];
}

class AcceptConnectionRequestEvent extends MyConnectEvent {
  final String requestId;

  const AcceptConnectionRequestEvent({required this.requestId});

  @override
  List<Object?> get props => [requestId];
}

class RejectConnectionRequestEvent extends MyConnectEvent {
  final String requestId;

  const RejectConnectionRequestEvent({required this.requestId});

  @override
  List<Object?> get props => [requestId];
}
