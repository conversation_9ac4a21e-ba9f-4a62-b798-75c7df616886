import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';
import 'package:equatable/equatable.dart';

class ConnectionListResponseEntity extends Equatable {
  const ConnectionListResponseEntity({
    required this.data,
    required this.meta,
  });
  final List<ConnectionRequestEntity> data;
  final PaginationMetaEntity meta;

  @override
  List<Object?> get props => [data, meta];
}

class PaginationMetaEntity extends Equatable {
  final int currentPage;
  final int totalPages;
  final int itemsPerPage;
  final int totalItems;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginationMetaEntity({
    required this.currentPage,
    required this.totalPages,
    required this.itemsPerPage,
    required this.totalItems,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  @override
  List<Object?> get props => [
        currentPage,
        totalPages,
        itemsPerPage,
        totalItems,
        hasNextPage,
        hasPreviousPage,
      ];
}
