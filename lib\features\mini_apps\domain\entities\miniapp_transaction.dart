class MiniappTransaction {
  final String transactionType;
  final String recipientName;
  final String senderName;
  final double amount;
  final double serviceFee;
  final double vat;
  final DateTime date;
  final double total;
  final String billRefNo;

  const MiniappTransaction({
    required this.transactionType,
    required this.recipientName,
    required this.amount,
    required this.serviceFee,
    required this.senderName,
    required this.vat,
    required this.date,
    required this.total,
    required this.billRefNo,
  });

  factory MiniappTransaction.fromJson(Map<String, dynamic> json) {
    final billAmount = (json['billAmount'] as num?)?.toDouble() ?? 0.0;
    final serviceCharge = (json['serviceCharge'] as num?)?.toDouble() ?? 0.0;
    final vat = (json['VAT'] as num?)?.toDouble() ?? 0.0;

    return MiniappTransaction(
      transactionType: (json['transactionType'] as String?) ?? '',
      recipientName: (json['merchantType'] as String?) ?? '',
      senderName: (json['senderName'] as String?) ?? '',
      amount: billAmount,
      serviceFee: serviceCharge,
      vat: vat,
      date: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      total: billAmount + serviceCharge + vat,
      billRefNo: (json['billRefNo'] as String?) ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transactionType': transactionType,
      'recipientName': recipientName,
      'amount': amount,
      'serviceFee': serviceFee,
      'vat': vat,
      'date': date.toIso8601String(),
      'total': total,
      'billRefNo': billRefNo,
    };
  }
}
