{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b804fba324d638a9dd738756ed7bd275", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f17063ab36ef39d74b78fb2d4edb8151", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f17063ab36ef39d74b78fb2d4edb8151", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dae362951ce264e251e5b82484d360ab", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ffec809f91c26c6fb1c1c534c4f9d59", "guid": "bfdfe7dc352907fc980b868725387e98a4d710700527ad989c60a7681edb3c22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb45d09f1b4af7400f0d21e01f4e017b", "guid": "bfdfe7dc352907fc980b868725387e98a0be20fbeeff465402a0bc5546ac8739", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987783e9097feb1d3198692f5e905bb0a4", "guid": "bfdfe7dc352907fc980b868725387e98c7d8b70b9eaf5fe138ceeccf10672f6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba776c55396bd2bba6597c7525e5aa5c", "guid": "bfdfe7dc352907fc980b868725387e98d114fb41bca185d1fb4c58b0091430f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9e9a26a2ff1f3c46fcb48d8e6e5602d", "guid": "bfdfe7dc352907fc980b868725387e987484403e3ea334679d0299c3360f076f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983989d487daed1160cfa4b6777373deb0", "guid": "bfdfe7dc352907fc980b868725387e981186a38ad76cdbd007c4e14d42d59b53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98458b5a6f0f52d916fcd5e95031e618a8", "guid": "bfdfe7dc352907fc980b868725387e980f23ddf3c57561b15eb3e5a25f1d1a8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d516149caad78000278ac6990d33c0", "guid": "bfdfe7dc352907fc980b868725387e989d23ca6b6491084d3da38fe768cff233", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbc878242785387bca1dbfe76123267c", "guid": "bfdfe7dc352907fc980b868725387e98cd36b956974eaf04bf15ea5cfdae4c99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe0093fc789e75bfadf76099e29dd404", "guid": "bfdfe7dc352907fc980b868725387e984e337015a04b28c97241a0b106365c58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b05250108f88a53531d23939bf7cbeb6", "guid": "bfdfe7dc352907fc980b868725387e9851e20e24ec1cc46a53bfcdc0f1c15cf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9abfd889677772e2fa94fe83688f395", "guid": "bfdfe7dc352907fc980b868725387e984a7bf97f86cba3446a7bab49bae384a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986095b6dfe6ef27fafeabb6eed142e4c8", "guid": "bfdfe7dc352907fc980b868725387e9878574163e584fb56f8cfa42406e77b1c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98101fd2a1e41cec4eef9c5e9ffc97938a", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846883f85711ccdcdd08bb8008062d304", "guid": "bfdfe7dc352907fc980b868725387e989c1908ec6b2b61515b516482358f6dfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7593ed530222f1fe80f45cd9b988bef", "guid": "bfdfe7dc352907fc980b868725387e984eed8aba4565c682c9b51ae795fd399b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1844105248f2fb5ea7ee75051676d14", "guid": "bfdfe7dc352907fc980b868725387e9852efe027fdc77e6af6a14b5efa0502ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3b26485d7cd76cadaedd830a295e3f3", "guid": "bfdfe7dc352907fc980b868725387e98181069611e3ecff167bd949cae00c558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806699e66a36c944e254441c1c192978c", "guid": "bfdfe7dc352907fc980b868725387e98206b94dfd6bba978e1b974ab29cee440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981588c71cc17a23ef4997a214103bde55", "guid": "bfdfe7dc352907fc980b868725387e98a542a9108464c93f726ff0160e6cb7e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847e9195053a1e3fd3df0a03f82961482", "guid": "bfdfe7dc352907fc980b868725387e9891d8f75896c130b17c4fbf734ae35ee0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c416bf5a3ac4aac8d152becdd015c0", "guid": "bfdfe7dc352907fc980b868725387e98997948b103e44cf5a06c76a2a21ba7da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dce818b07769a60dde0798c8a4c931d1", "guid": "bfdfe7dc352907fc980b868725387e98ca23546d1e1afb4e1234ad6ac6cebc64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b7476b4f48edd532559e98e733da646", "guid": "bfdfe7dc352907fc980b868725387e9803e73c2eac26f7726291d95fd2e5c712"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b74daf7cc1ad3238c61022cf4eba92", "guid": "bfdfe7dc352907fc980b868725387e986bfa30fe15dd0e317afffe18539f2446"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989df9ccae5ba53a451165ae79c3f483e5", "guid": "bfdfe7dc352907fc980b868725387e984d5f83937065bdeea5e3320647971ad8"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}