import 'dart:io';

import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/guest/presentation/widget/guest_mode_banner.dart';
import 'package:cbrs/features/guest/presentation/widget/quick_actions_grid.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

class BankAccountsSection extends StatelessWidget {
  const BankAccountsSection({
    required this.userName,
    super.key,
    this.avatarUrl,
  });
  final String userName;
  final String? avatarUrl;

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: MediaQuery.sizeOf(context).height *
              (Platform.isIOS ? 0.325 : 0.325),
          width: double.infinity,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(MediaRes.usdBackground),
              fit: BoxFit.cover,
              // colorFilter: ColorFilter.mode(
              //   Colors.black.withOpacity(0.1),
              //   BlendMode.darken,
              // ),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.transparent,
                offset: Offset(0, 4),
                blurRadius: 15,
                spreadRadius: 2,
              ),
              BoxShadow(
                color: Colors.transparent,
                offset: Offset(0, 10),
                blurRadius: 20,
                spreadRadius: -2,
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: Column(
              children: [
                Expanded(
                  child: SafeArea(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        children: [
                          if (Platform.isAndroid) SizedBox(height: 14.h),
                          _buildHeader(context),
                          SizedBox(height: 24.h),
                          const GuestModeBanner(),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: -48.h,
          left: 16.w,
          right: 16.w,
          child: const QuickActionsGrid(),
        ),
        /* // Quick Actions Grid
        Transform.translate(
          offset: Offset(0, 5.h),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute<void>(
                      builder: (context) => const GuestSendMoneyScreen(),
                    ),
                  );
                },
                child: Container(
                  height: 118.h,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.white.withOpacity(0.6),
                        Colors.white,
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                  ),
                  child: const QuickActionsGrid(),
                ),
              ),
            ),
          ),
        ),
      */
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Row(
            children: [
              Container(
                height: 48.w,
                width: 48.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 2.w,
                  ),
                ),
                child: ClipOval(
                  child: Image.asset(
                    'assets/icons/Profile.png',
                    width: 56.w,
                    height: 56.h,
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                'Hi, There',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  height: 1.2,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Row(
                              children: [
                                SizedBox(width: 2.w),
                                Text(
                                  '👋',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        if (userName == 'Loading...')
                          Shimmer.fromColors(
                            baseColor: Colors.white.withOpacity(0.3),
                            highlightColor: Colors.white.withOpacity(0.5),
                            child: Container(
                              width: 150.w,
                              height: 16.h,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4.r),
                              ),
                            ),
                          )
                        else
                          Text(
                            'Welcome to Connect!',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.8),
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              height: 1.2,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        Row(
          children: [
            SizedBox(width: 4.w),
          ],
        ),
      ],
    );
  }
}
