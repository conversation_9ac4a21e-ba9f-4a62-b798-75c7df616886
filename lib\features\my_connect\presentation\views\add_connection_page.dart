import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/connection_animation_dialog.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/member_lookup_response.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:equatable/equatable.dart';
import 'package:cbrs/features/my_connect/applications/bloc/my_connect_bloc.dart';

class AddConnectionPage extends StatefulWidget {
  const AddConnectionPage({super.key});

  @override
  State<AddConnectionPage> createState() => _AddConnectionPageState();
}

class _AddConnectionPageState extends State<AddConnectionPage> {
  String selectedTab = 'Phone Number';
  final List<String> tabList = ['Phone Number', 'Email'];

  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  bool _isValidEmail = false;
  bool _isValidPhone = false;
  bool _showRecipientPreview = false;
  String _recipientName = '';
  String _recipientPhone = '';
  String? _recipientEmail = '';
  String? _recipentAvatar = '';
  MemberLookupResponse? _memberInfo;
  bool _isLoading = false;

  void onTap(String tabName) {
    if (tabName != selectedTab) {
      setState(() {
        selectedTab = tabName;
        _isValidEmail = false;
        _isValidPhone = false;
        _showRecipientPreview = false;
        _recipientName = '';
        _recipentAvatar = '';
        _memberInfo = null;
        if (selectedTab == 'Email') {
          _emailController.clear();
        } else {
          _phoneController.clear();
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _emailController.addListener(() {
      if (selectedTab == 'Email') {
        _validateEmail();
      }
    });
    _phoneController.addListener(_validatePhone);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _validateEmail() {
    final email = _emailController.text.trim();
    final isValid = email.contains('@') && email.contains('.');
    setState(() {
      _isValidEmail = isValid;
      if (isValid) {
        _showRecipientPreview = false;
      }
    });
    if (isValid) {
      context.read<WalletTransferBloc>().add(
            LookupMemberEvent(email: email),
          );
    } else {
      setState(() {
        _showRecipientPreview = false;
      });
    }
  }

  void _validatePhone() {
    final phone = _phoneController.text.trim();
    final isValid = phone.length >= 9;
    setState(() {
      _isValidPhone = isValid;
      if (isValid) {
        _showRecipientPreview = false;
      }
    });
    if (isValid) {
      context.read<WalletTransferBloc>().add(
            LookupMemberEvent(phoneNumber: phone),
          );
    } else {
      setState(() {
        _showRecipientPreview = false;
      });
    }
  }

  void _onSearch() {
    if (selectedTab == 'Email') {
      _validateEmail();
    } else {
      _validatePhone();
    }
  }

  void _onAddConnection() {
    if (_memberInfo != null) {
      // CustomToastification(
      //   context,
      //   message: 'Connection request sent',
      //   isError: false,
      // );
      debugPrint('Sending connectionkk request');
      context.read<MyConnectBloc>().add(
            SendConnectionRequestEvent(
              recipientId: _memberInfo!.id,
              recipientName: _recipientName,
              recipientEmail: _recipientEmail,
              recipientPhone: _recipientPhone,
              recipientAvatar: _recipentAvatar,
            ),
          );
    }
    _showConnectionAnimationDialog();
  }

  Future<void> _showConnectionAnimationDialog() async {
    // Get current user data for sender information
    final currentUser = await sl<AuthLocalDataSource>().getCachedUserData();

    if (currentUser != null) {
      // Get sender initial from current user's name
      final senderInitial = currentUser.firstName.isNotEmpty
          ? currentUser.firstName[0].toUpperCase()
          : 'U';

      // Get receiver initial from recipient name
      final receiverInitial = _recipientName.isNotEmpty
          ? _recipientName.split(' ')[0][0].toUpperCase()
          : 'R';

      await showDialog(
        context: context,
        barrierDismissible: true,
        builder: (_) => ConnectionAnimationDialog(
          senderInitial: senderInitial,
          receiverInitial: receiverInitial,
          senderAvatar: currentUser.avatar,
          receiverAvatar: _recipentAvatar,
        ),
      );
    } else {
      // Fallback to original implementation if user data isn't available
      await showDialog(
        context: context,
        barrierDismissible: true,
        builder: (_) => const ConnectionAnimationDialog(
          senderInitial: 'R',
          receiverInitial: 'M',
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<WalletTransferBloc, WalletTransferState>(
          listener: (context, state) {
            debugPrint("Staat te $state");
            if (state is MemberLookupSuccess) {
              setState(() {
                _recipientName = state.member.fullName;
                _showRecipientPreview = true;
                _memberInfo = state.member;
                _recipientPhone = state.member.phoneNumber;
                _recipientEmail = state.member.email;
                _recipentAvatar = state.member.avatar;
              });
            } else if (state is MemberLookupNotFound) {
              setState(() {
                _showRecipientPreview = false;
                if (selectedTab == 'Email') {
                  _isValidEmail = false;
                } else {
                  _isValidPhone = false;
                }
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message)),
              );
            } else if (state is WalletTransferError) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message)),
              );
            }
          },
        ),
        BlocListener<MyConnectBloc, MyConnectState>(
          listener: (context, state) {
            if (state is ConnectionRequestSentState) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                    content: Text('Connection request sent successfully!')),
              );
              Navigator.of(context).pop();
            } else if (state is MyConnectErrorState) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message)),
              );
            }
          },
        ),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Add Connection'),
        ),
        body: CustomPagePadding(
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    spacing: 16,
                    children: [
                      const CustomPageHeader(
                        pageTitle: 'Add Connection',
                        description:
                            'Find a friend or relative by phone or email and add to your Connect List.',
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        child: CustomRoundedTabs(
                          onTap: onTap,
                          selectedTab: selectedTab,
                          tabList: tabList,
                        ),
                      ),
                      switchTextField(),
                      if (_showRecipientPreview)
                        RecipientCard(
                          name: _recipientName,
                          accountNumber: selectedTab != 'Email'
                              ? _recipientPhone
                              : _recipientEmail ?? '',
                          isBirrTransfer: false,
                          onTap: _onAddConnection,
                        ),
                    ],
                  ),
                ),
              ),
              SafeArea(
                child: CustomRoundedBtn(
                  btnText: 'Search',
                  isLoading: false,
                  onTap: _onSearch,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget switchTextField() {
    switch (selectedTab) {
      case 'Email':
        return CustomTextInput(
          inputLabel: 'Email addreess',
          hintText: 'Email address',
          controller: _emailController,
        );
      default:
        return CustomTextInput(
          inputLabel: 'Phone Number',
          hintText: 'Phone Number',
          controller: _phoneController,
        );
    }
  }
}
