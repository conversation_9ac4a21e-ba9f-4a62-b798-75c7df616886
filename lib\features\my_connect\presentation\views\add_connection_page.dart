import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/connection_animation_dialog.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:flutter/material.dart';

class AddConnectionPage extends StatefulWidget {
  const AddConnectionPage({super.key});

  @override
  State<AddConnectionPage> createState() => _AddConnectionPageState();
}

class _AddConnectionPageState extends State<AddConnectionPage> {
  String selectedTab = 'Phone Number';
  final List<String> tabList = ['Phone Number', 'Email'];

  void onTap(String tabName) {
    if (tabName != selectedTab) {
      setState(() {
        selectedTab = tabName;
      });
    }
  }

//// flow.
  ///1. user can serch using phone number or email
  ///2. bloc handles state updatinf
  ///3. the butoon hits add connection after user looked uo
  ///4. when user taps on add connection bottom shet will be appeared
  Future<void> _onSearch() async {
    // await Future.delayed(const Duration(seconds: 1));
    _onAddConnection();
  }

  Future<void> _onAddConnection() async {
    // await Future.delayed(const Duration(seconds:1));
    _showConnectionAnimationDialog();
  }

  Future<void> _showConnectionAnimationDialog() async {
  await showDialog(
    context: context,
    barrierDismissible: true,
    builder: (_) => const ConnectionAnimationDialog(
      senderInitial: 'R',
      receiverInitial: 'M',
    ),
  );
}


  

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Connection'),
      ),
      body: CustomPagePadding(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  spacing: 16,
                  children: [
                    const CustomPageHeader(
                      pageTitle: 'Add Connection',
                      description:
                          'Find a friend or relative by phone or email and add to your Connect List.',
                    ),
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      child: CustomRoundedTabs(
                        onTap: onTap,
                        selectedTab: selectedTab,
                        tabList: tabList,
                      ),
                    ),
                    switchTextField(),
                    RecipientCard(
                      name: 'Helen Solomon Taye',
                      accountNumber: '**********',
                      isBirrTransfer: false,

                      // recipientEmail: _emailController.text,
                      onTap: _onAddConnection,
                    ),
                  ],
                ),
              ),
            ),
            SafeArea(
              child: CustomRoundedBtn(
                btnText: 'Search',
                isLoading: false,
                onTap: _onSearch,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget switchTextField() {
    switch (selectedTab) {
      case 'Email':
        return const CustomTextInput(
          inputLabel: 'Email addreess',
          hintText: 'Email address',
        );

      default:
        return const CustomTextInput(
          inputLabel: 'Phone Number',
          hintText: 'Phone Number',
        );
    }
  }
}
