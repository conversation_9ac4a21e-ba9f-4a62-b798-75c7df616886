import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_app_bar.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/money_request/domain/entities/member_lookup_entity.dart';
import 'package:cbrs/features/money_request/domain/entities/my_requests_list_entity.dart';
import 'package:cbrs/features/money_request/domain/usecases/send_money_request_usecase.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_request_money_confirm_request/send_request_money_confirm_request_bloc.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class SendMoneyRequestAddMoneyScreen extends StatefulWidget {
  const SendMoneyRequestAddMoneyScreen({
    required this.member,
    super.key,
  });

  final MemberLookupEntity member;

  @override
  State<SendMoneyRequestAddMoneyScreen> createState() =>
      _SendMoneyRequestAddMoneyScreenState();
}

class _SendMoneyRequestAddMoneyScreenState
    extends State<SendMoneyRequestAddMoneyScreen> {
  late CurrencyInputController _currencyController;
  double amount = 0;
  final TextEditingController _reasonController = TextEditingController();
  bool _isLoading = false;
  late TransactionBottomSheetsManager _bottomSheetsManager;

  String _senderName = 'Rober'; // TODO

  @override
  void initState() {
    super.initState();

    context.read<HomeBloc>().add(const HomeProfileFetchingEvent());

    context.read<WalletBalanceBloc>().add(
          FetchWalletEvent(
            isUsdWallet: GlobalVariable.currentlySelectedWallet == 'USD',
            forceTheme: false,
          ),
        );

    final state = context.read<WalletBalanceBloc>().state;

    if (state is WalletLoadedState) {
      setState(() {
        walletBalance = state.isUsdWallet ? state.usdBalance : state.etbBalance;
      });
    }

    _currencyController = CurrencyInputController(
      currencyType: GlobalVariable.currentlySelectedWallet == 'USD'
          ? CurrencyType.usd
          : CurrencyType.etb,
      maxBalance: 0,
      ignoreWalletAmountCheck: true,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      isFromMoneyRequest: true,
      onMoneyRequest: _sendMoneyRequest,
      //  () {
      //   CustomToastification(context, message: 'Got money request');
      // },
      pinController: TextEditingController(),
      transactionType: TransactionType.moneyRequest,
      onPinSubmitted: (value) {},
      reasonController: _reasonController,
      onTransactionSuccess: (response) {
        // _showSuccessBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    final amount = _currencyController.numericAmount;

    if (amount <= 0) {
      CustomToastification(
        context,
        message: 'Amount must be greater than 0',
      );
      return;
    }

    // Show confirm bottom sheet
    _showConfirmRequestBottomSheet();
  }

  void _showConfirmRequestBottomSheet() {
    // Reset loading state when showing the confirm sheet
    setState(() {
      _isLoading = false;
    });

    final amount = _currencyController.numericAmount;

    final data = {
      'Transaction Type': 'money_request',
      'Beneficiary Name': widget.member.fullName ?? '',
      'Beneficiary Email': widget.member.emailAddress ?? '',
      'Beneficiary Phone': widget.member.phoneNumber ?? '',
      'Sender Name': _senderName,
      'Amount': amount,
      'createdAt': DateFormat('MMM dd, yyyy').format(DateTime.now()),
    };

    // Clear any previous reason text
    _reasonController.clear();
    setState(() {
      _isLoading = false;
    });

    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: data,
      requiresOtp: false,
      billRefNo: '',
      totalAmount: amount,
      billAmount: amount,
      originalCurrency: GlobalVariable.currentlySelectedWallet,
    );
  }

  void _sendMoneyRequest() {
    final amount = _currencyController.numericAmount;

    context.read<SendRequestMoneyConfirmRequestBloc>().add(
          SendRequestMoneyConfirmRequestEvent(
            sendMoneyRequestParam: SendMoneyRequestParam(
              memberId: widget.member.id,
              transactionType: 'money_request',
              currency: GlobalVariable.currentlySelectedWallet,
              amount: amount,
              reason: _reasonController.text,
            ),
          ),
        );
  }

  void _showSuccessBottomSheet(MoneyRequestEntity moneyRequest) {
    final data = {
      'Transaction Type': 'money_request',
      'Beneficiary Name': widget.member.fullName ?? '',
      'Beneficiary Email': widget.member.emailAddress ?? '',
      'Beneficiary Phone': widget.member.phoneNumber ?? '',
      'Sender Name': _senderName,
      // 'Beneficiary Name': moneyRequest.beneficiaryName,
      // 'Beneficiary email': moneyRequest.beneficiaryEmail,
      // 'Beneficiary Phone': moneyRequest.beneficiaryPhone,
      'Amount':
          '${AppMapper.safeFormattedNumberWithDecimal(moneyRequest.billAmount)} ${moneyRequest.currency}',
      'Date': AppMapper.safeFormattedDate(moneyRequest.createdAt),
      'reason': _reasonController.text.isNotEmpty
          ? _reasonController.text
          : moneyRequest.reason,
    };

    _bottomSheetsManager.showSuccessScreenBottomSheet(
      data,
      status: 'Requested',
      transactionId: '',
      billRefNo: '',
      totalAmount: moneyRequest.totalAmount,
      billAmount: moneyRequest.billAmount,
      originalCurrency: moneyRequest.currency,
      showActionButtons: false,
    );
  }

  double walletBalance = 0;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    context.read<WalletBalanceBloc>().add(
          FetchWalletEvent(
            isUsdWallet: GlobalVariable.currentlySelectedWallet == 'USD',
            forceTheme: false,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listener: (context, state) {
        if (state is HomeProfileLoadedState) {
          _senderName = state.localUser.fullName;

          // CustomToastification(context, message: state.localUser.fullName);
        }
      },
      child: BlocListener<WalletBalanceBloc, HomeState>(
        listener: (context, state) {
          // CustomToastification(context, message: 'tttkkekkeke $state');

          if (state is WalletLoadedState) {
            setState(() {
              walletBalance =
                  state.isUsdWallet ? state.usdBalance : state.etbBalance;
            });
          }
        },
        child: BlocConsumer<SendRequestMoneyConfirmRequestBloc,
            SendRequestMoneyConfirmRequestState>(
          listener: (context, state) {
            if (state is SendRequestMoneyResultState) {
              Navigator.pop(context);
              setState(() {
                _isLoading = false;
              });
              _showSuccessBottomSheet(state.moneyRequest);
              // CustomToastification(context, message: "Done");
            }
          },
          builder: (context, state) {
            return Scaffold(
              appBar: CustomAppBar(
                context: context,
                title: 'Send Money Request',
              ),
              body: SafeArea(
                child: CurrencyInputWidget(
                  controller: _currencyController,
                  title: 'Send Money Request',
                  subtitle: 'Send money requests to your relatives, '
                      'and receive funds in your wallet.',
                  transactionType: 'money_request',
                  balanceLabel: CurrencyFormatter.formatWalletBalance(
                    walletBalance,
                    GlobalVariable.currentlySelectedWallet,
                  ),
                  onContinue: _onContinuePressed,
                  isLoading: _isLoading,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
