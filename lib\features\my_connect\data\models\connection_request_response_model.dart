import 'package:cbrs/features/my_connect/data/models/connection_request_model.dart';

class ConnectionRequestResponseModel {
  final int statusCode;
  final bool success;
  final String message;
  final ConnectionRequestModel data;

  const ConnectionRequestResponseModel({
    required this.statusCode,
    required this.success,
    required this.message,
    required this.data,
  });

  factory ConnectionRequestResponseModel.fromJson(Map<String, dynamic> json) {
    return ConnectionRequestResponseModel(
      statusCode: json['statusCode'] as int,
      success: json['success'] as bool,
      message: json['message'] as String,
      data:
          ConnectionRequestModel.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'statusCode': statusCode,
      'success': success,
      'message': message,
      'data': data.toJson(),
    };
  }
}
