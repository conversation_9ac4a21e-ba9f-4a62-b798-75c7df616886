import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/quick_wallet_detail_entity.dart';
import 'package:cbrs/features/transactions/presentation/views/quick%20wallet%20transfer/widgets/quick_wallet_transfer_wallet_selection_modal.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class QuickWalletTransferScreen extends StatelessWidget {
  final String? recipientEmail;
  final String? recipientPhone;
  final String? recipientName;
  final String? senderName;

  const QuickWalletTransferScreen({
    this.recipientEmail,
    this.recipientPhone,
    this.recipientName,
    this.senderName,
    super.key,
  });

  void _showWalletSelectionModal(BuildContext context, List<dynamic> wallets) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
          ),
          child: Container() // todo
          /*
        
         QuickWalletTransferWalletSelectionModal(
          wallet: 
          recipientName: recipientName ?? 'Recipient',
          onWalletSelected: (selectedWallet) {
            // Handle wallet selection
            if (selectedWallet.currency == 'USD') {
              context.pushNamed(
                AppRouteName.addAmountUsdQuickWallet,
                extra: {
                  'walletBalance': selectedWallet.balance,
                  'recipientEmail': recipientEmail,
                  'recipientPhone': recipientPhone,
                  'recipientName': recipientName,
                  'senderName': senderName,
                  'currency': 'USD',
                },
              );
            } else if (selectedWallet.currency == 'ETB') {
              context.pushNamed(
                AppRouteName.addAmountBirrQuickWallet,
                extra: {
                  'walletBalance': selectedWallet.balance,
                  'currency': 'ETB',
                  'recipientEmail': recipientEmail,
                  'recipientPhone': recipientPhone,
                  'recipientName': recipientName,
                  'senderName': senderName,
                },
              );
            }
          },
        ),
     */

          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Quick Wallet Transfer',
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 16.0.w,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 21.h),
            const CustomPageHeader(
              pageTitle: 'Quick Wallet Transfer',
              description:
                  'Select the wallet account you want to use to send money to this beneficiary.',
            ),
            SizedBox(height: 24.h),
            // Recipient information card
            GestureDetector(
              //onTap: () => _showWalletSelectionModal(context, wallets),
              child: Container(
                padding: EdgeInsets.all(14.w),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withOpacity(0.4),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: Theme.of(context).primaryColor,
                      radius: 24.r,
                      child: Text(
                        recipientName!.substring(0, 2).toUpperCase(),
                        style: GoogleFonts.outfit(
                          color: Colors.white,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            recipientName!,
                            style: GoogleFonts.outfit(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            recipientEmail ?? recipientPhone!,
                            style: GoogleFonts.outfit(
                              color: Theme.of(context).primaryColor,
                              fontSize: 14.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const Expanded(child: SizedBox()),

            // Bottom button
            SafeArea(
              child: Padding(
                padding: EdgeInsets.only(bottom: 16.h),
                child: CustomRoundedBtn(
                  btnText: 'Continue',
                  onTap: () {},
                  isLoading: false,
                  // isLoading
                  //     ? null
                  //     : () => _showWalletSelectionModal(context, wallets),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    ;
  }
}
