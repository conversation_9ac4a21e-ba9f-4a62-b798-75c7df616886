import 'package:equatable/equatable.dart';

class MiniappPaymentRequest extends Equatable {
  final String stage;
  final String timestamp;
  final String nonceStr;
  final String? method;
  final BizContent bizContent;
  final String sign;
  final String confirmPayload;
  final String? dataToken;
  final String? deviceUuid;
  final String? sourceApp;
  final String? accessToken;
  final String? apiKey;

  const MiniappPaymentRequest({
    required this.stage,
    required this.timestamp,
    required this.nonceStr,
    this.method,
    required this.bizContent,
    required this.sign,
    required this.confirmPayload,
    this.dataToken,
    this.deviceUuid,
    this.sourceApp,
    this.accessToken,
    this.apiKey,
  });

  @override
  List<Object?> get props => [
    stage,
    timestamp,
    nonceStr,
    method,
    bizContent,
    sign,
    confirmPayload,
    dataToken,
    deviceUuid,
    sourceApp,
    accessToken,
    apiKey,
  ];

  Map<String, dynamic> toJson() {
    return {
      'stage': stage,
      'timestamp': timestamp,
      'nonce_str': nonceStr,
      if (method != null) 'method': method,
      'biz_content': bizContent.toJson(),
      'sign': sign,
      'confirmpayload': confirmPayload,
    };
  }
}

class BizContent extends Equatable {
  final String? notifyUrl;
  final String? tradeType;
  final String? appId;
  final dynamic merchCode;
  final dynamic merchOrderId;
  final String? title;
  final double totalAmount;
  final String? transCurrency;
  final String? timeoutExpress;
  final dynamic payeeIdentifier;
  final String? payeeIdentifierType;
  final String? payeeType;
  final String? utilityProvider;
  final MiniappData utilityData;
  final String? additionalData;

  const BizContent({
    this.notifyUrl,
    this.tradeType,
    this.appId,
    this.merchCode,
    this.merchOrderId,
    this.title,
    required this.totalAmount,
    this.transCurrency,
    this.timeoutExpress,
    this.payeeIdentifier,
    this.payeeIdentifierType,
    this.payeeType,
    this.utilityProvider,
    required this.utilityData,
    this.additionalData,
  });

  @override
  List<Object?> get props => [
    notifyUrl,
    tradeType,
    appId,
    merchCode,
    merchOrderId,
    title,
    totalAmount,
    transCurrency,
    timeoutExpress,
    payeeIdentifier,
    payeeIdentifierType,
    payeeType,
    utilityProvider,
    utilityData,
    additionalData,
  ];

  Map<String, dynamic> toJson() {
    return {
      if (notifyUrl != null) 'notify_url': notifyUrl,
      if (tradeType != null) 'trade_type': tradeType,
      if (appId != null) 'appid': appId,
      if (merchCode != null) 'merch_code': merchCode,
      if (merchOrderId != null) 'merch_order_id': merchOrderId,
      if (title != null) 'title': title,
      'total_amount': totalAmount,
      if (transCurrency != null) 'trans_currency': transCurrency,
      if (timeoutExpress != null) 'timeout_express': timeoutExpress,
      if (payeeIdentifier != null) 'payee_identifier': payeeIdentifier,
      if (payeeIdentifierType != null)
        'payee_identifier_type': payeeIdentifierType,
      if (payeeType != null) 'payee_type': payeeType,
      if (utilityProvider != null) 'utility_provider': utilityProvider,
      'utility_data': utilityData.toJson(),
      if (additionalData != null) 'additional_data': additionalData,
    };
  }
}

class MiniappData extends Equatable {
  final String? id;
  final String? payerFullName;
  final String? reference;
  final String? serviceType;
  final String? cardNumber;
  final int? packageTime;
  final String? productCode;
  final String? customerNumber;
  final String? orderId;
  final String? narrative;

  const MiniappData({
    this.id,
    this.payerFullName,
    this.reference,
    this.serviceType,
    this.cardNumber,
    this.packageTime,
    this.productCode,
    this.customerNumber,
    this.orderId,
    this.narrative,
  });

  @override
  List<Object?> get props => [
    id,
    payerFullName,
    reference,
    serviceType,
    cardNumber,
    packageTime,
    productCode,
    customerNumber,
    orderId,
    narrative,
  ];

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (payerFullName != null) 'payer_full_name': payerFullName,
      if (reference != null) 'reference': reference,
      if (serviceType != null) 'serviceType': serviceType,
      if (cardNumber != null) 'card_number': cardNumber,
      if (packageTime != null) 'package_time': packageTime,
      if (productCode != null) 'product_code': productCode,
      if (customerNumber != null) 'customer_number': customerNumber,
      if (orderId != null) 'orderId': orderId,
      if (narrative != null) 'narrative': narrative,
    };
  }
}
