import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_list_response_entity.dart';
import 'package:cbrs/features/my_connect/domain/repositories/my_connect_repository.dart';
import 'package:equatable/equatable.dart';

class MyConnectFetchConnectsUseCase
    implements
        UsecaseWithParams<ConnectionListResponseEntity, FetchConnectsParams> {
  const MyConnectFetchConnectsUseCase(this._repository);
  final MyConnectRepository _repository;

  @override
  ResultFuture<ConnectionListResponseEntity> call(
      FetchConnectsParams params) async {
    return _repository.getConnections(
      status: params.status,
      scope: params.scope,
      page: params.page,
      limit: params.limit,
    );
  }
}

class FetchConnectsParams extends Equatable {
  final String? status; // pending, accepted, rejected
  final String? scope; // sent, received
  final int? page;
  final int? limit;

  const FetchConnectsParams({
    this.status,
    this.scope,
    this.page,
    this.limit,
  });

  @override
  List<Object?> get props => [status, scope, page, limit];
}
