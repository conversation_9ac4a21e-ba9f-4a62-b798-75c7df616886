{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983b7fff04f985dda5baea3066fc763c81", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dce9d0c4d6769eb4b9199a4fff27a0bc", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc451b90d1bf01bc7df0bef30430b567", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1837394cc9e536dade17608b1906585", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc451b90d1bf01bc7df0bef30430b567", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a7018d5bc006cee38d8e5ffcfddb7006", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98521e50899ec1f333767341d64708ecba", "guid": "bfdfe7dc352907fc980b868725387e9819dcfa6be0b6fb1a450b271204c6e8a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989be53b47685497d9954681f7b790d555", "guid": "bfdfe7dc352907fc980b868725387e98f2727fb2657f7c4938457f786f7dc764", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdbbd1304511dc7776691e1d356fd02a", "guid": "bfdfe7dc352907fc980b868725387e987a11cc03ffc4a8610b9da6559bf53251", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8bda4510238130e04b09ff12a204932", "guid": "bfdfe7dc352907fc980b868725387e98d4216c1ca26db8745dd7e527ff9ad23d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840fcc1d95b70e8fe2d02c2a6c6dcff2f", "guid": "bfdfe7dc352907fc980b868725387e98cf1b4ab124ccb9b315853781420d322f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7119b7af67b43cfaeb9ced66bc88732", "guid": "bfdfe7dc352907fc980b868725387e98c521e337dca7d7f3853f811354f0b07c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ac2fd4a2f5bc3f2d57b5beb1b86d314", "guid": "bfdfe7dc352907fc980b868725387e98a0e3f053fb201da95c6cda13c433653b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a63450705af08c7e4b7c473c69d1e2a1", "guid": "bfdfe7dc352907fc980b868725387e987530f0b4b6889970df3437e9f373a75f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78eceec94ff0ae2c0741b6f44cf7d4d", "guid": "bfdfe7dc352907fc980b868725387e981830c35cc5bebd54d7bed0738213ba51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985172a7066f37da849aed0d9ff5c9fee9", "guid": "bfdfe7dc352907fc980b868725387e98d24cf17a2aa9f31f19ff706c9b61f625", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a09d76f54f20f1ba7639aecd3eddbd3", "guid": "bfdfe7dc352907fc980b868725387e98f6e1bd5d3f4a5126698d42dda75cab81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ed0ed623c25bf958c0dad229891a79", "guid": "bfdfe7dc352907fc980b868725387e982a29100b49d6da536fe17aaa59da516f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac6aabdf0e78f7eaca26e8a07a1c9f11", "guid": "bfdfe7dc352907fc980b868725387e98dd4e65f33b775aeaa39403aa3f8206fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981421e673d540feed1b2f932b1d706907", "guid": "bfdfe7dc352907fc980b868725387e983d9309c9db0636d7ef68b0dc5d3c5026", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854d326b0dec5ff284f17cce7804b74ff", "guid": "bfdfe7dc352907fc980b868725387e9858f98df00775d6eef69116d3712bd141", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5bafc91ac55dab44bd176d078811272", "guid": "bfdfe7dc352907fc980b868725387e9857f38b5f2bf46cf0b9967a6e78c2a63b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e3c89cbc3a521b3f3d67d7827f037b8", "guid": "bfdfe7dc352907fc980b868725387e9879ba8e041656f14b5b723fa0945edad3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c0d8e6ca751744278c9d37a8ed3082e", "guid": "bfdfe7dc352907fc980b868725387e9813c3b7376534415ac26c376f6827e0ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5cb42bfc9b82938467d1fe549d520fd", "guid": "bfdfe7dc352907fc980b868725387e9871c5a5d761c4d50fef47ee90a3b1dbd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826fd6846022e05c0813cab8397e81ff1", "guid": "bfdfe7dc352907fc980b868725387e98a726f6896533adca9b2d1abc2a9f18b0", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc7e4206b6d487fcdc9e208a404d0867", "guid": "bfdfe7dc352907fc980b868725387e983cb3ca3a7071b9b0b8db532d8b320f26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884a6224eccdf4e69d1d01d29168da8bd", "guid": "bfdfe7dc352907fc980b868725387e98e5ec8b525e4968bba2b784812dc9584f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9873c5cdc675df6ee859230cd31688e7a3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9822551eb8d00fff13c647efbf81e10ba0", "guid": "bfdfe7dc352907fc980b868725387e987c88ade221d64ffedf5315c2e8e679a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874adf44779661bd3d77053b259addf92", "guid": "bfdfe7dc352907fc980b868725387e98b82a007d2018209f38da9c28de59de08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c8dc80fa826d55319445d0f815e4e60", "guid": "bfdfe7dc352907fc980b868725387e983b925a25eaa1aebdf2c4c589c312aace"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c53b36d01fb3a783274f397d631ccd76", "guid": "bfdfe7dc352907fc980b868725387e981d4c0c4cc5614c96850d50c8f53004c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c9cf3bc51ff88d77491631b201b3d9e", "guid": "bfdfe7dc352907fc980b868725387e980af4576239dff3fe519e5351d2e35ede"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a1e3b76f8b0749ef0d86a84e6e873b8", "guid": "bfdfe7dc352907fc980b868725387e98bb11c5930afece1c08859950c4c82b01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119d216d0a3144b4aef62d529fab714f", "guid": "bfdfe7dc352907fc980b868725387e9878a62380bbc0b47d7ed733df20bae3d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac6da904f782fff60ef081ca51ef4aef", "guid": "bfdfe7dc352907fc980b868725387e98644ccd5a579c442f87c4bf8af6971a78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ad324a2a4610d68fee814e8d401452e", "guid": "bfdfe7dc352907fc980b868725387e984a61088886ec9970493554a894ac3c34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ef848881d9afe2bb5650aa510eb0181", "guid": "bfdfe7dc352907fc980b868725387e9843f36ebfa6a628d85357bc2550f8b7b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e8c3ad508019e3287ffdc6a8fc63455", "guid": "bfdfe7dc352907fc980b868725387e98d008ff64a04b24592c45c435210e623f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98436c0fcea939a966507a738229b38e64", "guid": "bfdfe7dc352907fc980b868725387e98d0bdd5581aa9fd874412a5f3dec1c87c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5d0f98d502ba93b7bef296ee373278b", "guid": "bfdfe7dc352907fc980b868725387e98efbd4c154878da30adcf8135440684b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987190f2f167065ec642785541fdde488c", "guid": "bfdfe7dc352907fc980b868725387e98ac88b34334f3f91f098069358abdfc94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b866f4300b4e829b9b31921721cff99", "guid": "bfdfe7dc352907fc980b868725387e98c82c0b7a32e0082960dd020dd944f445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802bb6b61a5ae62ec91f932f6efd3ebc8", "guid": "bfdfe7dc352907fc980b868725387e98ea82cd007a8710952ec64a999da4f5ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98230b8f1e639af07a138da45642c747cb", "guid": "bfdfe7dc352907fc980b868725387e986d70d626aa5ab15e8a6e03fb9afcd33e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc8928b86831e9ede70ad675242c2aa", "guid": "bfdfe7dc352907fc980b868725387e98929088d5aadb5a6b62a978a6710170f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bdb61fbc879d64e0f415f0b2ce3ed9c", "guid": "bfdfe7dc352907fc980b868725387e98cafab0ee1cf797846d96f348a39074ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d65e9e29f59f5f7a08bb8fc64d15cdb4", "guid": "bfdfe7dc352907fc980b868725387e980d3ae2a0294e878e3c58b0db6a971680"}], "guid": "bfdfe7dc352907fc980b868725387e98d4583f030bbbcbc3431a8455d60ce053", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e985047ebb270c273bafe6d37477511e889"}], "guid": "bfdfe7dc352907fc980b868725387e98857f289a73c9b4bd1911b5a54d771a6e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9806494d2ae642847f2ae708225ea5004e", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e985b5a458c7485e5811b80a82cb2d9e0fb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}