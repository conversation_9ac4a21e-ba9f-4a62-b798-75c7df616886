import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/core/utils/format_date_in_month.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_payment_bloc.dart';
import 'package:cbrs/features/merchant_payment/presentation/widgets/qr_currency_input_widget.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class MerchantPaymentByQrAddAmountPage extends StatefulWidget {
  const MerchantPaymentByQrAddAmountPage({
    required this.merchantData,
    super.key,
  });
  final Map<String, dynamic> merchantData;

  @override
  State<MerchantPaymentByQrAddAmountPage> createState() =>
      _MerchantPaymentByQrAddAmountPageState();
}

class _MerchantPaymentByQrAddAmountPageState
    extends State<MerchantPaymentByQrAddAmountPage> {
  late CurrencyInputController _currencyController;
  final TextEditingController _pinController = TextEditingController();
  bool _isLoading = false;
  String _senderName = '';
  late TransactionBottomSheetsManager _bottomSheetsManager;
  bool _isAmountFromQr = false;

  @override
  void initState() {
    super.initState();
    // Fetch latest user details to get updated wallet balance

    _currencyController = CurrencyInputController(
      currencyType: CurrencyType.etb,
      maxBalance: 0,
    );

    // Set initial amount if provided in QR code
    final qrAmount = widget.merchantData['amount'] as String?;
    if (qrAmount != null && qrAmount.isNotEmpty) {
      try {
        final initialAmount = double.parse(qrAmount);
        _currencyController.setAmount(initialAmount);
        _isAmountFromQr = true; // Mark that amount is from QR code
      } catch (e) {
        debugPrint('Error parsing amount from QR: $e');
      }
    }

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.merchantPayment,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        context.read<TransactionBloc>().add(
              ConfirmTransferEvent(
                pin: pin,
                billRefNo: (context.read<MerchantPaymentBloc>().state
                        as WalletTransferPinRequired)
                    .billRefNo,
                transactionType: tx_type.TransactionType.merchantPayment,
              ),
            );
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Recreate controller with updated balance when user state changes
    final balance = 0.0;
    if (_currencyController.maxBalance != balance) {
      // Save current amount before recreating controller
      final currentAmount = _currencyController.numericAmount;

      _currencyController = CurrencyInputController(
        currencyType: CurrencyType.etb,
        maxBalance: balance,
      );

      // Restore amount if it was set
      if (currentAmount > 0) {
        _currencyController.setAmount(currentAmount);
      } else {
        // Try to set initial amount from QR if present
        final qrAmount = widget.merchantData['amount'] as String?;
        if (qrAmount != null && qrAmount.isNotEmpty) {
          try {
            final initialAmount = double.parse(qrAmount);
            _currencyController.setAmount(initialAmount);
            _isAmountFromQr = true; // Mark that amount is from QR code
          } catch (e) {
            debugPrint('Error parsing amount from QR: $e');
          }
        }
      }
    }
  }

  void _showConfirmScreenBottomSheet(WalletTransferResponse response) {
    final merchantName = widget.merchantData['merchantName'] as String;
    final merchantTill = widget.merchantData['merchantTill'] as String?;

    final calculatedTotalAmount = response.data.billAmount +
        response.data.vat +
        response.data.serviceCharge;

    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'transactionType': 'merchant_payment',
        'merchantName': merchantName,
        'merchantCode': merchantTill ?? '',
        'senderName': _senderName,
        'amount': response.data.billAmount,
        'originalCurrency': response.data.originalCurrency,
        'serviceCharge': response.data.serviceCharge,
        'VAT': response.data.vat,
        'Date': formatDateWithTime(response.data.createdAt),
        'totalAmount': calculatedTotalAmount,
        'Payment Reference': response.data.billRefNo,
        // 'status': response.data.status,
      },
      originalCurrency: response.data.originalCurrency,
      totalAmount: response.data.totalAmount,
      billAmount: response.data.billAmount,
      confirmButtonText: 'Confirm Payment',
      requiresOtp: (context.read<MerchantPaymentBloc>().state
              as WalletTransferPinRequired)
          .requiresOtp,
      billRefNo: response.data.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    final merchantName = widget.merchantData['merchantName'] as String;
    final merchantTill = widget.merchantData['merchantTill'] as String?;

    final calculatedTotalAmount =
        transaction.billAmount + transaction.serviceCharge + transaction.vat;

    _bottomSheetsManager.showSuccessScreenBottomSheet(
      originalCurrency: transaction.originalCurrency,
      totalAmount: transaction.totalAmount,
      billAmount: transaction.billAmount,
      transactionId: transaction.id,
      billRefNo: transaction.billRefNo,
      {
        'totalAmount': transaction.billAmount,
        'originalCurrency': transaction.originalCurrency,
        'merchantName': merchantName,
        'merchantCode': merchantTill ?? '',
        'senderName': _senderName,
        'Payment Reference': transaction.billRefNo,
        'Date': formatDateWithTime(transaction.createdAt.toString()),
        'serviceCharge': transaction.serviceCharge,
        'VAT': transaction.vat,
        'amount': transaction.billAmount,
        // 'status': 'Paid',
      },
      status: 'Paid',
    );
  }

  void _onContinuePressed() {
    if (_isLoading) return;

    try {
      final amount = _currencyController.numericAmount;

      if (_isAmountFromQr) {
        // Proceed with payment
        context.read<MerchantPaymentBloc>().add(
              CheckMerchantPaymentRulesRequested(
                amount: _currencyController.numericAmount,
                currency: 'ETB',
              ),
            );
        return;
      }

      // Regular validations for manual amount entry
      if (amount <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Amount must be greater than 0',
              style: GoogleFonts.outfit(),
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Check transfer rules before proceeding
      context.read<MerchantPaymentBloc>().add(
            CheckMerchantPaymentRulesRequested(
              amount: amount,
              currency: 'ETB',
            ),
          );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Invalid amount format',
            style: GoogleFonts.outfit(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.sizeOf(context);
    final userState = context.read<HomeBloc>().state;
    if (userState is HomeProfileLoadedState) {
      _senderName = userState.localUser.fullName;
    }

    // Validate merchant data
    final merchantName = widget.merchantData['merchantName'] as String?;
    final merchantId = widget.merchantData['merchantId'] as String?;
    final merchantTill = widget.merchantData['merchantTill'] as String?;

    if (merchantName == null || merchantId == null) {
      return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => context.pop(),
          ),
          title: Text(
            'Error',
            style: GoogleFonts.outfit(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: Center(
          child: Text(
            'Invalid merchant data',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              color: Colors.red,
            ),
          ),
        ),
      );
    }

    return BlocConsumer<MerchantPaymentBloc, MerchantPaymentState>(
      listenWhen: (previous, current) =>
          current is CheckingMerchantPaymentRules ||
          current is MerchantPaymentRulesChecked ||
          current is WalletTransferLoading ||
          current is WalletTransferPinRequired ||
          current is WalletTransferFailure ||
          current is WalletTransferError,
      listener: (context, state) {
        if (state is CheckingMerchantPaymentRules ||
            state is WalletTransferLoading) {
          setState(() => _isLoading = true);
        } else {
          setState(() => _isLoading = false);
        }

        if (state is MerchantPaymentRulesChecked) {
          // Initiate merchant payment after rules are checked
          context.read<MerchantPaymentBloc>().add(
                MerchantPaymentEventt(
                  beneficiaryId: merchantId,
                  amount: _currencyController.numericAmount,
                  currency: 'ETB',
                ),
              );
        } else if (state is WalletTransferPinRequired) {
          // Get the response from the bloc
          final merchantBloc = context.read<MerchantPaymentBloc>();
          if (merchantBloc.lastResponse != null) {
            _showConfirmScreenBottomSheet(merchantBloc.lastResponse!);
          }
        } else if (state is WalletTransferFailure ||
            state is WalletTransferError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state is WalletTransferFailure
                    ? state.message
                    : (state as WalletTransferError).message,
                style: GoogleFonts.outfit(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      buildWhen: (previous, current) =>
          current is WalletTransferInitial ||
          current is WalletTransferLoading ||
          current is WalletDetailsLoaded,
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black),
              onPressed: () => context.pop(),
            ),
            title: Text(
              'Merchant Payment',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          body: SafeArea(
            child: BlocListener<TransactionBloc, TransactionState>(
              listenWhen: (previous, current) =>
                  current is ConfirmTransferSuccess ||
                  current is ConfirmTransferError,
              listener: (context, state) {
                if (state is ConfirmTransferError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        state.message,
                        style: GoogleFonts.outfit(),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
                if (state is ConfirmTransferSuccess) {
                  _showSuccessScreenBottomSheet(state.transaction);
                }
              },
              child: BlocConsumer<WalletBalanceBloc, HomeState>(
                listener: (context, state) {
                  // Handle state changes if needed
                },
                builder: (context, state) {
                  double balance = 0;
                  if (state is WalletLoadedState) {
                    balance =
                        state.isUsdWallet ? state.usdBalance : state.etbBalance;
                  }
                  return BlocProvider(
                    create: (context) => GetIt.I<TransactionBloc>(),
                    child: QrCurrencyInputWidget(
                      alwaysShowETBColor: true,
                      controller: _currencyController,
                      title: _isAmountFromQr ? 'Check Amount' : 'Add Amount',
                      subtitle: _isAmountFromQr
                          ? "Review the amount scanned from the merchant's QR code, along with the merchant details."
                          : 'Enter the amount you want to pay to $merchantName.',
                      balanceLabel: CurrencyFormatter.formatWalletBalance(
                        balance,
                        'ETB',
                      ),
                      onContinue: _onContinuePressed,
                      isLoading: _isLoading,
                      transactionType: 'merchant_payment',
                      merchantId: merchantId,
                      merchantName: merchantName,
                      merchantTill: merchantTill,
                      isAmountFromQr: _isAmountFromQr,
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
