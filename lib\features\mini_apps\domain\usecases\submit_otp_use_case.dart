import 'package:cbrs/core/utils/typedef.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:cbrs/core/error/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:cbrs/features/mini_apps/domain/repositories/miniapp_repository.dart';

class SubmitOtpMiniappUseCase
    extends UsecaseWithParams<MiniappEntity, MiniappOtpParams> {
  final MiniappRepository _repository;

  const SubmitOtpMiniappUseCase(this._repository);

  @override
  ResultFuture<MiniappEntity> call(MiniappOtpParams params) async {
    return _repository.submitOtp(
        transactionType: params.transactionType,
        otp: params.otp,
        billRefNo: params.billRefNo


    );
  }
}

class MiniappOtpParams extends Equatable {
  final String transactionType;
  final String otp;
  final String billRefNo;

  const MiniappOtpParams({
    required this.transactionType,
    required this.otp,
    required this.billRefNo,
  });

  @override
  List<Object> get props => [transactionType, otp, billRefNo];
}
