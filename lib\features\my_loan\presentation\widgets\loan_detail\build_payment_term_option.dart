import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';

class BuildPaymentTermOption extends StatefulWidget {
  const BuildPaymentTermOption({
    required this.selectedTerm,
    required this.onTermSelected,
    super.key,
  });
  final int selectedTerm;
  final Function(int) onTermSelected;

  @override
  State<BuildPaymentTermOption> createState() => _BuildPaymentTermOptionState();
}

class _BuildPaymentTermOptionState extends State<BuildPaymentTermOption> {
  @override
  void initState() {
    setState(() {
      selectedOption = widget.selectedTerm;
    });
  }

  int selectedOption = 0;
  void handleSelection(int term) {
    setState(() {
      selectedOption = term;
    });
    // widget.onTermSelected(term);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildBottomSheetHeader(context),
        SizedBox(
          height: 16.h,
        ),
        CustomBuildText(
          text: 'Select a repayment period from the lists',
          color: Colors.black.withOpacity(0.3),
          fontSize: 14.sp,
        ),
        SizedBox(
          height: 16.h,
        ),
        _buildPaymentRow(
          context,
          iconString: MediaRes.solarWallet,
          onTap: () async {
            handleSelection(1);
          },
          paymentText: '1 Month',
          isSelected: selectedOption == 1,
        ),
        SizedBox(
          height: 8.h,
        ),
        _buildPaymentRow(
          context,
          iconString: MediaRes.solarAgent,
          onTap: () async {
            handleSelection(3);
          },
          paymentText: '3 Months',
          isSelected: selectedOption == 3,
        ),
        SizedBox(
          height: 8.h,
        ),
        _buildPaymentRow(
          context,
          iconString: MediaRes.solarAgent,
          onTap: () async {
            handleSelection(6);
          },
          paymentText: '6 Months',
          isSelected: selectedOption == 6,
        ),
        const SizedBox(
          height: 30,
        ),
        _buildPaymentAction(context, selectedOption),
      ],
    );
  }

  Widget _buildBottomSheetHeader(BuildContext context) {
    return Container(
      // color: Colors.green,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomBuildText(
            text: 'Choose Repayment Period',
            fontWeight: FontWeight.w700,
            fontSize: 16.sp,
          ),
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Icon(
              Icons.close,
              color: Colors.black,
              size: 20.h,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentRow(
    BuildContext context, {
    required String iconString,
    required String paymentText,
    required VoidCallback onTap,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        // height: 56.h,
        padding: EdgeInsets.all(16.h),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: const Color(0x0A2C2B34),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: Colors.grey[300]!),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Container(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 20.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected
                            ? Theme.of(context).primaryColor.withOpacity(0.8)
                            : Colors.white,
                        border: Border.all(color: Colors.grey.shade500),
                      ),
                      child: isSelected
                          ? Icon(Icons.check, color: Colors.white, size: 15.h)
                          : null,
                    ),
                    SizedBox(width: 10.w),
                    CustomBuildText(
                      text: paymentText,
                      color: Theme.of(context).primaryColor.withOpacity(0.8),
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 10),
            Container(
              clipBehavior: Clip.antiAlias,
              decoration: const BoxDecoration(),
              child: Icon(
                Icons.arrow_forward_ios,
                size: 16.h,
                color: Theme.of(context).primaryColor.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentAction(BuildContext context, int selectedOption) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: const CustomBuildText(
              text: 'Cancel',
              color: Color(0xFF595959),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: () async {
              widget.onTermSelected(selectedOption);
              Navigator.pop(context);
              // TODO: Filter transactions based on selected date range
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor.withOpacity(0.8),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const CustomBuildText(
              text: 'Done',
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
